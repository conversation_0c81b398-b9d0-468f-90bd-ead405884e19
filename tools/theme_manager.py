from itertools import cycle, islice
from pathlib import Path
from typing import TypeVar

import yaml
from box import Box

# --------------------------------------------------------------------------
# 1. 辅助函数 (保持独立或作为私有静态方法)
# --------------------------------------------------------------------------


def _deep_merge(source: dict, destination: dict) -> dict:
    """递归合并字典，source的值会覆盖destination的值"""
    for key, value in source.items():
        if isinstance(value, dict) and key in destination and isinstance(destination[key], dict):
            _deep_merge(value, destination[key])
        else:
            destination[key] = value
    return destination


# --------------------------------------------------------------------------
# 2. 带有工厂类方法的自定义配置类
# --------------------------------------------------------------------------

# 定义一个泛型类型变量，用于类型提示，确保 load 方法返回正确的类实例
T_ThemeConfig = TypeVar("T_ThemeConfig", bound="ThemeConfig")


class ThemeConfig(Box):
    """
    一个包含自定义辅助方法的智能配置类。
    """

    def __init__(self, *args, **kwargs):
        kwargs.setdefault("default_box", True)
        super().__init__(*args, **kwargs)

    @property
    def name(self) -> str:
        return self.name

    @classmethod
    def load(cls: type[T_ThemeConfig], config_path: str = "config/config.yaml") -> T_ThemeConfig:
        """
        工厂类方法：加载并合并当前激活的主题配置，然后返回一个类实例。

        Args:
            cls: 类本身，由 @classmethod 自动传入。
            config_path: 主配置文件的路径。

        Returns:
            一个 `ThemeConfig` (或其子类) 的实例。
        """
        config_file = Path(config_path)
        base_dir = config_file.parent.parent

        # 1. 加载主配置文件
        with open(config_file, encoding="utf-8") as f:
            main_config = yaml.safe_load(f)

        active_theme_name = main_config.get("theme", "default")
        cls.name = active_theme_name
        themes_dir = base_dir / "themes"

        # 2. 加载默认主题配置
        default_theme_path = themes_dir / "_defaults.yml"
        if not default_theme_path.exists():
            raise FileNotFoundError(f"'_defaults.yml' not found in '{themes_dir}' directory!")

        with open(default_theme_path, encoding="utf-8") as f:
            theme_dict = yaml.safe_load(f) or {}

        # 3. 加载并合并激活的主题配置
        active_theme_path = themes_dir / f"{active_theme_name}.yml"
        if active_theme_path.exists():
            with open(active_theme_path, encoding="utf-8") as f:
                active_theme_data = yaml.safe_load(f) or {}

            # 使用递归合并
            theme_dict = _deep_merge(active_theme_data, theme_dict)
        else:
            print(f"Warning: Theme '{active_theme_name}.yml' not found. Using defaults only.")

        # 4. 使用 `cls` 来实例化，而不是硬编码 ThemeConfig
        # 这使得该方法在子类中也能正确工作
        return cls(theme_dict)

    def get_cyclic_colors(self, count: int = None) -> list[str]:
        """
        获取循环使用的颜色序列。
        """
        # 后备默认值
        default_colors = ["#2196F3", "#4CAF50", "#FFC107", "#F44336", "#9C27B0"]

        colors = self.colors.get("cyclic_colors", default_colors)
        if not colors:
            colors = default_colors

        if count is None:
            return list(colors)

        color_cycler = cycle(colors)
        return list(islice(color_cycler, count))


theme = ThemeConfig.load()

# --------------------------------------------------------------------------
# 3. 可运行的演示示例
# --------------------------------------------------------------------------
if __name__ == "__main__":
    theme = ThemeConfig.load(config_path="config/config.yaml")

    # --- 结果完全一样，但调用方式更优雅 ---
    print("--- 点属性访问 ---")
    print(f"主颜色: {theme.colors.primary}")
    print(f"H1 字号: {theme.typography.sizes.h1}")

    print("\n--- 自定义方法 get_cyclic_colors ---")
    print("获取2种颜色:", theme.get_cyclic_colors(2))
    print("获取所有颜色:", theme.get_cyclic_colors())
    print("获取5种颜色 (循环):", theme.get_cyclic_colors(5))

    # 验证返回的对象确实是 ThemeConfig 类型
    print(f"\n对象类型是: {type(theme)}")
