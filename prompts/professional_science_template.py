import os
import sys
import datetime
import logging

sys.path.append(os.getcwd())

from manim import *
from manim.constants import DEFAULT_WAIT_TIME

from dsl.v2.animation_functions import *
from dsl.v2.core.scene import FeynmanScene


class ProfessionalScienceTemplate(FeynmanScene):
    """
    世界级专业理科原理讲解模板（增强版）

    新增功能：
    - 连贯性动画支持：内容在原有基础上变化，而不是完全替换
    - 状态管理系统：跨步骤信息存储和状态追踪
    - 增量更新机制：支持渐进式内容变化
    - 动画序列管理：复杂多步动画的协调
    - 智能自动适配：所有区域内容自动缩放以适应布局空间
    - 时间戳记录功能：记录动画状态变化的时间点，用于语音同步

    布局结构：
    ┌─────────────────┬─────────────────┐
    │  标题区域       │   步骤介绍区域   │  <- 各占顶部10%（自动适配）
    │  (TOP_LEFT)     │   (TOP_RIGHT)   │
    ├────┬─────────────────────────┬────┤
    │辅助│                          |辅助│
    │区域│      主内容区域           │区域│  <- 左15%，中60%，右15%（自动适配）
    │LEFT│      (CENTER)           │RIGH│
    │    │                         │T   │
    │    │                         │    │
    ├────┴─────────────────────────┴────┤
    │           结果区域                │  <- 底部10%（自动适配）
    │           (DOWN)                  │
    └───────────────────────────────────┘
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # 初始化时间戳记录功能
        self._init_timestamp_logging()
        # 设置动画播放时长
        self.play_run_time = 1.0
        # 初始化动画时间追踪
        self._init_animation_time_tracking()

        # 区域元素记录字典
        self.region_elements = {
            "title": VGroup(),
            "step": VGroup(),
            "main": VGroup(),
            "aux": VGroup(),
            "left_auxiliary": VGroup(),
            "right_auxiliary": VGroup(),
            "result": VGroup(),
        }

        # 专业色彩系统 - 黑色背景下的亮色配色
        self.colors = {
            "background": "#195A56",  # 背景颜色
            "text_primary": "#FED3A8",  # 文字颜色
            "text_secondary": "#9D1E31",  # 次要文字颜色
            "grid": "#5AA7A7",  # 网格线颜色
            "highlight": "#96D7C6",  # 高亮颜色
        }

        # 字体大小系统 - 层次分明的视觉层级
        self.font_sizes = {
            "title": 36,  # 标题字体 - 增大
            "step": 32,  # 步骤介绍字体 - 增大
            "main_content": 36,  # 主内容字体
            "auxiliary": 26,  # 辅助信息字体 - 增大
            "auxiliary_detail": 26,  # 辅助详细信息字体 - 增大
            "result": 32,  # 结果字体
            "formula": 32,  # 公式字体
            "annotation": 16,  # 注释字体
        }

        # 字体种类配置
        self.font_types = {
            "title": "Microsoft YaHei",
            "step": "Microsoft YaHei",
            "main_content": "Microsoft YaHei",
            "left_auxiliary": "Microsoft YaHei",
            "right_auxiliary": "Microsoft YaHei",
            "result": "Microsoft YaHei",
        }

        # 区域位置配置 - 精确的布局控制
        self.regions = {
            "title_width": 6,  # 标题区域宽度
            "title_height": 2,  # 标题区域高度
            "step_width": 8,  # 步骤区域宽度
            "step_height": 1,  # 步骤区域高度
            "main_width": 10.0,  # 主内容区域宽度（增大）
            "main_height": 5.0,  # 主内容区域高度（增大）
            "auxiliary_width": 4.0,  # 辅助区域宽度（减小）
            "auxiliary_height": 5.0,  # 辅助区域高度
            "result_width": 10.0,  # 结果区域宽度
            "result_height": 1.5,  # 结果区域高度
        }

        # 是否显示开发备注
        self.show_debug_labels = False

        # 自动缩放配置
        self.auto_scale_config = {
            "min_scale_factor": 0.3,  # 最小缩放比例
            "scale_step": 0.1,  # 缩放步长
            "padding_ratio": 0.95,  # 内容占区域的比例（留5%边距）
            "min_font_size": 18,  # 最小字体大小（确保可读性）
        }

        # 连贯性动画配置
        self.continuity_config = {
            "transition_duration": 1.0,  # 过渡动画持续时间
            "highlight_duration": 0.5,  # 高亮动画持续时间
            "fade_duration": 0.3,  # 淡入淡出持续时间
            "morph_duration": 1.5,  # 形变动画持续时间
            "pause_between_steps": 0.5,  # 步骤间暂停时间
            "enable_smooth_transitions": True,  # 启用平滑过渡
            "keep_main_elements": True,  # 保持主要元素
            "auto_highlight_changes": True,  # 自动高亮变化部分
        }

        # 内容建议指南 - 确保最佳视觉效果
        self.content_guidelines = {
            "title": {"max_chars": 8, "description": "标题建议8个字以内"},
            "step": {"max_chars": 12, "description": "步骤建议12个字以内"},
            "auxiliary_title": {"max_chars": 6, "description": "辅助标题建议6个字以内"},
            "auxiliary_items": {
                "max_items": 5,
                "max_chars_per_item": 15,
                "description": "辅助项目建议5项以内，每项15字以内",
            },
            "result": {"max_chars": 40, "description": "结果描述建议40个字以内"},
        }

    def construct(self):
        """场景构建主函数 - 子类应该重写此方法"""
        # 设置背景和网格
        self.setup_background()

    def setup_background(self):
        """设置专业背景（纯色背景）"""
        # 设置纯色背景
        self.camera.background_color = self.colors["background"]

    def _init_timestamp_logging(self):
        """
        初始化时间戳记录功能
        为语音同步创建专用的时间戳日志文件
        智能检测概念目录并保存到相应位置
        """
        # 智能检测输出目录
        log_dir = self._detect_concept_output_directory()
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建固定名称的时间戳日志文件
        self.timestamp_log_file = f"{log_dir}/animation_timestamp.log"
        
        # 如果文件已存在，先删除
        if os.path.exists(self.timestamp_log_file):
            try:
                os.remove(self.timestamp_log_file)
                print(f"🗑️ 已删除现有时间戳日志文件: {self.timestamp_log_file}")
            except Exception as e:
                print(f"⚠️ 删除现有时间戳日志文件时出错: {e}")
        
        # 配置专用的时间戳记录器
        self.timestamp_logger = logging.getLogger("timestamp_logger")
        self.timestamp_logger.setLevel(logging.INFO)
        
        # 清除现有的handlers
        if self.timestamp_logger.handlers:
            for handler in self.timestamp_logger.handlers:
                self.timestamp_logger.removeHandler(handler)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(self.timestamp_log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 设置时间戳格式
        formatter = logging.Formatter('%(message)s')
        file_handler.setFormatter(formatter)
        
        self.timestamp_logger.addHandler(file_handler)
            

    def _detect_concept_output_directory(self):
        """
        智能检测概念输出目录
        
        检测逻辑：
        1. 检查是否存在 output/concept_* 目录
        2. 如果存在，返回该概念目录
        3. 如果不存在，返回默认的 logs 目录
        
        Returns:
            str: 检测到的日志目录路径
        """
        # 检查 output 目录下是否有 concept_ 开头的目录
        output_dir = "output"
        if os.path.exists(output_dir):
            try:
                # 查找所有 concept_ 开头的目录
                concept_dirs = [
                    d for d in os.listdir(output_dir) 
                    if d.startswith("concept_") and os.path.isdir(os.path.join(output_dir, d))
                ]
                
                if concept_dirs:
                    # 如果有多个，选择最新的（按修改时间）
                    latest_concept_dir = max(
                        concept_dirs,
                        key=lambda d: os.path.getmtime(os.path.join(output_dir, d))
                    )
                    concept_path = os.path.join(output_dir, latest_concept_dir)
                    print(f"🎯 检测到概念目录: {concept_path}")
                    return concept_path
                    
            except Exception as e:
                print(f"⚠️  检测概念目录时出错: {e}")
        
        # 如果没有检测到概念目录，使用默认的 logs 目录
        print(f"📁 使用默认日志目录: logs")
        return "logs"
    
    def _init_animation_time_tracking(self):
        """
        初始化动画时间追踪功能
        用于准确记录每个阶段在整个动画中的实际时间位置
        """
        # 累计动画时间（秒）
        self._cumulative_time = 0.0
        # 记录开始时间
        self._animation_start_time = datetime.datetime.now()
        
        print(f"⏱️ 动画时间追踪已初始化")
    
    def update_animation_time(self, duration=None, wait_flag=False):
        """
        更新累计动画时间
        
        Args:
            duration (float): 动画持续时间（秒），如果不提供则使用默认值
        """
        
        start_time = self._cumulative_time
        self._cumulative_time += duration
        wait_str = "等待" if wait_flag else "动画"
        
        # 记录到日志
        self.timestamp_logger.info(f"#{wait_str}: 开始: {start_time:.2f}s, 结束: {self._cumulative_time:.2f}s")
    
    def get_current_animation_time(self):
        """
        获取当前累计动画时间
        
        Returns:
            float: 累计动画时间（秒）
        """
        return self._cumulative_time
    
    def play(self, *animations, **kwargs):
        """
        重写play方法，自动追踪动画时间
        """

        run_time = kwargs.get('run_time', self.play_run_time)
        kwargs['run_time'] = run_time

        # 执行动画
        result = super().play(*animations, **kwargs)
        
        # 更新累计时间
        self.update_animation_time(run_time, wait_flag=kwargs.get('wait_flag', False))
        
        return result

    def wait(self, duration):
        # 创建一个完全不可见的虚拟动画来占用时间
        invisible_dot = Dot(radius=0.0001, fill_opacity=0, stroke_opacity=0)
        invisible_dot.move_to(ORIGIN)
        
        # 添加到场景（完全不可见）
        self.add(invisible_dot)
        
        # 执行一个持续指定时间的虚拟动画
        self.play(
            invisible_dot.animate.shift(RIGHT * 0.0001),
            run_time=duration,
            wait_flag = True
        )
        
        # 移除虚拟对象
        self.remove(invisible_dot)
        

    def log_animation_timestamp(self, state_description):
        """
        记录动画状态的时间戳到日志文件中
        
        功能说明：
        - 记录实际动画播放时间和状态描述
        - 生成标准格式的日志条目，便于后续语音同步处理
        - 支持中文状态描述，便于语音合成
        
        Args:
            state_description (str): 当前动画状态的描述，如"显示标题"、"开始主要内容动画"等
            
        日志格式：
            [动画时间: Xs] 状态描述
            例如：[动画时间: 3.5s] 阶段记录：显示标题区域
            
        使用示例：
            # 在动画的关键时刻调用
            self.log_animation_timestamp("标题淡入开始")
            title = self.create_title_region_content("函数原理")
            self.play(Write(title))
            self.log_animation_timestamp("标题淡入完成")
            
            self.log_animation_timestamp("主内容显示开始")
            main_content = self.create_main_region_content(my_diagram)
            self.play(FadeIn(main_content))
            self.log_animation_timestamp("主内容显示完成")
        """
        # 获取动画实际播放时间
        try:
            # 优先使用累计时间追踪
            animation_time = self.get_current_animation_time()
            animation_time_str = f"{animation_time:.2f}s"
        except:
            # 备选方案：通过renderer获取当前动画时间线时间
            try:
                animation_time = getattr(self.renderer, 'time', 0.0) if hasattr(self, 'renderer') else 0.0
                animation_time_str = f"{animation_time:.2f}s"
            except:
                animation_time_str = "0.00s"
        
        # 格式化日志消息（只包含动画时间）
        log_message = f"[动画时间: {animation_time_str}] {state_description}"
        
        # 写入时间戳日志
        self.timestamp_logger.info(log_message)
        
        # 同时在控制台输出（便于实时调试）
        print(f"🎬 动画时间戳: {log_message}")
        
        # 如果是阶段记录，添加分隔线
        if "阶段记录" in state_description:
            separator = "=" * 50
            self.timestamp_logger.info(separator)
            print(f"🎬 {separator}")

    # === 自动缩放功能 ===

    def auto_fit_content(self, content, max_width, max_height, target_position=ORIGIN):
        """
        自动调整内容大小以适应指定区域（只缩小，不放大）

        Args:
            content: 要调整的内容对象
            max_width: 最大宽度
            max_height: 最大高度
            target_position: 目标位置

        Returns:
            调整后的内容对象
        """
        if not hasattr(content, "width") or not hasattr(content, "height"):
            return content

        # 获取内容的实际尺寸
        content_width = content.width
        content_height = content.height

        # 如果内容尺寸为0，直接返回
        if content_width == 0 or content_height == 0:
            content.move_to(target_position)
            return content

        # 计算可用空间（留出边距）
        available_width = max_width * self.auto_scale_config["padding_ratio"]
        available_height = max_height * self.auto_scale_config["padding_ratio"]

        # 计算当前内容占区域的比例
        width_ratio = content_width / max_width
        height_ratio = content_height / max_height
        current_ratio = max(width_ratio, height_ratio)

        # 判断是否需要缩小
        scale_factor = 1.0

        if current_ratio > self.auto_scale_config["padding_ratio"]:
            # 内容太大，需要缩小
            scale_x = available_width / content_width
            scale_y = available_height / content_height
            scale_factor = min(scale_x, scale_y)
            # 应用最小缩放限制
            scale_factor = max(scale_factor, self.auto_scale_config["min_scale_factor"])

        # 应用缩放（只在需要缩小时）
        if scale_factor < 1.0 and abs(scale_factor - 1.0) > 0.01:
            content.scale(scale_factor)

        # 移动到目标位置
        content.move_to(target_position)

        return content

    def auto_fit_text_content(self, text_content, max_width, max_height, target_position=ORIGIN):
        """
        自动调整文本内容大小以适应指定区域（只缩小，不放大）
        对于文本，优先调整字体大小而不是整体缩放

        Args:
            text_content: 文本内容对象
            max_width: 最大宽度
            max_height: 最大高度
            target_position: 目标位置

        Returns:
            调整后的文本内容对象
        """
        if not hasattr(text_content, "width") or not hasattr(text_content, "height"):
            return text_content

        # 如果内容尺寸为0，直接返回
        if text_content.width == 0 or text_content.height == 0:
            text_content.move_to(target_position)
            return text_content

        # 获取可用空间
        available_width = max_width * self.auto_scale_config["padding_ratio"]
        available_height = max_height * self.auto_scale_config["padding_ratio"]

        # 计算当前内容占区域的比例
        width_ratio = text_content.width / max_width
        height_ratio = text_content.height / max_height
        current_ratio = max(width_ratio, height_ratio)

        if current_ratio > self.auto_scale_config["padding_ratio"]:
            # 内容太大，需要缩小
            # 优先减小字体大小
            iteration = 0
            max_iterations = 15  # 防止无限循环

            while (
                text_content.width > available_width or text_content.height > available_height
            ) and iteration < max_iterations:
                current_font_size = self.get_text_font_size(text_content)
                new_font_size = max(current_font_size * 0.92, self.auto_scale_config["min_font_size"])

                self.set_text_font_size(text_content, new_font_size)
                iteration += 1

                # 如果字体已经达到最小值，退出循环
                if new_font_size <= self.auto_scale_config["min_font_size"]:
                    break

            # 如果仍然超出范围，使用整体缩放作为后备方案
            if text_content.width > available_width or text_content.height > available_height:
                scale_x = available_width / text_content.width
                scale_y = available_height / text_content.height
                scale_factor = min(scale_x, scale_y)
                scale_factor = max(scale_factor, self.auto_scale_config["min_scale_factor"])

                if scale_factor < 1.0:
                    text_content.scale(scale_factor)

        # 移动到目标位置
        text_content.move_to(target_position)

        return text_content

    def get_text_font_size(self, text_content):
        """获取文本内容的字体大小"""
        if hasattr(text_content, "font_size"):
            return text_content.font_size
        elif isinstance(text_content, VGroup) and len(text_content.submobjects) > 0:
            # 对于VGroup，获取第一个有font_size属性的子对象的字体大小
            for submob in text_content.submobjects:
                if hasattr(submob, "font_size"):
                    return submob.font_size
        return 12  # 默认字体大小

    def set_text_font_size(self, text_content, font_size):
        """设置文本内容的字体大小"""
        if hasattr(text_content, "font_size"):
            text_content.font_size = font_size

        # 如果是VGroup，递归处理所有子对象
        if isinstance(text_content, VGroup):
            for submob in text_content.submobjects:
                if hasattr(submob, "font_size"):
                    submob.font_size = font_size
                elif isinstance(submob, VGroup):
                    self.set_text_font_size(submob, font_size)

    # === 标准化区域内容创建接口 ===

    def create_title_region_content(self, title_text):
        """
        创建标题区域内容的标准接口（自动适配区域大小）

        功能说明：
        - 自动定位到左上角标题区域（6×2）
        - 内容自动缩放适应区域，保持15%边距
        - 使用专业主题色

        内容建议：
        - 标题长度：建议8个字以内（如"数学原理"、"物理定律"）
        - 避免过长：如"机器学习中的深度神经网络算法详解"
        - 自动适配：过长标题会自动缩小字体以适应区域

        Args:
            title_text (str): 标题文字

        Returns:
            VGroup: 定位到标题区域的内容组，包含自动适配的文字

        使用示例：
            title_group = self.create_title_region_content("函数原理")
            self.play(Write(title_group))

            # 长标题会自动缩放适应区域
            title = self.create_title_region_content("复杂机器学习算法详细演示")
        """
        title_content = Text(
            title_text,
            font_size=self.font_sizes["title"],
            color=self.colors["text_primary"],
            font=self.font_types["title"],
            weight=BOLD,
        ).set_stroke(color = self.colors["text_primary"], width=2)

        # 标题区域位置（左上角）
        title_position = UP * 3.5 + LEFT * 4

        # 自动适配内容大小
        title_content = self.auto_fit_text_content(
            title_content,
            max_width=self.regions["title_width"],
            max_height=self.regions["title_height"],
            target_position=title_position,
        )

        title_content.to_edge(UL)

        title_group = VGroup(title_content)
        return title_group

    def create_step_region_content(self, step_text):
        """
        创建步骤区域内容的标准接口（自动适配区域大小）

        功能说明：
        - 自动定位到右上角步骤区域（8×1.5）
        - 内容自动缩放适应区域，保持15%边距
        - 使用主题色，与标题区域形成对比

        内容建议：
        - 步骤长度：建议12个字以内（如"第一步：数据预处理"）
        - 常用格式：
          * "第一步：初始化"
          * "阶段1：数据收集"
          * "步骤A：建立模型"
        - 自动适配：过长步骤会自动缩小字体以适应区域

        Args:
            step_text (str): 步骤文字

        Returns:
            VGroup: 定位到步骤区域的内容组，包含自动适配的文字

        使用示例：
            step_group = self.create_step_region_content("第一步：定义函数")
            self.play(Write(step_group))

            # 动态更新步骤，长步骤会自动缩放
            step2 = self.create_step_region_content("第二步：复杂数学推导和分析过程")
            self.play(Transform(step_group, step2))
        """
        step_content = Text(
            step_text,
            font_size=self.font_sizes["step"],
            color=self.colors["text_primary"],
            font=self.font_types["step"],
        ).set_stroke(color = self.colors["text_primary"], width=2)

        # 步骤区域位置（右上角）
        step_position = UP * 3.5 + RIGHT * 3.5

        # 自动适配内容大小
        step_content = self.auto_fit_text_content(
            step_content,
            max_width=self.regions["step_width"],
            max_height=self.regions["step_height"],
            target_position=step_position,
        )
        step_content.to_edge(UR)

        step_group = VGroup(step_content)
        return step_group

    def create_main_region_content(self, main_content):
        """
        创建主内容区域内容的标准接口（自动适配区域大小）

        功能说明：
        - 自动定位到屏幕中央主内容区域（6.0×3.5）
        - 内容自动缩放适应区域，保持15%边距
        - 支持任何Mobject对象：图形、动画、文字等

        区域优势：
        - 大空间：比原版增大33%，适合复杂可视化
        - 灵活性：可容纳多个图形、坐标系、动画序列
        - 居中显示：视觉焦点，最佳观看效果

        Args:
            main_content: 主要内容对象（VGroup、图形、动画等）

        Returns:
            VGroup: 定位到主内容区域的内容组，包含自动适配的内容

        使用示例：
            # 数学函数图
            axes = Axes(x_range=[-3,3], y_range=[-2,4])
            curve = axes.plot(lambda x: x**2)
            main_group = self.create_main_region_content(VGroup(axes, curve))

            # 算法动画
            sorting_animation = self.create_sorting_visualization()
            main_group = self.create_main_region_content(sorting_animation)

            # 复杂图形组合
            complex_diagram = VGroup(circles, arrows, labels)
            main_group = self.create_main_region_content(complex_diagram)
        """
        # 主内容区域位置（中央）
        main_position = LEFT * 2.0

        # 自动适配内容大小
        main_content = self.auto_fit_content(
            main_content,
            max_width=self.regions["main_width"],
            max_height=self.regions["main_height"],
            target_position=main_position,
        )

        return main_content

    def create_left_auxiliary_content(self, auxiliary_content):
        """
        创建左侧辅助区域内容的标准接口（自动适配区域大小）

        功能说明：
        - 自动定位到左侧辅助区域（3.0×4.0）
        - 内容自动缩放适应区域，保持15%边距
        - 支持任何Mobject对象：文字、图形、图表等

        Args:
            auxiliary_content: 辅助内容对象（VGroup、文字、图形等）

        Returns:
            VGroup: 定位到左侧辅助区域的内容组，包含自动适配的内容

        使用示例：
            # 文字列表
            text_list = VGroup(*[Text(f"• {item}") for item in items])
            left_group = self.create_left_auxiliary_content(text_list)

            # 带标题的内容
            title = Text("要点", weight=BOLD)
            content = VGroup(*[Text(f"• {item}") for item in items])
            full_content = VGroup(title, content).arrange(DOWN)
            left_group = self.create_left_auxiliary_content(full_content)
        """
        # 左侧辅助区域位置（调整位置以适应主内容区域增大）
        left_position = LEFT * 5.0

        # 自动适配内容大小
        auxiliary_content = self.auto_fit_content(
            auxiliary_content,
            max_width=self.regions["auxiliary_width"],
            max_height=self.regions["auxiliary_height"],
            target_position=left_position,
        )

        return auxiliary_content

    def create_right_auxiliary_content(self, auxiliary_content):
        """
        创建右侧辅助区域内容的标准接口（自动适配区域大小）

        功能说明：
        - 自动定位到右侧辅助区域（3.0×4.0）
        - 内容自动缩放适应区域，保持15%边距
        - 支持任何Mobject对象：文字、图形、图表等

        Args:
            auxiliary_content: 辅助内容对象（VGroup、文字、图形等）

        Returns:
            VGroup: 定位到右侧辅助区域的内容组，包含自动适配的内容

        使用示例：
            # 公式列表
            formula_list = VGroup(*[MathTex(formula) for formula in formulas])
            right_group = self.create_right_auxiliary_content(formula_list)

            # 带标题的内容
            title = Text("公式", weight=BOLD)
            content = VGroup(*[MathTex(formula) for formula in formulas])
            full_content = VGroup(title, content).arrange(DOWN)
            right_group = self.create_right_auxiliary_content(full_content)
        """
        # 右侧辅助区域位置（调整位置以适应主内容区域增大）
        right_position = RIGHT * 5.0

        # 自动适配内容大小
        auxiliary_content = self.auto_fit_content(
            auxiliary_content,
            max_width=self.regions["auxiliary_width"],
            max_height=self.regions["auxiliary_height"],
            target_position=right_position,
        )

        return auxiliary_content

    def create_result_region_content(self, result_content):
        """创建结果区域"""

        # 结果区域位置（底部中央）
        result_position = DOWN * 2.5

        # 自动适配内容大小
        result_content = self.auto_fit_text_content(
            result_content,
            max_width=self.regions["result_width"],
            max_height=self.regions["result_height"],
            target_position=result_position,
        )

        return result_content


# === 使用说明 ===
"""
专业科学模板使用指南：

1. 继承ProfessionalScienceTemplate类
2. 重写construct方法
3. 使用以下标准接口方法创建各区域内容：

   - self.create_title_region_content(title): 创建标题区域（自动适配）
   - self.create_step_region_content(step): 创建步骤区域（自动适配）
   - self.create_main_region_content(content): 创建主内容区域（自动适配）
   - self.create_left_auxiliary_content(title, items): 创建左辅助区域（自动适配）
   - self.create_right_auxiliary_content(title, items): 创建右辅助区域（自动适配）
   - self.create_result_region_content(result): 创建结果区域（自动适配）

4. 📝 内容建议指南（确保最佳效果）：

   区域类型 | 建议限制 | 示例
   --------|---------|-------
   标题区域 | 推荐8字以内，支持自动缩放 | "数学原理"、"物理定律"、"复杂机器学习算法"
   步骤区域 | 推荐12字以内，支持自动缩放 | "第一步：数据预处理"、"阶段3：复杂计算处理"
   辅助标题 | 6个字以内 | "要点"、"公式"、"特点"
   辅助项目 | 5项×15字/项 | "• 开口向上"、"• 时间复杂度O(n²)"
   结果区域 | 40个字以内 | "结论：二次函数具有抛物线形状..."

5. 快速开始模板：
    ```python
    from prompts.professional_science_template import ProfessionalScienceTemplate

    class MyExample(ProfessionalScienceTemplate):
        def construct(self):
            # 必须调用
            self.setup_background()

            # 基本布局（所有区域都支持自动适配）
            title = self.create_title_region_content("标题")  # 推荐≤8字，支持自动缩放
            step = self.create_step_region_content("第一步")  # 推荐≤12字，支持自动缩放
            main = self.create_main_region_content(my_content)
            result = self.create_result_region_content("结论")  # ≤40字

            # 可选：辅助区域
            left = self.create_left_auxiliary_content("要点:", items)
            right = self.create_right_auxiliary_content("公式:", formulas)

            # 动画展示
            self.play(Write(title), Write(step))
            self.play(FadeIn(main))
            self.play(FadeIn(left), FadeIn(right))  # 可选
            self.play(Write(result))
    ```

6. 复杂多步动画模板：

    对于涉及多个步骤的复杂教学场景，您可以自由控制各区域内容的动态更新：

    ```python
    class ComplexAnimation(ProfessionalScienceTemplate):
        def construct(self):
            self.setup_background()

            # 创建持久化标题
            title = self.create_title_region_content("算法演示")
            self.add(title)

            # 多步骤动画序列
            steps_data = [
                {"step": "第一步：初始化", "main": "self.create_step1_content()", "left": ["• 设置数组", "• 定义变量"], "result": "初始化完成"},
                {"step": "第二步：排序", "main": "self.create_step2_content()", "left": ["• 比较元素", "• 交换位置"], "result": "正在排序..."},
                {"step": "第三步：完成", "main": "self.create_step3_content()", "left": ["• 验证结果", "• 输出数组"], "result": "排序完成！"}
            ]

            # 动态执行多步骤
            current_step = current_main = current_left = current_result = None

            for i, data in enumerate(steps_data):
                # 创建新内容
                new_step = self.create_step_region_content(data["step"])
                new_main = self.create_main_region_content(data["main"])
                new_left = self.create_left_auxiliary_content("进度:", data["left"])
                new_result = self.create_result_region_content(data["result"])

                if i == 0:
                    # 首次显示
                    self.play(Write(new_step), FadeIn(new_main), FadeIn(new_left), Write(new_result))
                else:
                    # 区域间转换动画 - 用户自定义VGroup动画
                    animations = []
                    if current_step:
                        animations.append(Transform(current_step, new_step))
                    if current_left:
                        animations.append(Transform(current_left, new_left))
                    if current_result:
                        animations.append(Transform(current_result, new_result))

                    # 主内容区域特殊动画（用户自由实现）
                    animations.append(self.create_main_transition(current_main, new_main))

                    self.play(*animations)
                    self.wait(1)

                # 更新当前引用
                current_step = new_step
                current_main = new_main
                current_left = new_left
                current_result = new_result

         def create_main_transition(self, old_content, new_content):
             # 自定义主内容区域转换动画
             # 用户可自由实现：渐变、滑动、缩放、形变等
             return ReplacementTransform(old_content, new_content)

         def create_step1_content(self):
             # 第一步的主内容 - 用户自定义
             return VGroup(Circle(), Text("初始化"))

         def create_step2_content(self):
             #第二步的主内容 - 用户自定义
             return VGroup(Square(), Text("处理中"))

         def create_step3_content(self):
             #第三步的主内容 - 用户自定义
             return VGroup(Triangle(), Text("完成"))
    ```

    核心特性：
    • 区域自动定位：各区域内容自动适配布局
    • VGroup动画：支持Transform、ReplacementTransform等manim动画
    • 状态管理：可跟踪和更新各区域的当前状态
    • 动画自由度：主内容区域动画完全由用户控制
    • 同步更新：多个区域可同时或分步更新内容

"""
