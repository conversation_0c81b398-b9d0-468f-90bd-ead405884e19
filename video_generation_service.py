#!/usr/bin/env python3
"""
视频生成服务 - 基于Feynman工作流的视频生成API

提供REST API接口，接收URL、文件或聊天内容，生成相应的视频。
"""

import os
import sys
import json
import yaml
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import uuid

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn
from loguru import logger

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

# 设置环境变量
os.environ["TAVILY_API_KEY"] = "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"

app = FastAPI(
    title="Feynman视频生成服务",
    description="基于AI的视频生成服务，支持URL、文件和聊天内容输入",
    version="1.0.0"
)

class VideoGenerationRequest(BaseModel):
    """视频生成请求模型"""
    url: Optional[str] = None
    chat: Optional[str] = None
    file_path: Optional[str] = None  # 本地文件路径
    username: Optional[str] = None  # 用户名
    user_id: Optional[str] = None  # 用户id
    output_dir: Optional[str] = "output"

class VideoGenerationResponse(BaseModel):
    """视频生成响应模型"""
    success: bool
    video_name: Optional[str] = None
    video_title: Optional[str] = None
    video_path: Optional[str] = None
    project_dir: Optional[str] = None
    error_message: Optional[str] = None
    timestamp: str

def create_temp_config(url: Optional[str] = None, file_path: Optional[str] = None, chat: Optional[str] = None, username: Optional[str] = None, user_id: Optional[str] = None) -> str:
    """创建临时配置文件"""
    # 读取原始配置
    with open("config/config.yaml", "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)

    # 清空所有intent_input字段，只保留用户实际提供的输入
    config["material"]["intent_input"]["url"] = ""
    config["material"]["intent_input"]["file"] = ""
    config["material"]["intent_input"]["chat"] = ""

    # 更新输入配置 - 只设置用户实际提供的非空值
    if url:
        config["material"]["intent_input"]["url"] = url
    if file_path:
        config["material"]["intent_input"]["file"] = file_path
    if chat:
        config["material"]["intent_input"]["chat"] = chat

    # 在视频生成服务中启用服务器模式
    config["app"]["server"] = True

    # 创建临时配置文件
    temp_config_path = f"config/temp_config_{user_id}_{username}_{uuid.uuid4().hex[:8]}.yaml"
    with open(temp_config_path, "w", encoding="utf-8") as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

    return temp_config_path

def extract_video_info(project_dir: str) -> tuple[str, str]:
    """从项目目录中提取视频名称和标题"""
    video_name = None
    video_title = None

    # 查找视频文件 - 支持递归搜索
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    project_path = Path(project_dir)

    # 首先查找 generated_files 目录下的视频文件
    generated_files_dir = project_path / "generated_files"
    if generated_files_dir.exists():
        for ext in video_extensions:
            video_files = list(generated_files_dir.glob(f"*{ext}"))
            if video_files:
                # 优先选择最终的视频文件（包含 final 和项目名称的文件）
                project_name = project_path.name
                final_videos = [f for f in video_files if "final" in f.name.lower() and project_name in f.name]

                if final_videos:
                    # 如果有多个最终视频，优先选择 no_bgm 版本
                    no_bgm_videos = [f for f in final_videos if "no_bgm" in f.name.lower()]
                    if no_bgm_videos:
                        video_name = no_bgm_videos[0].name
                    else:
                        video_name = final_videos[0].name
                else:
                    # 如果没有找到最终视频，选择第一个视频文件
                    video_name = video_files[0].name
                break

    # 如果没找到，搜索整个项目目录
    if not video_name:
        for ext in video_extensions:
            video_files = list(project_path.rglob(f"*{ext}"))
            if video_files:
                video_name = video_files[0].name
                break

    # 查找标题信息
    title_files = [
        "feynman_explanation.json",
        "outline.json",
        "storyboard.json",
        "material_result.json",
        "intention_result.json"
    ]

    for title_file in title_files:
        title_path = project_path / title_file
        if title_path.exists():
            try:
                with open(title_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    # 尝试从不同字段提取标题
                    if isinstance(data, dict):
                        if "title" in data:
                            video_title = data["title"]
                            break
                        elif "name" in data:
                            video_title = data["name"]
                            break
                        elif "topic" in data:
                            video_title = data["topic"]
                            break
                        elif "subject" in data:
                            video_title = data["subject"]
                            break
                        elif "classification" in data and isinstance(data["classification"], dict):
                            # 从意图分类结果中提取标题
                            classification = data["classification"]
                            if "extracted_info" in classification:
                                info = classification["extracted_info"]
                                if "title" in info:
                                    video_title = info["title"]
                                    break
                                elif "repo" in info:
                                    video_title = info["repo"]
                                    break
            except Exception as e:
                logger.debug(f"读取标题文件 {title_file} 失败: {e}")
                continue

    # 如果没有找到标题，使用项目目录名
    if not video_title:
        video_title = Path(project_dir).name

    # 如果没有找到视频文件，记录日志
    if not video_name:
        logger.warning(f"在项目目录 {project_dir} 中未找到视频文件")
        # 列出目录内容以便调试
        try:
            logger.debug(f"项目目录内容: {list(project_path.iterdir())}")
            if generated_files_dir.exists():
                logger.debug(f"generated_files目录内容: {list(generated_files_dir.iterdir())}")
        except Exception as e:
            logger.debug(f"无法列出目录内容: {e}")

    return video_name, video_title

@app.post("/generate_video", response_model=VideoGenerationResponse)
async def generate_video(request: VideoGenerationRequest):
    """生成视频的主要接口"""
    try:
        # 验证输入
        if not any([request.url, request.file_path, request.chat]):
            raise HTTPException(status_code=400, detail="必须提供url、file_path或chat中的至少一个")

        # 创建临时配置文件
        temp_config_path = create_temp_config(
            url=request.url,
            file_path=request.file_path,
            chat=request.chat,
            username=request.username,
            user_id=request.user_id
        )

        logger.info(f"开始生成视频，配置: {temp_config_path}")

        # 导入并运行工作流
        from unified_feynman_workflow import UnifiedFeynmanWorkflow

        # 创建临时输出目录
        temp_output_dir = f"output/{request.username}_{request.user_id}"
        os.makedirs(temp_output_dir, exist_ok=True)
        newtag = f"{request.username}_{request.user_id}"

        # 运行工作流
        workflow = UnifiedFeynmanWorkflow(temp_config_path, newtag)

        # 运行工作流
        success = workflow.run()

        if not success:
            raise Exception("视频生成失败")

        # 获取实际的项目目录
        # 从工作流的输出中获取实际的项目目录
        actual_project_dir = None

        # 查找最新的项目目录
        actual_project_dir = workflow.project_name_
        logger.info(f"找到项目目录: {actual_project_dir}")

        if not actual_project_dir:
            # 如果没找到，使用默认的临时目录
            actual_project_dir = temp_output_dir
            logger.warning(f"未找到实际项目目录，使用临时目录: {actual_project_dir}")

        # 提取视频信息
        video_name, video_title = extract_video_info(actual_project_dir)

        # 清理临时配置文件
        #try:
        #    os.remove(temp_config_path)
        #except:
        #    pass

        return VideoGenerationResponse(
            success=True,
            video_name=video_name,
            video_path=str(project_root) + "/" + actual_project_dir + "/generated_files/" + video_name,
            video_title=video_title,
            project_dir=actual_project_dir,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        logger.error(f"视频生成失败: {str(e)}")
        return VideoGenerationResponse(
            success=False,
            error_message=str(e),
            timestamp=datetime.now().isoformat()
        )

@app.post("/upload_and_generate")
async def upload_and_generate(
    file: UploadFile = File(...),
    chat: Optional[str] = Form(None)
):
    """上传文件并生成视频"""
    try:
        # 创建临时文件
        temp_dir = f"temp_uploads/{uuid.uuid4().hex[:8]}"
        os.makedirs(temp_dir, exist_ok=True)

        file_path = os.path.join(temp_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 创建请求对象
        request = VideoGenerationRequest(
            file_path=file_path,
            chat=chat
        )

        # 调用生成接口
        return await generate_video(request)

    except Exception as e:
        logger.error(f"文件上传和视频生成失败: {str(e)}")
        return VideoGenerationResponse(
            success=False,
            error_message=str(e),
            timestamp=datetime.now().isoformat()
        )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Feynman视频生成服务",
        "version": "1.0.0",
        "endpoints": {
            "POST /generate_video": "生成视频（JSON请求）",
            "POST /upload_and_generate": "上传文件并生成视频",
            "GET /health": "健康检查"
        }
    }

if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs("output", exist_ok=True)
    os.makedirs("temp_uploads", exist_ok=True)

    # 启动服务
    uvicorn.run(
        "video_generation_service:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
