#!/bin/bash

# Feynman视频生成服务监控演示脚本

echo "🎬 Feynman视频生成服务监控演示"
echo "=================================="
echo ""

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}📋 当前服务状态:${NC}"
./monitor_video_service.sh --check
echo ""

echo -e "${BLUE}📊 服务进程信息:${NC}"
ps aux | grep "video_generation_service.py" | grep -v grep
echo ""

echo -e "${BLUE}🌐 健康检查测试:${NC}"
curl -s "http://localhost:8000/health" | head -3
echo ""

echo -e "${BLUE}📁 目录结构:${NC}"
ls -la output/ temp_uploads/ logs/ 2>/dev/null | head -5
echo ""

echo -e "${YELLOW}🚀 启动监控演示 (按Ctrl+C停止):${NC}"
echo "监控脚本将每30秒检查一次服务状态"
echo "如果服务失败，将自动尝试重启"
echo ""

# 运行监控脚本进行演示
./monitor_video_service.sh
