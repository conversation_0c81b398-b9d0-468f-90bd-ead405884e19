#!/usr/bin/env python

from __future__ import annotations

from typing import Any

import mistune
from loguru import logger

# Define type for simplified elements
SimplifiedElement = dict[str, Any]


class SimplifiedPangoRenderer(mistune.BaseRenderer):
    """
    Custom Mistune renderer to convert Markdown into a simplified structure
    where text-like elements become Pango-formatted strings within a dict,
    and other elements like tables and images have a flat structure.
    """

    NAME = "simplified_pango"

    def __init__(self):
        super().__init__()
        self.list_stack: list[tuple[int, int, bool]] = []  # (level, item_number, is_ordered)
        # 用于跟踪样式信息的状态
        self.current_plain_text = ""  # 当前段落的纯文本
        self.current_styles = {
            "bold": [],
            "italic": [],
            "code": [],
            "strikethrough": [],
            "link": [],
        }  # 当前段落的样式信息
        self.plain_text_offset = 0  # 当前在纯文本中的偏移量

    # --- Helper for rendering children to a single Pango string ---
    def _render_children_to_pango_string(self, children_tokens: list[dict[str, Any]], state: dict[str, Any]) -> str:
        parts = []
        for child_token in children_tokens:
            method_name = f"_render_inline_{child_token['type']}"
            method = getattr(self, method_name, self._render_inline_default)
            parts.append(method(child_token, state))
        return "".join(parts)

    def _extract_raw_markdown_content(self, children_tokens: list[dict[str, Any]]) -> str:
        """
        第一级解析：提取原始markdown内容，保持格式标记
        只做基本的文本提取，不处理样式
        """
        parts = []
        for child_token in children_tokens:
            token_type = child_token.get("type", "")

            if token_type == "text":
                parts.append(child_token.get("raw", ""))
            elif token_type == "strong":
                # 保持**格式
                inner_text = self._extract_raw_from_children(child_token.get("children", []))
                parts.append(f"**{inner_text}**")
            elif token_type == "emphasis":
                # 保持*格式
                inner_text = self._extract_raw_from_children(child_token.get("children", []))
                parts.append(f"*{inner_text}*")
            elif token_type == "codespan":
                # 保持`格式
                code_text = child_token.get("raw", "")
                parts.append(f"`{code_text}`")
            elif token_type == "strikethrough":
                # 保持~~格式
                inner_text = self._extract_raw_from_children(child_token.get("children", []))
                parts.append(f"~~{inner_text}~~")
            elif token_type == "link":
                # 保持链接格式
                inner_text = self._extract_raw_from_children(child_token.get("children", []))
                url = child_token.get("attrs", {}).get("url", "")
                parts.append(f"[{inner_text}]({url})")
            elif token_type == "inline_math":
                # 保持数学公式格式
                math_content = child_token.get("raw", "")
                parts.append(f"${math_content}$")
            else:
                # 其他类型，递归处理或使用raw内容
                if "raw" in child_token:
                    parts.append(child_token.get("raw", ""))
                elif "children" in child_token:
                    parts.append(self._extract_raw_from_children(child_token.get("children", [])))

        return "".join(parts)

    def _extract_raw_from_children(self, children_tokens: list[dict[str, Any]]) -> str:
        """辅助方法：从子token中提取原始文本"""
        parts = []
        for child_token in children_tokens:
            if child_token.get("type") == "text":
                parts.append(child_token.get("raw", ""))
            else:
                # 递归处理嵌套结构
                parts.append(child_token.get("raw", ""))
        return "".join(parts)

    # --- 新增：样式跟踪方法 ---
    def _reset_style_tracking(self):
        """重置样式跟踪状态"""
        self.current_plain_text = ""
        self.current_styles = {"bold": [], "italic": [], "code": [], "strikethrough": [], "link": []}
        self.plain_text_offset = 0

    def _add_style_info(self, text: str, style_type: str, style_value: str = None):
        """添加样式信息到当前跟踪状态"""
        start_pos = len(self.current_plain_text)
        end_pos = start_pos + len(text)

        if style_type not in self.current_styles:
            self.current_styles[style_type] = []

        self.current_styles[style_type].append({"start": start_pos, "end": end_pos, "text": text, "value": style_value})

    def _render_children_to_plain_text_with_styles(
        self, children_tokens: list[dict[str, Any]], state: dict[str, Any]
    ) -> tuple[str, dict]:
        """
        渲染子元素为纯文本，同时跟踪样式信息
        返回: (纯文本, 样式信息字典)
        """
        self._reset_style_tracking()

        for child_token in children_tokens:
            self._process_token_for_styles(child_token, state)

        return self.current_plain_text, self.current_styles

    def _process_token_for_styles(self, token: dict[str, Any], state: dict[str, Any]):
        """处理单个token，提取纯文本和样式信息"""
        token_type = token.get("type", "")
        logger.info(f"Processing token for styles: {token_type} {token.get('raw', '')}")

        if token_type == "text":
            # 普通文本，直接添加
            text = token.get("raw", "")
            self.current_plain_text += text

        elif token_type == "strong":
            # 粗体文本
            children = token.get("children", [])
            bold_text = ""
            for child in children:
                if child.get("type") == "text":
                    bold_text += child.get("raw", "")

            if bold_text:
                # self._add_style_info(bold_text, "bold")
                self.current_styles["bold"].append(bold_text)
                self.current_plain_text += bold_text

        elif token_type == "emphasis":
            # 斜体文本
            children = token.get("children", [])
            italic_text = ""
            for child in children:
                if child.get("type") == "text":
                    italic_text += child.get("raw", "")

            if italic_text:
                # self._add_style_info(italic_text, "italic")
                self.current_styles["italic"].append(italic_text)
                self.current_plain_text += italic_text

        elif token_type == "codespan":
            # 行内代码
            code_text = token.get("raw", "")
            if code_text:
                # self._add_style_info(code_text, "code")
                self.current_styles["code"].append(code_text)
                self.current_plain_text += code_text

        elif token_type == "strikethrough":
            # 删除线文本
            children = token.get("children", [])
            strike_text = ""
            for child in children:
                if child.get("type") == "text":
                    strike_text += child.get("raw", "")

            if strike_text:
                # self._add_style_info(strike_text, "strikethrough")
                self.current_styles["strikethrough"].append(strike_text)
                self.current_plain_text += strike_text

        elif token_type == "link":
            # 链接文本
            children = token.get("children", [])
            link_text = ""
            for child in children:
                if child.get("type") == "text":
                    link_text += child.get("raw", "")

            if link_text:
                # self._add_style_info(link_text, "link", url)
                self.current_styles["link"].append(link_text)
                self.current_plain_text += link_text

        else:
            logger.debug(f"Unsupported token type for style tracking: {token_type}")
            # 其他类型，递归处理子元素
            children = token.get("children", [])
            for child in children:
                self._process_token_for_styles(child, state)

    # --- Inline Element to Pango String Renderers ---
    def _render_inline_text(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        return mistune.escape(token.get("raw", ""))

    def _render_inline_strong(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        content = self._render_children_to_pango_string(token.get("children", []), state)
        # 保持原始markdown格式
        return f"**{content}**"

    def _render_inline_emphasis(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        content = self._render_children_to_pango_string(token.get("children", []), state)
        # 保持原始markdown格式
        return f"*{content}*"

    def _render_inline_codespan(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        # 保持原始markdown格式
        return f"`{token.get('raw', '')}`"

    def _render_inline_strikethrough(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        content = self._render_children_to_pango_string(token.get("children", []), state)
        # 保持原始markdown格式
        return f"~~{content}~~"

    def _render_inline_link(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        content = self._render_children_to_pango_string(token.get("children", []), state)
        url = token.get("attrs", {}).get("url", "")
        # 保持原始markdown格式
        return f"[{content}]({url})"

    def _render_inline_image(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        # For alt text if image is used inline in a Pango string (though primary image is a block element)
        alt_text_children = token.get("children", [])
        if alt_text_children:
            return self._render_children_to_pango_string(alt_text_children, state)
        return mistune.escape(token.get("attrs", {}).get("alt", ""))

    def _render_inline_newline(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        return "\n" if token.get("hard") else " "

    def _render_inline_softbreak(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        return " "  # Treat softbreaks as spaces

    def _render_inline_inline_html(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        # Basic Pango supported tags, or escape others
        raw_html = token.get("raw", "")
        # This is a simplification; proper HTML to Pango would be complex.
        # For now, allow common Pango-compatible tags if they are not escaped by mistune's default text handling.
        # However, mistune's 'text' token usually escapes HTML. If this method is hit, it's likely unescaped.
        # A safer bet for arbitrary HTML is to escape it or log a warning.
        # logger.warning(f"Inline HTML encountered: {raw_html}. Outputting as escaped text.")
        return mistune.escape(raw_html)  # Default to escaping to be safe

    def _render_inline_inline_math(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        """Render inline math formulas (e.g., $x^2$) within text"""
        math_content = token.get("raw", "")
        # 保持原始数学公式格式，与第一级解析保持一致
        return f"${math_content}$"

    def _render_inline_default(self, token: dict[str, Any], state: dict[str, Any]) -> str:
        logger.warning(f"Unsupported inline token type for Pango string: {token['type']}")
        if token["type"] == "block_text":  # Handle block_text if it somehow reaches here as inline
            # This should ideally be handled by block-level renderers like list_item
            return self._render_children_to_pango_string(token.get("children", []), state)
        if "raw" in token:
            return mistune.escape(token.get("raw", ""))
        if "children" in token:
            return self._render_children_to_pango_string(token.get("children", []), state)
        return ""

    # --- Block Element Renderers (Return SimplifiedElement or List[SimplifiedElement] or None) ---
    def paragraph(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        # If the paragraph is just an image, return the image element directly
        children = token.get("children", [])
        if len(children) == 1 and children[0]["type"] == "image":
            return self.image(children[0], state)

        # 第一级解析：只提取原始markdown内容，不做细粒度格式解析
        raw_content = self._extract_raw_markdown_content(token.get("children", []))

        return {"type": "text", "content": raw_content + "\n"}

    def heading(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        level = token["attrs"]["level"]
        # 提取原始内容，不使用span标签
        raw_content = self._extract_raw_markdown_content(token.get("children", []))

        return {"type": "heading", "content": raw_content.strip(), "attrs": {"level": level}}

    def thematic_break(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        return {"type": "horizontal_rule"}

    def block_quote(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        new_state = state.copy()
        new_state["in_blockquote"] = True
        new_state["blockquote_prefix"] = state.get("blockquote_prefix", "▎ ")

        pango_parts = []
        for child_token in token.get("children", []):
            # Render child block (e.g., paragraph) into its Pango string content
            # We need to get the 'content' if it's a text element, or handle other types
            rendered_child = self.render_token(child_token, new_state)
            if rendered_child and rendered_child["type"] == "text":
                # The paragraph renderer already adds its own blockquote prefix if in_blockquote is true.
                # So, we just take its content directly.
                pango_parts.append(rendered_child["content"].strip())
            elif rendered_child and rendered_child["type"] == "blockquote":  # Nested blockquote
                # The nested blockquote would have already formatted its content with deeper prefixes.
                pango_parts.append(rendered_child["content"].strip())
            elif rendered_child:  # Other types, try to get a string representation
                logger.warning(f"Unsupported block_quote child type for Pango string: {rendered_child.get('type')}")
                pango_parts.append(str(rendered_child.get("content", "")))

        combined_pango_content = "\n".join(pango_parts)
        # The paragraph method itself adds the prefix, so we don't add it again here if children are paragraphs.
        # However, if block_quote is called directly, it should ensure its content is prefixed.
        # Let's ensure the prefix is applied at this level to the combined content.
        # This might lead to double prefix if paragraph also adds it. Let's adjust paragraph.
        # For now, let paragraph handle its own prefixing based on state.
        # The `paragraph` method will use `new_state` which has `in_blockquote=True`.
        return {"type": "blockquote", "content": combined_pango_content + "\n"}

    def _render_block_content_to_pango_string(
        self, token: dict[str, Any], state: dict[str, Any], indent_str: str = ""
    ) -> str:
        """Helper to render various block tokens to an indented Pango string."""
        rendered_element = self.render_token(token, state)
        if not rendered_element:
            return ""

        if isinstance(rendered_element, list):  # Should not happen for blocks we pass here
            logger.error(f"_render_block_content_to_pango_string received list for {token['type']}")
            return ""

        content_str = ""
        if rendered_element["type"] == "text":
            content_str = rendered_element["content"].strip()
        elif rendered_element["type"] == "list":  # Handle both old string format and new structured format
            content = rendered_element["content"]
            if isinstance(content, list):
                # New structured format: convert back to string for nested content
                list_strings = []
                for item_data in content:
                    level = item_data.get("level", 1)
                    text = item_data.get("text", "")
                    marker = item_data.get("marker", "•")
                    item_indent_str = "  " * (level - 1)
                    list_strings.append(f"{item_indent_str}{marker} {text}")
                content_str = "\n".join(list_strings)
            else:
                # Old string format
                content_str = content.strip()
        elif rendered_element["type"] == "blockquote":  # Already formatted blockquote string
            content_str = rendered_element["content"].strip()
        elif rendered_element["type"] == "code_block":
            lang = rendered_element.get("lang", "")
            code_text = mistune.escape(rendered_element.get("content", ""))
            # Simple Pango for code block
            content_str = f"<span font_family='monospace' bgcolor='#F0F0F0'><b>{lang}</b>\n{code_text}</span>"
        elif rendered_element["type"] == "block_math":
            math_content = rendered_element.get("content", "")
            # 保持原始格式，不使用span标签
            content_str = math_content
        elif rendered_element["type"] == "horizontal_rule":
            content_str = "<hr/>"  # Placeholder, Pango doesn't have <hr>
        else:
            logger.warning(f"Cannot convert {rendered_element['type']} to string for list/blockquote content.")
            content_str = str(rendered_element.get("content", ""))

        if indent_str and content_str:
            return "\n".join([f"{indent_str}{line}" for line in content_str.split("\n")])
        return content_str

    def _render_list_item_to_structured_data(self, item_token: dict[str, Any], state: dict[str, Any]) -> dict[str, Any]:
        """
        Render list item to structured data instead of string.
        Returns: {"level": int, "text": str, "marker": str, "is_ordered": bool}
        """
        if not self.list_stack:
            logger.error("List item rendering (to structured data) outside of list context.")
            return {"level": 1, "text": "", "marker": "•", "is_ordered": False}

        level, current_item_actual_number_from_stack, is_ordered = self.list_stack[-1]

        if is_ordered:
            # current_item_actual_number_from_stack is already the correct number for this item
            marker = f"{current_item_actual_number_from_stack}."
        else:
            marker = "•"

        item_children_state = state.copy()
        item_children_state["in_blockquote"] = False  # Reset blockquote context

        direct_text_parts = []
        nested_block_pango_strings = []

        for child_token in item_token.get("children", []):
            if child_token["type"] in ["paragraph", "block_text"]:
                # 使用第一级解析：提取原始markdown内容，不做格式化处理
                direct_text_parts.append(self._extract_raw_markdown_content(child_token.get("children", [])).strip())
            else:
                # For nested lists, code_blocks, etc., render them fully and indent their string output.
                # Note: For now, we'll still render nested content as string, but this could be improved later
                child_block_indent_str = "  " * level  # For nested blocks under this item
                nested_block_pango_strings.append(
                    self._render_block_content_to_pango_string(child_token, item_children_state, child_block_indent_str)
                )

        item_pango_content = "\n".join(filter(None, direct_text_parts))

        # Combine main content with nested content
        full_text = item_pango_content
        if nested_block_pango_strings:
            full_text += "\n" + "\n".join(filter(None, nested_block_pango_strings))

        return {"level": level, "text": full_text, "marker": marker, "is_ordered": is_ordered}

    def _render_list_item_to_pango_string(self, item_token: dict[str, Any], state: dict[str, Any]) -> str:
        """
        Legacy method for backward compatibility.
        Converts structured data back to string format.
        """
        item_data = self._render_list_item_to_structured_data(item_token, state)
        level = item_data["level"]
        text = item_data["text"]
        marker = item_data["marker"]

        item_indent_str = "  " * (level - 1)
        return f"{item_indent_str}{marker} {text}"

    def list(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        current_level = len(self.list_stack) + 1
        is_ordered = token["attrs"].get("ordered", False)
        start_number = token["attrs"].get("start", 1) if is_ordered else 0

        # Item number for the *current* list being processed. Each item will increment this if ordered.
        self.list_stack.append((current_level, start_number - 1, is_ordered))

        all_item_data = []  # Changed from strings to structured data
        item_counter_for_this_list = start_number - 1  # Tracks item number for this specific list instance

        for child_item_token in token.get("children", []):
            if child_item_token["type"] == "list_item":
                # Update the stack's item_number for the current item being processed
                if is_ordered:
                    item_counter_for_this_list += 1
                    self.list_stack[-1] = (current_level, item_counter_for_this_list, is_ordered)
                else:  # For unordered, item number in stack can remain 0 or be ignored
                    self.list_stack[-1] = (current_level, 0, is_ordered)

                # Get structured data instead of string
                item_data = self._render_list_item_to_structured_data(child_item_token, state)
                all_item_data.append(item_data)
            else:
                logger.warning(f"Unexpected child type in list: {child_item_token['type']}. Skipping.")

        self.list_stack.pop()

        # Return structured list data instead of concatenated string
        return {"type": "list", "content": all_item_data}

    def list_item(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement | None:
        # This method should ideally not be called directly by the main render_tokens loop anymore
        # if the 'list' method correctly processes all its 'list_item' children.
        # If it is called, it means a list_item token appeared outside a list, which is unusual.
        logger.warning("list_item renderer called directly. This item might not be part of a list structure.")
        # Fallback: render as a simple text element, but this loses list context.
        # Or, better, return None and let it be filtered out if it's an anomaly.
        return None

    def block_code(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        return {"type": "code_block", "content": token.get("raw", ""), "lang": token.get("attrs", {}).get("info")}

    def block_math(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        """Render block math formulas (e.g., $$...$$) as a separate element"""
        math_content = token.get("raw", "")
        # 保持原始的$$格式，不使用span标签
        return {"type": "block_math", "content": f"{math_content}"}

    def image(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        # This is for block-level images.
        # The 'alt' text is in token['children'] for mistune v3 if it's parsed from ![alt](src)
        # However, for the simplified output, we only need the URL.
        # alt_text_content = self._render_children_to_pango_string(token.get('children', []), state).strip()
        return {"type": "image", "content": token.get("attrs", {}).get("url", "")}

    def table(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement:
        output_2d_list: list[list[str]] = []

        # Mistune v3 table structure: token['children'] contains 'table_head' and 'table_body' tokens.
        header_cells_texts: list[str] = []
        body_rows_texts: list[list[str]] = []

        for part_token in token.get("children", []):
            if part_token["type"] == "table_head":
                # table_head contains one row of table_cell tokens
                for cell_token in part_token.get("children", []):
                    cell_text = self._render_children_to_pango_string(cell_token.get("children", []), state).strip()
                    header_cells_texts.append(cell_text)
            elif part_token["type"] == "table_body":
                # table_body contains table_row tokens
                for row_token in part_token.get("children", []):
                    current_row_texts: list[str] = []
                    for cell_token in row_token.get("children", []):
                        cell_text = self._render_children_to_pango_string(cell_token.get("children", []), state).strip()
                        current_row_texts.append(cell_text)
                    body_rows_texts.append(current_row_texts)

        if header_cells_texts:
            output_2d_list.append(header_cells_texts)
        output_2d_list.extend(body_rows_texts)

        return {"type": "table", "content": output_2d_list}

    def blank_line(self, token: dict[str, Any], state: dict[str, Any]) -> None:
        return None  # Filter out blank lines from the final list

    def block_html(self, token: dict[str, Any], state: dict[str, Any]) -> SimplifiedElement | None:
        # As per spec, not a primary output type. Could be a text element with escaped HTML.
        logger.warning(f"Block HTML encountered: {token.get('raw', '')[:50]}... Converting to escaped text element.")
        escaped_html = mistune.escape(token.get("raw", ""))
        return {"type": "text", "content": escaped_html + "\n"}

    # Override render_tokens to handle lists of elements and None
    def render_tokens(self, tokens: list[dict[str, Any]], state: Any) -> list[SimplifiedElement]:
        rendered_elements: list[SimplifiedElement] = []
        if not tokens:
            return rendered_elements

        for token in tokens:
            # The 'state' should be passed to each render_token call. Mistune's BaseRenderer does this.
            element = self.render_token(token, state)
            if isinstance(element, list):  # Should only be from old list_item if it's still somehow called
                rendered_elements.extend(el for el in element if el is not None)
            elif element is not None:
                # If element is from list_item directly (which we want to avoid now),
                # it might be a list. This case should be rare.
                if token["type"] == "list_item" and isinstance(element, list):
                    rendered_elements.extend(el for el in element if el is not None)
                else:
                    rendered_elements.append(element)
        return rendered_elements

    def __call__(self, tokens: list[dict[str, Any]], state: Any) -> list[SimplifiedElement]:
        # This is the entry point when the renderer instance is called by mistune
        # Initialize state if it's not already a dict (mistune might pass None initially)
        initial_state = state if isinstance(state, dict) else {}
        self.list_stack = []  # Reset list stack for each top-level call
        return self.render_tokens(tokens, initial_state)


class MarkdownToSimplifiedConverter:
    """Converts Markdown text to a simplified list of dictionaries."""

    def __init__(self):
        self.markdown_parser = mistune.create_markdown(
            renderer=SimplifiedPangoRenderer(),
            plugins=[
                "strikethrough",
                "footnotes",
                "table",
                "url",
                "math",  # Enable LaTeX math support
            ],  # Footnotes might not be handled well by this simplified output
        )

    def convert(self, markdown_text: str) -> list[SimplifiedElement]:
        if not isinstance(markdown_text, str):
            logger.error(f"Input must be a string, got {type(markdown_text)}")
            return [{"type": "error", "content": "Invalid input type"}]

        try:
            parsed_structure = self.markdown_parser(markdown_text)
            return parsed_structure
        except Exception as e:
            logger.exception(f"Error during Markdown parsing: {e}")
            return [{"type": "error", "content": f"Parsing error: {str(e)}"}]


if __name__ == "__main__":
    converter = MarkdownToSimplifiedConverter()
    import json

    def print_test_case(name: str, md_text: str):
        print(f"\n==================== {name} ====================")
        # print(f"--- Markdown Input ---\n{md_text}")
        simplified_output = converter.convert(md_text)
        print("--- Simplified Output (JSON) ---")
        print(json.dumps(simplified_output, indent=2, ensure_ascii=False))

    test_markdown_complex = """
# Heading 1

This is a paragraph with **bold text** and *italic text*.
Also `inline code` and a [link to example](http://example.com).
And ~~strikethrough~~ text.

---

## Heading 2

> This is a blockquote.
> It can span multiple lines.
> > And be nested (though prefix might not show nesting here).

*   Unordered item 1
    *   Nested unordered (rendered after parent item's text)
*   Unordered item 2
    This item has text.
    And another line of text in the same item (becomes one pango string).

1.  Ordered item A
    1.  Nested ordered (rendered after parent item's text)
2.  Ordered item B

```python
def hello_world():
    print("Hello, simplified world!")
```

![Alt text for image](assets/manim_logo.png "Image Title")

| Header A | Header B |
| :------- | :------- |
| Cell 1A  | Cell 1B  |
| Cell 2A  | Cell 2B  |

End of text.
"""
    print_test_case("Complex Markdown Test", test_markdown_complex)

    simple_md = "Just a **simple** paragraph.\n\nAnd another one."
    print_test_case("Simple Paragraphs", simple_md)

    table_md = "| Col1 | Col2 |\n|---|---|\n| R1C1 | R1C2 |\n| R2C1 | R2C2 |"
    print_test_case("Simple Table", table_md)

    list_md = "* Item One\n* Item Two\n  * Sub Item A\n1. Num One\n   1. Sub Num Alpha"
    print_test_case("Lists Test", list_md)

    empty_md = ""
    print_test_case("Empty Input", empty_md)

    code_md = "```js\nconsole.log('test');\n```"
    print_test_case("Code Block Test", code_md)

    image_md = "![My Pic](local/image.jpg)"
    print_test_case("Image Test", image_md)

    blockquote_md = "> Quote line 1\n> \n> Quote line 2"
    print_test_case("Blockquote Test", blockquote_md)

    # Test LaTeX math formulas
    inline_math_md = "This is a paragraph with inline math $x^2 + y^2 = z^2$ and more text."
    print_test_case("Inline Math Test", inline_math_md)

    block_math_md = """Here is a block math formula:

$$
\\int_{0}^{\\infty} e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}
$$

And some text after the formula."""
    print_test_case("Block Math Test", block_math_md)

    mixed_math_md = """# Mathematical Examples

This paragraph contains inline math like $E = mc^2$ and $\\alpha + \\beta = \\gamma$.

Here's a block formula:

$$
\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}
$$

And another inline formula: $\\sin^2(x) + \\cos^2(x) = 1$."""
    print_test_case("Mixed Math Test", mixed_math_md)

    mixed_md = """# 主标🚗题

⚠️ 这是一段普通文本，支持**粗体**和*斜体*。

> 🚰 这是一个引用💦块。

## 子标题

- 列表🐆 项1
- 🐇 列表项2
- 包含数学公式 $E=mc^2$ 的项目

这里有块级数学公式：

$$
a^2 = b^2 + c^2
$$

```python
def hello_world():
    print("Hello, world!")
```"""
    print_test_case("Mixed Test", mixed_md)
