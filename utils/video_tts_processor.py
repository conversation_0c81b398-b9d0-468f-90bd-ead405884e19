#!/usr/bin/env python3
"""
视频TTS处理工具
根据log文件中的动画时间戳将视频分段，并为每段添加对应的TTS讲解文案
支持自动生成SRT字幕文件并嵌入到视频中
"""

import argparse
import asyncio
import os
import re
from pathlib import Path
from typing import List, <PERSON>ple

import edge_tts
from moviepy import VideoFileClip, AudioFileClip, concatenate_videoclips

import sys
sys.path.append(str(Path(__file__).parent.parent))
from utils.audio_utils import AudioUtils


class VideoTTSProcessor:
    def __init__(self, voice="zh-CN-YunxiNeural"):
        """
        初始化视频TTS处理器
        
        Args:
            voice: EdgeTTS语音模型
        """
        self.voice = voice
        self.temp_dir = None
        self.audio_utils = AudioUtils()
    
    def parse_log_timestamps(self, log_file: str) -> List[Tuple[str, float]]:
        """解析log文件中的动画时间戳和阶段信息"""
        timestamps = []
        
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                # 匹配时间戳格式：[动画时间: 3.5s] 阶段记录：红黑树规则
                match = re.match(r'\[动画时间:\s*([\d.]+)s\]\s*阶段记录：(.+)', line)
                if match:
                    animation_time = float(match.group(1))
                    stage_name = match.group(2).strip()
                    timestamps.append((stage_name, animation_time))
                    print(f"解析时间戳: {stage_name} @ {animation_time:.2f}s")
        
        return timestamps
    
    def parse_markdown_scripts(self, md_file: str) -> dict:
        """从markdown文件中提取每个阶段的讲解文案"""
        scripts = {}
        
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按阶段分割内容
        stages = re.split(r'### 阶段(\d+)：', content)[1:]
        
        for i in range(0, len(stages), 2):
            if i + 1 < len(stages):
                stage_num = int(stages[i])
                stage_content = stages[i + 1]
                
                # 提取讲解文案
                script_match = re.search(r'\*\*讲解文案\*\*：(.+?)(?=\n\n|\n###|\Z)', stage_content, re.DOTALL)
                if script_match:
                    script_text = script_match.group(1).strip()
                    scripts[stage_num] = script_text
                    print(f"提取到阶段{stage_num}的讲解文案：{script_text[:50]}...")
        
        return scripts
    
    def calculate_video_segments(self, timestamps: List[Tuple[str, float]], video_duration: float) -> List[Tuple[float, float, str]]:
        """根据动画时间戳计算视频分段"""
        if not timestamps:
            return []
        
        segments = []
        
        for i, (stage_name, animation_time) in enumerate(timestamps):
            start_time = animation_time
            
            # 计算结束时间
            if i + 1 < len(timestamps):
                end_time = timestamps[i + 1][1]
            else:
                end_time = video_duration
            
            # 确保时间在有效范围内
            start_time = max(0, min(start_time, video_duration))
            end_time = max(start_time, min(end_time, video_duration))
            
            if end_time > start_time:
                segments.append((start_time, end_time, stage_name))
                print(f"分段: {stage_name} [{start_time:.2f}s - {end_time:.2f}s]")
        
        return segments
    
    async def generate_tts_audio(self, text: str, output_path: str) -> str:
        """为文本生成TTS音频"""
        communicate = edge_tts.Communicate(text, self.voice)
        await communicate.save(output_path)
        print(f"✅ TTS音频生成: {output_path}")
        return output_path
    
    def add_audio_to_video_segment(self, video_clip, audio_path: str):
        """为视频片段添加音频"""
        if not Path(audio_path).exists():
            print(f"警告：音频文件不存在: {audio_path}")
            return video_clip
        
        # 验证音频文件
        file_size = Path(audio_path).stat().st_size
        if file_size < 1000:  # 小于1KB
            print(f"警告：音频文件太小: {audio_path}")
            return video_clip
        
        # 尝试加载音频
        try:
            audio_clip = AudioFileClip(audio_path)
            # 测试音频是否可读
            test_frame = audio_clip.get_frame(0)
            if test_frame is None:
                audio_clip.close()
                print(f"警告：音频文件无法读取: {audio_path}")
                return video_clip
        except Exception as e:
            print(f"警告：音频文件损坏: {audio_path}, {e}")
            return video_clip
        
        # 调整视频时长匹配音频
        if audio_clip.duration > video_clip.duration:
            # 音频比视频长，延长视频（静止最后一帧）
            extend_duration = audio_clip.duration - video_clip.duration
            last_frame = video_clip.get_frame(video_clip.duration - 1.0 / video_clip.fps)
            
            from moviepy import ImageClip
            frozen_clip = ImageClip(last_frame).with_duration(extend_duration)
            video_clip = concatenate_videoclips([video_clip, frozen_clip])
            print(f"视频延长 {extend_duration:.2f}s 匹配音频")
        
        elif audio_clip.duration < video_clip.duration:
            # 音频比视频短，裁剪视频
            video_clip = video_clip.subclipped(0, audio_clip.duration)
            print(f"视频裁剪到 {audio_clip.duration:.2f}s 匹配音频")
        
        # 替换音频
        video_clip = video_clip.with_audio(audio_clip)
        audio_clip.close()
        
        return video_clip
    
    def merge_audio_with_ffmpeg(self, video_path: str, audio_files: list, output_path: str, srt_file: str = None) -> bool:
        """使用FFmpeg合成音视频，处理TTS音频"""
        import subprocess
        
        try:
            tts_files = [f for f in audio_files if 'tts_segment_' in f]
            
            if not tts_files:
                print("❌ 没有找到有效的TTS音频文件")
                return False
            
            combined_audio = str(self.temp_dir / "combined_audio.mp3")
            
            if len(tts_files) == 1:
                # 只有一个TTS文件，直接使用
                combined_audio = tts_files[0]
            else:
                # 多个TTS文件，拼接
                file_list = str(self.temp_dir / "tts_list.txt")
                with open(file_list, 'w', encoding='utf-8') as f:
                    for tts_file in tts_files:
                        # 使用绝对路径避免路径问题
                        abs_path = os.path.abspath(tts_file)
                        f.write(f"file '{abs_path}'\n")
                
                subprocess.run([
                    'ffmpeg', '-y', '-f', 'concat', '-safe', '0',
                    '-i', file_list, '-c:a', 'mp3', combined_audio
                ], check=True, capture_output=True)
            
            # 3. 合成音视频并添加字幕
            if srt_file and Path(srt_file).exists():
                # 添加字幕的命令
                subtitle_style = "Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑,BorderStyle=3,Outline=1,Shadow=1"
                subprocess.run([
                    'ffmpeg', '-y',
                    '-i', video_path,
                    '-i', combined_audio,
                    '-c:v', 'libx264',
                    '-c:a', 'aac',
                    '-vf', f"subtitles='{srt_file}':force_style='{subtitle_style}'",
                    '-shortest',
                    output_path
                ], check=True, capture_output=True)
                print(f"✅ 添加字幕成功: {srt_file}")
            else:
                print(f"❌ 字幕文件不存在，无法添加字幕")
                return False
            
            print(f"✅ FFmpeg合成成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ FFmpeg命令执行失败: {e}")
            return False
        except FileNotFoundError:
            print(f"❌ 未找到FFmpeg，请确保已安装FFmpeg")
            return False
        except Exception as e:
            print(f"❌ FFmpeg合成出错: {e}")
            return False

    def split_text_by_punctuation(self, text: str) -> List[str]:
        """按标点符号拆分文本，移除标点符号"""
        import re
        
        # 按标点符号拆分（保留分隔符）
        sentences = re.split(r'([。！？；.!?;])', text)
        
        # 合并文本和标点符号，然后移除标点符号
        result = []
        for i in range(0, len(sentences), 2):
            if i < len(sentences):
                sentence = sentences[i].strip()
                if sentence:  # 非空句子
                    # 移除标点符号
                    clean_sentence = re.sub(r'[。！？；.!?;，、,]', '', sentence).strip()
                    if clean_sentence:
                        result.append(clean_sentence)
        
        return result
    
    def calculate_sentence_duration(self, sentence: str, total_duration: float, total_chars: int) -> float:
        """根据字符数计算句子显示时间"""
        if total_chars == 0:
            return 0.0
        
        # 基于字符数比例分配时间，但设置最小和最大时间限制
        char_count = len(sentence)
        duration = (char_count / total_chars) * total_duration
        
        # 设置合理的时间范围：最少1秒，最多8秒
        min_duration = max(1.0, char_count * 0.15)  # 每字符至少0.15秒
        max_duration = min(8.0, char_count * 0.4)   # 每字符最多0.4秒
        
        return max(min_duration, min(max_duration, duration))

    def generate_srt_subtitles(self, scripts: dict, segment_durations: List[float]) -> str:
        """生成SRT字幕文件，按标点符号拆分并根据字数分配时间"""
        srt_lines = []
        current_time = 0.0
        subtitle_index = 1
        
        for i, duration in enumerate(segment_durations):
            stage_num = i + 1
            if stage_num in scripts:
                script_text = scripts[stage_num]
                
                # 按标点符号拆分句子并移除标点符号
                sentences = self.split_text_by_punctuation(script_text)
                
                if sentences:
                    # 计算总字符数
                    total_chars = sum(len(sentence) for sentence in sentences)
                    
                    # 为每个句子分配时间
                    sentence_start_time = current_time
                    
                    for sentence in sentences:
                        sentence_duration = self.calculate_sentence_duration(sentence, duration, total_chars)
                        sentence_end_time = sentence_start_time + sentence_duration
                        
                        # 确保不超过当前段的结束时间
                        if sentence_end_time > current_time + duration:
                            sentence_end_time = current_time + duration
                            sentence_duration = sentence_end_time - sentence_start_time
                        
                        # 格式化时间 (SRT格式: 00:00:00,000)
                        start_time = self.format_srt_time(sentence_start_time)
                        end_time = self.format_srt_time(sentence_end_time)
                        
                        # 添加字幕条目
                        srt_lines.append(f"{subtitle_index}")
                        srt_lines.append(f"{start_time} --> {end_time}")
                        srt_lines.append(sentence)
                        srt_lines.append("")  # 空行分隔
                        
                        print(f"字幕{subtitle_index}: [{start_time} --> {end_time}] {sentence}")
                        
                        subtitle_index += 1
                        sentence_start_time = sentence_end_time
                        
                        # 如果已经到达段结束时间，跳出
                        if sentence_end_time >= current_time + duration:
                            break
                else:
                    print(f"阶段{stage_num}没有有效的句子内容")
            
            current_time += duration
        
        # 保存SRT文件
        srt_file_path = self.temp_dir / "subtitles.srt"
        with open(srt_file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(srt_lines))
        
        print(f"✅ SRT字幕文件生成: {srt_file_path} (共 {subtitle_index-1} 条字幕)")
        return str(srt_file_path)
    
    def format_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式 (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    async def process_video(self, video_path: str, log_file: str, md_file: str, output_path: str, background_music_path: str = None):
        """处理视频：根据log时间戳分段，添加对应的TTS音频"""
        # 创建临时目录
        self.temp_dir = Path(output_path).parent / "temp_tts"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 解析时间戳和讲解文案
        timestamps = self.parse_log_timestamps(log_file)
        scripts = self.parse_markdown_scripts(md_file)
        
        print(f"找到 {len(timestamps)} 个时间戳，{len(scripts)} 个讲解文案")
        
        # 加载视频
        video = VideoFileClip(video_path)
        video_duration = video.duration
        print(f"视频时长: {video_duration:.2f} 秒")
        
        # 计算视频分段
        segments = self.calculate_video_segments(timestamps, video_duration)
        processed_clips = []
        segment_durations = []  # 记录每段的实际长度
        
        # 处理每个分段
        for i, (start_time, end_time, stage_name) in enumerate(segments):
            print(f"\n处理分段 {i+1}/{len(segments)}: {stage_name}")
            
            # 提取视频片段
            video_segment = video.subclipped(start_time, end_time)
            original_duration = video_segment.duration
            
            # 查找对应的讲解文案
            script_text = None
            for stage_num, script in scripts.items():
                if stage_num == i + 1:  # 按序号匹配
                    script_text = script
                    break
            
            if script_text:
                print(f"找到讲解文案: {script_text[:50]}...")
                
                # 生成TTS音频
                audio_path = self.temp_dir / f"tts_segment_{i}.mp3"
                await self.generate_tts_audio(script_text, str(audio_path))
                
                # 为视频段添加音频
                video_segment = self.add_audio_to_video_segment(video_segment, str(audio_path))
            else:
                print("未找到对应的讲解文案")
            
            processed_clips.append(video_segment)
            segment_durations.append(video_segment.duration)
            print(f"分段 {i+1} 长度变化: {original_duration:.2f}s -> {video_segment.duration:.2f}s")
        
        # 采用无音频策略，避免音频reader问题
        print(f"\n🎬 移除音频并拼接 {len(processed_clips)} 个视频片段...")
        video_clips_no_audio = []
        
        # 移除所有音频
        for i, clip in enumerate(processed_clips):
            video_only = clip.without_audio()
            video_clips_no_audio.append(video_only)
        
        # 拼接无音频视频
        final_video = concatenate_videoclips(video_clips_no_audio, method="compose")
        final_video_duration = final_video.duration
        

        
        # 先保存无音频版本
        print(f"💾 保存无音频视频...")
        final_video.write_videofile(
            output_path,
            codec="libx264",
            fps=30,
            preset="medium",
            audio=False,
            remove_temp=True,
        )
        
        # 生成字幕文件
        srt_file = None
        if scripts and segment_durations:
            print(f"\n📝 生成字幕文件...")
            srt_file = self.generate_srt_subtitles(scripts, segment_durations)
        else:
            print(f"⚠️  无法生成字幕：缺少讲解文案或分段信息")
        
        print(f"✅ 无音频视频生成完成: {output_path}")
        if srt_file:
            print(f"📝 字幕文件生成完成: {srt_file}")
        
        # 收集生成的音频文件
        audio_files = []
        print(f"\n🎵 生成的TTS音频文件:")
        for i in range(len(processed_clips)):
            audio_file = self.temp_dir / f"tts_segment_{i}.mp3"
            if audio_file.exists():
                audio_files.append(str(audio_file))
                print(f"  - 分段{i+1}: {audio_file}")
        
        # 清理资源
        video.close()
        for clip in processed_clips:
            clip.close()
        for clip in video_clips_no_audio:
            clip.close()
        final_video.close()
        
        print(f"✅ 视频生成完成: {output_path}")
        
        # 尝试使用FFmpeg合成音视频
        final_output_path = output_path
        if audio_files:
            print(f"\n🔧 尝试使用FFmpeg合成音视频（包含 {len(audio_files)} 个TTS音频文件）...")
            final_with_audio = output_path.replace('.mp4', '_with_audio.mp4')
            success = self.merge_audio_with_ffmpeg(output_path, audio_files, final_with_audio, srt_file)
            
            if success:
                print(f"🎉 音视频合成成功！最终文件: {final_with_audio}")
                final_output_path = final_with_audio
            else:
                print(f"⚠️  FFmpeg合成失败，请手动合成音频")
                print(f"📁 TTS音频文件保留在: {self.temp_dir}")
        else:
            print(f"⚠️  未找到有效的音频文件")
        
        # 添加背景音乐
        if background_music_path:
            print(f"\n🎵 添加背景音乐...")
            output_dir = Path(final_output_path).parent
            project_name = Path(final_output_path).stem
            final_with_bgm = self.audio_utils.add_background_music(
                final_output_path, 
                output_dir, 
                f"{project_name}_with_bgm",
                background_music_path
            )
            
            if final_with_bgm != final_output_path:
                print(f"🎉 背景音乐添加成功！最终文件: {final_with_bgm}")
                final_output_path = final_with_bgm
            else:
                print(f"⚠️  背景音乐添加失败")
        
        print(f"\n✅ 最终视频生成完成: {final_output_path}")
        
        # 不清理临时文件，保留TTS音频供用户使用
        # if self.temp_dir and self.temp_dir.exists():
        #     import shutil
        #     shutil.rmtree(self.temp_dir)


async def main():
    parser = argparse.ArgumentParser(description="视频TTS处理工具")
    parser.add_argument("video_path", help="输入视频文件路径")
    parser.add_argument("log_file", help="时间戳log文件路径")
    parser.add_argument("md_file", help="markdown讲解文案文件路径")
    parser.add_argument("output_path", help="输出视频文件路径")
    parser.add_argument("--background-music", help="背景音乐文件路径")
    parser.add_argument("--voice", default="zh-CN-YunxiNeural", help="EdgeTTS语音模型")
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    for file_path in [args.video_path, args.log_file, args.md_file]:
        if not os.path.exists(file_path):
            print(f"错误：文件不存在 {file_path}")
            return
    
    # 检查背景音乐文件（如果指定了）
    if args.background_music and not os.path.exists(args.background_music):
        print(f"错误：背景音乐文件不存在 {args.background_music}")
        return
    
    # 创建处理器并处理视频
    processor = VideoTTSProcessor(voice=args.voice)
    await processor.process_video(
        args.video_path,
        args.log_file, 
        args.md_file,
        args.output_path,
        args.background_music
    )


if __name__ == "__main__":
    asyncio.run(main())