#!/usr/bin/env python3
"""
背景管理器

专门管理不同类型的背景创建和添加功能
"""

import pickle

import numpy as np
from loguru import logger
from manim import *

from tools.theme_manager import theme


class BackgroundManager:
    """背景管理器类"""

    def __init__(self, scene, background_config=None):
        """
        初始化背景管理器

        Args:
            scene: <PERSON><PERSON>场景实例
            background_config: 背景配置字典
        """
        self.scene = scene
        self.background_config = background_config or {}
        self.background_type = self.background_config.get("type", "hyperbolic_network")

        # 从场景获取尺寸信息
        self.frame_width = scene.frame_width
        self.frame_height = scene.frame_height

    def create_gray_background(self):
        """创建简单的纯灰色背景"""
        # 创建纯色背景
        background = Rectangle(
            width=self.frame_width,
            height=self.frame_height,
            fill_opacity=1,
            stroke_width=0,
            fill_color=BLACK,  # 调暗背景色，从GRAY_D改为GRAY_E
        )

        return background

    def create_hyperbolic_network_background(self):
        """创建双曲网络背景"""
        # 获取主题颜色
        primary_color = theme.colors.text
        background_color = theme.colors.background

        # Main background - using config.frame_width and config.frame_height
        gradient = Rectangle(
            width=self.frame_width, height=self.frame_height, fill_opacity=1, stroke_width=0
        ).set_color_by_gradient([primary_color, background_color, primary_color])

        # Create hyperbolic network with regular pattern
        network = VGroup()

        # Parameters for the hyperbolic grid
        num_radial_lines = 16
        num_circles = 8
        max_radius = 10

        # Create radial lines
        for i in range(num_radial_lines):
            angle = i * TAU / num_radial_lines
            line = Line(
                ORIGIN,
                max_radius * np.array([np.cos(angle), np.sin(angle), 0]),
                stroke_width=1.2,
                stroke_opacity=0.3,
                stroke_color=primary_color,
            )
            network.add(line)

        # Create concentric circles
        for i in range(1, num_circles):
            radius = i * max_radius / num_circles
            circle = Circle(radius=radius, stroke_width=1.2, stroke_opacity=0.3, stroke_color=primary_color)
            network.add(circle)

        # Create hyperbolic curves connecting points
        for i in range(num_radial_lines):
            for j in range(i + 2, num_radial_lines, 3):  # Connect to every third line
                angle1 = i * TAU / num_radial_lines
                angle2 = j * TAU / num_radial_lines

                # Get points on different circles for a more interesting pattern
                radius1 = (i % 3 + 2) * max_radius / num_circles
                radius2 = (j % 3 + 2) * max_radius / num_circles

                start = radius1 * np.array([np.cos(angle1), np.sin(angle1), 0])
                end = radius2 * np.array([np.cos(angle2), np.sin(angle2), 0])

                # Create a curved path between points
                control = (
                    np.array([(start[0] + end[0]) * 0.5, (start[1] + end[1]) * 0.5, 0]) * 0.5
                )  # Pull control point toward center for hyperbolic effect

                curve = CubicBezier(
                    start,
                    start * 0.6 + control * 0.4,
                    end * 0.6 + control * 0.4,
                    end,
                    stroke_width=0.8,
                    stroke_opacity=0.2,
                    stroke_color=primary_color,
                )
                network.add(curve)

        # Scale the network to fit the screen
        network.scale(0.9)

        # Add a central node
        central_node = Circle(
            radius=0.15, fill_opacity=0.5, stroke_width=1.5, stroke_color=primary_color, fill_color=primary_color
        )

        # Add some smaller nodes at intersection points
        nodes = VGroup()
        for i in range(1, num_circles, 2):
            for j in range(0, num_radial_lines, 4):
                angle = j * TAU / num_radial_lines
                radius = i * max_radius / num_circles
                position = radius * np.array([np.cos(angle), np.sin(angle), 0])

                node = Circle(
                    radius=0.08, fill_opacity=0.4, stroke_width=1, stroke_color=primary_color, fill_color=primary_color
                ).move_to(position)
                nodes.add(node)

        network.add(central_node, nodes)

        # Create a clear space in the center for content
        # Use a solid white background for center to ensure text is clear
        center_mask = Circle(
            radius=5.5,
            fill_opacity=1.0,  # Fully opaque
            stroke_width=0,
            fill_color=background_color,
        )

        return gradient, network, center_mask

    def create_scrolling_text(self):
        """创建滚动文字组"""
        text_group = VGroup()  # 恢复使用VGroup，因为只有文字对象

        # 调整字体大小和间距，让屏幕显示更多文字
        text_spacing_x = 8  # 水平间距从12缩小到8
        text_spacing_y = 6  # 垂直间距从9缩小到6

        # 计算需要的行数和列数（确保覆盖整个屏幕加上更大的缓冲区）
        rows = int(self.frame_height / text_spacing_y) + 8  # 增加更多缓冲行
        cols = int(self.frame_width / text_spacing_x) + 8  # 增加更多缓冲列

        text = pickle.load(open("assets/bg_text.pickle", "rb"))

        # 创建多行多列的文字
        for row in range(rows):
            for col in range(cols):
                text_copy = text.copy()

                # 计算位置（从左下角开始，向右上角倾斜排列，调整初始位置确保文字完整显示）
                # 考虑到文字大小和旋转角度，调整起始位置
                start_x = -self.frame_width / 2 - text_spacing_x * 1.5  # 减少左边距，让文字更早进入屏幕
                start_y = -self.frame_height / 2 - text_spacing_y * 1.5  # 减少下边距，让文字更早进入屏幕

                x_pos = start_x + col * text_spacing_x
                y_pos = start_y + row * text_spacing_y

                text_copy.move_to(np.array([x_pos, y_pos, 0]))

                # 添加旋转角度，使文字倾斜15度（从左下到右上）
                text_copy.rotate(PI / 12)  # 旋转15度 (PI/12 = 15度)

                text_group.add(text_copy)

        # 保存间距信息，供滚动函数使用
        text_group.text_spacing_x = text_spacing_x
        text_group.text_spacing_y = text_spacing_y

        return text_group

    def add_gray_background(self):
        """添加纯灰色背景环境"""
        background = self.create_gray_background()
        self.scene.add(background)

        # 添加滚动文字效果，确保在最上层
        scrolling_text = self.create_scrolling_text()
        self.scene.add(scrolling_text)

        # 添加滚动动画（从左下向右上滚动）
        def scroll_text(mob, dt):
            # 向右上角滚动
            mob.shift(dt * np.array([0.3, 0.2, 0]))  # 减慢速度，使滚动更平滑

            # 平滑的循环重置逻辑
            center = mob.get_center()

            # 使用动态计算的间距
            spacing_x = mob.text_spacing_x
            spacing_y = mob.text_spacing_y

            # 当文字移出屏幕右边界时，平滑重置到左边
            if center[0] > self.frame_width / 2 + spacing_x:
                mob.shift(np.array([-(self.frame_width + spacing_x * 2), 0, 0]))

            # 当文字移出屏幕上边界时，平滑重置到下边
            if center[1] > self.frame_height / 2 + spacing_y:
                mob.shift(np.array([0, -(self.frame_height + spacing_y * 2), 0]))

        scrolling_text.add_updater(scroll_text)

    def add_hyperbolic_network_background(self):
        """添加双曲网络背景环境"""
        gradient, network, center_mask = self.create_hyperbolic_network_background()

        # Add the background to the scene
        self.scene.add(gradient, center_mask)

        # Add the network and start rotation animation in the background
        self.scene.add(network)

        # Create rotation updater function
        def rotate_network(mob, dt):
            mob.rotate(dt * 0.1)  # 固定旋转速度

        # Add the continuous rotation updater
        network.add_updater(rotate_network)
        self.scene.add(network)

    def create_theme_background(self):
        """创建基于主题配置的纯色背景"""
        # 从主题配置中获取背景颜色
        background_color = theme.colors.background
        
        # 创建纯色背景
        background = Rectangle(
            width=self.frame_width,
            height=self.frame_height,
            fill_opacity=1,
            stroke_width=0,
            fill_color=background_color,
        )

        return background

    def add_theme_background(self):
        """添加基于主题配置的纯色背景"""
        background = self.create_theme_background()
        self.scene.add(background)

    def add_background(self):
        """
        根据配置添加不同类型的背景
        """
        logger.info(f"Using theme background color: {theme.colors.background}")
        self.add_theme_background()
