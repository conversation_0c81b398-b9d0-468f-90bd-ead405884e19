#!/usr/bin/env python3
"""
音频处理工具类

提供音频和视频的混合、背景音乐添加等功能
"""

import subprocess
from pathlib import Path
from loguru import logger


class AudioUtils:
    """音频处理工具类"""

    def __init__(self, config=None):
        """
        初始化音频工具类
        
        Args:
            config: 配置字典，包含音频相关设置
        """
        self.config = config or {}

    def add_background_music(self, video_path, output_dir, project_name, bg_music_path=None):
        """
        为视频添加背景音乐
        
        Args:
            video_path: 输入视频路径
            output_dir: 输出目录路径
            project_name: 项目名称
            bg_music_path: 背景音乐文件路径，如果为None则从配置中读取
            
        Returns:
            str: 添加背景音乐后的视频路径，如果失败则返回原视频路径
        """
        try:
            # 获取背景音乐路径
            if bg_music_path is None:
                bg_music_path = self.config.get("video", {}).get("bg_music", "assets/bgm_v2.m4a")
            
            if not bg_music_path or not Path(bg_music_path).exists():
                if bg_music_path:
                    logger.warning(f"背景音乐文件不存在: {bg_music_path}")
                return video_path  # 返回原始视频路径

            logger.info("🎵 添加背景音乐...")
            final_video_with_audio = Path(output_dir) / f"{project_name}_final.mp4"

            # 获取视频时长
            duration = self.get_video_duration(video_path)

            # 构建FFmpeg命令
            ffmpeg_audio_cmd = [
                "ffmpeg",
                "-loglevel",
                "warning",
                "-y",
                "-i",
                str(video_path),
                "-t",
                str(duration),
                "-stream_loop",
                "-1",
                "-i",
                bg_music_path,
                "-c:v",
                "copy",
                "-filter_complex",
                "[1:a]volume=0.03[bgm];[0:a][bgm]amix=inputs=2:duration=first",
                str(final_video_with_audio),
            ]

            # 执行命令
            logger.info("🎵 执行FFmpeg命令添加背景音乐...")
            result = subprocess.run(ffmpeg_audio_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.success("✅ 背景音乐添加成功")
                return str(final_video_with_audio)
            else:
                logger.warning(f"背景音乐添加失败: {result.stderr}")
                return video_path

        except Exception as e:
            logger.error(f"背景音乐添加过程中发生错误: {e}")
            return video_path

    def get_video_duration(self, video_path):
        """
        获取视频时长
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            float: 视频时长（秒）
        """
        command = [
            "ffprobe",
            "-v",
            "error",
            "-show_entries",
            "format=duration",
            "-of",
            "default=noprint_wrappers=1:nokey=1",
            str(video_path),
        ]

        try:
            duration_str = subprocess.check_output(command, text=True, encoding="utf-8").strip()
            return float(duration_str)
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning(f"无法获取视频时长: {video_path}，使用默认值5秒")
            return 5.0


