#!/usr/bin/env python

import argparse
import os
import re
import time

import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Record smooth scrolling video of a webpage")
    parser.add_argument("url", type=str, help="URL to record")
    parser.add_argument("--output-path", type=str, default="output.mp4", help="Output file path or directory")
    parser.add_argument("--duration", type=int, default=9, help="Recording duration in seconds")
    parser.add_argument("--width", type=int, default=1920, help="Video width in pixels")
    parser.add_argument("--height", type=int, default=1080, help="Video height in pixels")
    parser.add_argument("--fps", type=int, default=15, help="Frames per second")
    parser.add_argument("--smooth-factor", type=float, default=0.2, help="Smoothness of scrolling (lower is smoother)")
    parser.add_argument("--title-focus", type=int, default=1, help="Time to focus on title in seconds")
    parser.add_argument("--star-focus", type=int, default=2, help="Time to focus on star section in seconds")
    parser.add_argument(
        "--zoom-factor", type=float, default=2, help="Zoom factor for focus areas (1.0 means no zoom)"
    )
    parser.add_argument("--readme-pause", type=float, default=1.0, help="Time to pause at readme section in seconds")
    return parser.parse_args()


def setup_driver(width, height):
    """Set up the Selenium WebDriver with appropriate options."""
    chrome_options = Options()
    # 使用headless模式避免浏览器窗口意外关闭
    chrome_options.add_argument("--headless")
    chrome_options.add_argument(f"--window-size={width},{height}")
    chrome_options.add_argument("--disable-infobars")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-browser-side-navigation")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")

    # 增加稳定性设置
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)

    try:
        driver_path = ChromeDriverManager().install()
        # 确保路径指向正确的chromedriver可执行文件
        if "THIRD_PARTY_NOTICES" in driver_path:
            # 如果路径包含THIRD_PARTY_NOTICES，尝试找到正确的chromedriver
            import os
            driver_dir = os.path.dirname(driver_path)
            for file in os.listdir(driver_dir):
                if file.startswith("chromedriver") and not file.endswith(".txt"):
                    driver_path = os.path.join(driver_dir, file)
                    break
        
        # 自动修复权限问题
        import os
        if os.path.exists(driver_path):
            try:
                # 尝试给chromedriver添加执行权限
                os.chmod(driver_path, 0o755)
                print(f"✅ 已修复chromedriver权限: {driver_path}")
            except Exception as perm_error:
                print(f"⚠️  无法修复权限，但继续尝试: {perm_error}")
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver
    except Exception as e:
        print(f"设置Chrome驱动时出错: {e}")
        # 尝试更通用的错误处理
        if "permission" in str(e).lower() or "executable" in str(e).lower():
            print("💡 提示: 如果遇到权限问题，请手动运行: chmod +x <chromedriver路径>")
        raise


def get_scroll_height(driver):
    """Get the total scrollable height of the page."""
    return driver.execute_script(
        "return Math.max("
        "document.body.scrollHeight, "
        "document.documentElement.scrollHeight, "
        "document.body.offsetHeight, "
        "document.documentElement.offsetHeight, "
        "document.body.clientHeight, "
        "document.documentElement.clientHeight);"
    )


def center_element_in_viewport(driver, element, position="center"):
    """
    Position an element in the viewport.
    position can be: 'center', 'left', 'right'
    """
    # Get element position
    rect = driver.execute_script(
        """
        var rect = arguments[0].getBoundingClientRect();
        return {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
        };
    """,
        element,
    )

    # Get viewport dimensions and scroll position
    viewport = driver.execute_script(
        """
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            scrollX: window.scrollX,
            scrollY: window.scrollY
        };
    """
    )

    # Calculate element center coordinates relative to viewport
    element_center_x = rect["left"] + rect["width"] / 2
    element_center_y = rect["top"] + rect["height"] / 2

    # Calculate viewport center
    viewport_center_x = viewport["width"] / 2
    viewport_center_y = viewport["height"] / 2

    # Calculate offset based on requested position
    if position == "left":
        # Position element at left side of screen
        target_x = viewport["width"] * 0.15
        offset_x = element_center_x - target_x
    elif position == "right":
        # Position element at right side of screen
        target_x = viewport["width"] * 0.7
        offset_x = element_center_x - target_x
    else:  # center
        offset_x = element_center_x - viewport_center_x

    # Always center vertically
    offset_y = element_center_y - viewport_center_y

    # Calculate target scroll position
    target_scroll_x = viewport["scrollX"] + offset_x
    target_scroll_y = viewport["scrollY"] + offset_y

    # Apply precise scroll
    print(f"Positioning element '{position}' (offset X: {offset_x:.1f}px, Y: {offset_y:.1f}px)...")
    driver.execute_script(
        f"""
    window.scrollTo({{
        top: {target_scroll_y},
        left: {target_scroll_x},
        behavior: 'smooth'
    }});
    """
    )

    # Wait for scroll to complete - further reduce wait time
    time.sleep(0.2)

    # Return viewport center for transform origin
    return {
        "center_x": viewport_center_x,
        "center_y": viewport_center_y,
        "element_center_x": element_center_x,
        "element_center_y": element_center_y,
    }


def apply_highlight(driver, element):
    """Apply highlight effect to an element with red color for better visibility."""
    driver.execute_script(
        """
    let el = arguments[0];
    // 使用红色高亮，增加可见度
    el.style.outline = '3px solid rgba(255, 0, 0, 0.9)';
    el.style.outlineOffset = '3px';
    el.style.transition = 'outline 0.5s ease-in-out, box-shadow 0.5s ease-in-out, transform 0.3s ease';

    // 添加脉冲动画效果
    function pulseOutline() {
        // 在深红色和亮红色之间切换
        el.style.outline = '3px solid rgba(255, 0, 0, 0.95)';
        setTimeout(() => {
            el.style.outline = '3px solid rgba(255, 30, 30, 0.8)';
            setTimeout(pulseOutline, 600);
        }, 600);
    }
    pulseOutline();

    // 添加更明显的阴影效果
    if (!el._originalBoxShadow) {
        el._originalBoxShadow = getComputedStyle(el).boxShadow;
    }
    el.style.boxShadow = '0 0 15px rgba(255, 0, 0, 0.7)';

    // 添加微小缩放效果增强突出显示
    if (!el._originalTransform) {
        el._originalTransform = getComputedStyle(el).transform;
    }
    el.style.transform = 'scale(1.02)';
    """,
        element,
    )


def remove_highlight(driver, element):
    """Remove highlight effect from an element."""
    driver.execute_script(
        """
    let el = arguments[0];
    el.style.outline = '0px solid rgba(255, 0, 0, 0)';
    el.style.boxShadow = el._originalBoxShadow || 'none';
    el.style.transform = el._originalTransform || '';
    """,
        element,
    )


def capture_zoom_in(driver, element, duration, fps, width, height, zoom_factor, position="center"):
    """Capture frames of zooming in on an element with exact positioning."""
    frames = []
    transition_frames = int(duration * fps)

    try:
        # Use a consistent zoom factor
        safe_zoom_factor = base_zoom = max(1.0, min(zoom_factor, 2.0))

        # Position element in viewport based on requested position
        viewport_data = center_element_in_viewport(driver, element, position)

        # Get more precise positioning for the transform
        precise_center_x = viewport_data["center_x"]
        precise_center_y = viewport_data["center_y"]

        # Apply highlight
        apply_highlight(driver, element)

        # Get original transform for later restoration
        original_transform = driver.execute_script("return document.body.style.transform || '';")

        # Setup for zoom transition effect with cubic-bezier for smooth easing
        driver.execute_script(
            f"""
        // Apply transform origin at viewport center
        document.body.style.transformOrigin = '{precise_center_x}px {precise_center_y}px';
        document.body.style.transition = 'transform {duration}s cubic-bezier(0.34, 1.56, 0.64, 1)';
        """
        )

        print(f"Capturing zoom-in animation: {transition_frames} frames over {duration:.1f}s")

        # Start with scale 1
        driver.execute_script("document.body.style.transform = 'scale(1)';")

        # Get element's pre-zoom position again for precise calculations
        element_rect = driver.execute_script(
            """
            var rect = arguments[0].getBoundingClientRect();
            return {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height
            };
        """,
            element,
        )

        # Calculate where the element will be after zoom
        element_center_x = element_rect["left"] + element_rect["width"] / 2
        element_center_y = element_rect["top"] + element_rect["height"] / 2

        # Calculate the offset needed to keep element centered after zoom
        dx = element_center_x - precise_center_x
        dy = element_center_y - precise_center_y

        # 记录开始时间
        start_time = time.time()

        # 计算每帧的理想时间间隔
        frame_interval = duration / transition_frames

        # Capture frames during zoom-in with eased timing
        for i in range(transition_frames):
            # 计算当前帧应该在的时间点
            target_time = start_time + i * frame_interval

            # Calculate progress with easing
            progress = i / (transition_frames - 1) if transition_frames > 1 else 1.0  # 0 to 1

            # Apply manual easing for smoother animation capture
            eased_progress = ease_in_out_cubic(progress)
            current_scale = 1 + (safe_zoom_factor - 1) * eased_progress

            # Calculate translation to keep element centered as it scales
            # The translation needs to increase as the scale increases
            translate_x = -dx * (current_scale - 1)
            translate_y = -dy * (current_scale - 1)

            # Apply current scale and translation for perfect centering
            driver.execute_script(
                f"""
            document.body.style.transform = 'scale({current_scale}) translate({translate_x}px, {translate_y}px)';
            """
            )

            # Capture current frame
            screenshot = driver.get_screenshot_as_png()
            nparr = np.frombuffer(screenshot, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            img = cv2.resize(img, (width, height))
            frames.append(img)

            # 计算需要等待的时间，确保按照精确间隔捕获帧
            current_time = time.time()
            wait_time = max(0.001, target_time - current_time)
            if wait_time > 0:
                time.sleep(wait_time)

        # Ensure we end at exact target zoom with perfect centering
        translate_x = -dx * (safe_zoom_factor - 1)
        translate_y = -dy * (safe_zoom_factor - 1)
        driver.execute_script(
            f"""
        document.body.style.transform = 'scale({safe_zoom_factor}) translate({translate_x}px, {translate_y}px)';
        """
        )

        # 返回实际使用的缩放系数，供其他函数保持一致性
        return frames, safe_zoom_factor

    except Exception as e:
        print(f"Error during zoom in: {e}")
        import traceback

        traceback.print_exc()

    return frames, zoom_factor


def capture_steady_zoom(driver, element, duration, fps, width, height, zoom_factor):
    """Capture frames of a steady zoomed element with subtle animation."""
    frames = []
    total_frames = int(duration * fps)

    try:
        # Get current transform to determine centering translation
        current_transform = driver.execute_script("return document.body.style.transform || '';")

        # Extract current translation if any
        translate_match = re.search(r"translate\(([-\d.]+)px, ([-\d.]+)px\)", current_transform)
        base_translate_x = float(translate_match.group(1)) if translate_match else 0
        base_translate_y = float(translate_match.group(2)) if translate_match else 0

        print(f"Capturing steady zoom: {total_frames} frames over {duration:.1f}s")

        # 记录开始时间
        start_time = time.time()

        # 计算每帧的理想时间间隔
        frame_interval = duration / total_frames

        # Capture frames of fully zoomed element with a fixed position (no animation)
        for i in range(total_frames):
            # 计算当前帧应该在的时间点
            target_time = start_time + i * frame_interval

            # Keep position fixed to eliminate any "floating" effect that causes jitter
            driver.execute_script(
                f"""
            document.body.style.transform = 'scale({zoom_factor}) translate({base_translate_x}px, {base_translate_y}px)';
            """
            )

            screenshot = driver.get_screenshot_as_png()
            nparr = np.frombuffer(screenshot, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            img = cv2.resize(img, (width, height))
            frames.append(img)

            # 计算需要等待的时间，确保按照精确间隔捕获帧
            current_time = time.time()
            wait_time = max(0.001, target_time - current_time)
            if wait_time > 0:
                time.sleep(wait_time)

    except Exception as e:
        print(f"Error during steady zoom: {e}")

    return frames


def capture_pan_between_elements(
    driver,
    from_element,
    to_element,
    duration,
    fps,
    width,
    height,
    zoom_factor,
    from_position="center",
    to_position="center",
):
    """Capture frames of panning from one element to another while zoomed in."""
    frames = []
    transition_frames = int(duration * fps)

    try:
        # Get current transform to determine centering translation
        current_transform = driver.execute_script("return document.body.style.transform || '';")

        # Extract current translation if any
        translate_match = re.search(r"translate\(([-\d.]+)px, ([-\d.]+)px\)", current_transform)
        initial_translate_x = float(translate_match.group(1)) if translate_match else 0
        initial_translate_y = float(translate_match.group(2)) if translate_match else 0

        # Get viewport dimensions
        viewport = driver.execute_script(
            """
            return {
                width: window.innerWidth,
                height: window.innerHeight,
                scrollX: window.scrollX,
                scrollY: window.scrollY
            };
        """
        )

        # 计算视口中心
        viewport_center_x = viewport["width"] / 2
        viewport_center_y = viewport["height"] / 2

        # 使用执行时的实际位置获取元素位置
        from_rect = driver.execute_script(
            """
            var rect = arguments[0].getBoundingClientRect();
            return {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height,
                bottom: rect.bottom,
                right: rect.right
            };
        """,
            from_element,
        )

        to_rect = driver.execute_script(
            """
            var rect = arguments[0].getBoundingClientRect();
            return {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height,
                bottom: rect.bottom,
                right: rect.right
            };
        """,
            to_element,
        )

        # 打印元素位置信息
        print(
            f"源元素位置: 左={from_rect['left']:.1f}, 上={from_rect['top']:.1f}, 宽={from_rect['width']:.1f}, 高={from_rect['height']:.1f}"
        )
        print(
            f"目标元素位置: 左={to_rect['left']:.1f}, 上={to_rect['top']:.1f}, 宽={to_rect['width']:.1f}, 高={to_rect['height']:.1f}"
        )

        # 计算元素中心点
        from_center_x = from_rect["left"] + from_rect["width"] / 2
        from_center_y = from_rect["top"] + from_rect["height"] / 2

        to_center_x = to_rect["left"] + to_rect["width"] / 2
        to_center_y = to_rect["top"] + to_rect["height"] / 2

        # 决定目标水平位置
        if to_position == "left":
            # 目标是使目标元素位于左侧
            target_x = viewport_center_x - to_rect["width"] * 0.3
            desired_translate_x = initial_translate_x - (to_rect["left"] - target_x) / zoom_factor
        elif to_position == "right":
            # 目标是使目标元素位于右侧
            target_x = viewport["width"] * 0.7 - to_rect["width"] / 2
            desired_translate_x = initial_translate_x - (to_rect["left"] - target_x) / zoom_factor
        else:  # center
            # 目标是使目标元素居中
            full_center_shift = (to_center_x - viewport_center_x) / zoom_factor
            desired_translate_x = initial_translate_x - full_center_shift

        # 垂直方向保持当前位置
        translate_y = initial_translate_y

        # 记录最终的目标平移值
        target_translate_x = desired_translate_x
        target_translate_y = translate_y

        # 计算平移变化量
        dx = target_translate_x - initial_translate_x
        dy = 0  # 垂直方向不移动

        print(
            f"平移：从 ({initial_translate_x:.1f}, {initial_translate_y:.1f}) 到 ({target_translate_x:.1f}, {target_translate_y:.1f})"
        )
        print(f"平移变化量: dx={dx:.1f}, dy={dy:.1f}")
        print(f"Capturing pan animation: {transition_frames} frames over {duration:.1f}s")

        # Highlight target element, remove from source
        apply_highlight(driver, to_element)
        remove_highlight(driver, from_element)

        # 记录开始时间
        start_time = time.time()

        # 计算每帧的理想时间间隔
        frame_interval = duration / transition_frames

        # Capture frames during pan with easing
        for i in range(transition_frames):
            # 计算当前帧应该在的时间点
            target_time = start_time + i * frame_interval

            progress = i / (transition_frames - 1) if transition_frames > 1 else 1.0  # 0 to 1

            # Apply easing function for smooth motion
            ease_progress = ease_in_out_cubic(progress)

            # Calculate current translation
            current_x = initial_translate_x + dx * ease_progress
            current_y = initial_translate_y + dy * ease_progress  # 垂直位置不变

            # Apply the new translation while maintaining zoom
            driver.execute_script(
                f"""
            document.body.style.transform = 'scale({zoom_factor}) translate({current_x}px, {current_y}px)';
            """
            )

            # Capture current frame
            screenshot = driver.get_screenshot_as_png()
            nparr = np.frombuffer(screenshot, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            img = cv2.resize(img, (width, height))
            frames.append(img)

            # 计算需要等待的时间，确保按照精确间隔捕获帧
            current_time = time.time()
            wait_time = max(0.001, target_time - current_time)
            if wait_time > 0:
                time.sleep(wait_time)

        # Ensure we end at exact target position
        driver.execute_script(
            f"""
        document.body.style.transform = 'scale({zoom_factor}) translate({target_translate_x}px, {target_translate_y}px)';
        """
        )

    except Exception as e:
        print(f"Error during pan: {e}")
        import traceback

        traceback.print_exc()

    return frames


def capture_zoom_out(driver, element, duration, fps, width, height, zoom_factor):
    """Capture frames of zooming out from an element."""
    frames = []
    transition_frames = int(duration * fps)

    try:
        # Get current transform to determine centering translation
        current_transform = driver.execute_script("return document.body.style.transform || '';")

        # Extract current translation if any
        translate_match = re.search(r"translate\(([-\d.]+)px, ([-\d.]+)px\)", current_transform)
        initial_translate_x = float(translate_match.group(1)) if translate_match else 0
        initial_translate_y = float(translate_match.group(2)) if translate_match else 0

        # Remove highlight with animation
        remove_highlight(driver, element)

        # Start zoom-out transition with improved easing
        driver.execute_script(
            f"""
        document.body.style.transition = 'transform {duration}s cubic-bezier(0.34, 1.56, 0.64, 1)';
        """
        )

        print(f"Capturing zoom-out animation: {transition_frames} frames over {duration:.1f}s")

        # Start with full zoom and current translation
        driver.execute_script(
            f"""
        document.body.style.transform = 'scale({zoom_factor}) translate({initial_translate_x}px, {initial_translate_y}px)';
        """
        )

        # 记录开始时间
        start_time = time.time()

        # 计算每帧的理想时间间隔
        frame_interval = duration / transition_frames

        # Capture frames during zoom-out with manual easing
        for i in range(transition_frames):
            # 计算当前帧应该在的时间点
            target_time = start_time + i * frame_interval

            progress = i / (transition_frames - 1) if transition_frames > 1 else 1.0  # 0 to 1

            # Apply easing for smoother animation capture
            eased_progress = ease_in_out_cubic(progress)

            # Calculate current scale
            current_scale = zoom_factor - (zoom_factor - 1) * eased_progress

            # Calculate current translation (gradually reduce to 0)
            current_translate_x = initial_translate_x * (1 - eased_progress)
            current_translate_y = initial_translate_y * (1 - eased_progress)

            # Apply current scale and translation
            driver.execute_script(
                f"""
            document.body.style.transform = 'scale({current_scale}) translate({current_translate_x}px, {current_translate_y}px)';
            """
            )

            # Capture current frame
            screenshot = driver.get_screenshot_as_png()
            nparr = np.frombuffer(screenshot, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            img = cv2.resize(img, (width, height))
            frames.append(img)

            # 计算需要等待的时间，确保按照精确间隔捕获帧
            current_time = time.time()
            wait_time = max(0.001, target_time - current_time)
            if wait_time > 0:
                time.sleep(wait_time)

        # Ensure we end at exact scale 1 with no translation
        driver.execute_script("document.body.style.transform = 'scale(1) translate(0px, 0px)';")

        # Clean up styles
        driver.execute_script(
            """
        document.body.style.transformOrigin = '';
        document.body.style.transition = '';
        """
        )

        # Allow a moment for cleanup
        time.sleep(0.1)

    except Exception as e:
        print(f"Error during zoom out: {e}")

    return frames


def ease_in_out_cubic(t):
    """Cubic easing function for smoother animations."""
    if t < 0.5:
        return 4 * t * t * t
    else:
        p = 2 * t - 2
        return 0.5 * p * p * p + 1


def capture_frames_with_focus(
    driver, duration, fps, width, height, smooth_factor, title_focus, star_focus, zoom_factor, readme_pause=1.0
):
    """Capture frames while smoothly scrolling the webpage with focus on title and repository details."""
    frames = []

    # Calculate durations for each phase - longer animations for slower playback
    zoom_in_duration = min(1.0, duration * 0.08)  # Time to zoom in
    pan_duration = min(1.2, duration * 0.1)  # Time to pan from title to details
    zoom_out_duration = min(1.0, duration * 0.08)  # Time to zoom out

    # Ensure focus times are respected
    title_focus_duration = max(0.5, title_focus)  # At least 0.5 seconds
    details_focus_duration = max(0.5, star_focus)  # At least 0.5 seconds (reusing star_focus parameter)

    # Remaining time for scrolling
    total_focus_time = (
        title_focus_duration + details_focus_duration + zoom_in_duration + pan_duration + zoom_out_duration
    )
    remaining_duration = max(0, duration - total_focus_time)

    try:
        # Find title element
        title_element = None
        try:
            print("Finding title element...")
            # Find the repository title element with multiple selectors
            title_element = driver.find_element(
                By.CSS_SELECTOR,
                "h1.AppHeader-context-item-label, strong[itemprop='name'], h1[class*='Heading'], .repo-header h1, a[data-pjax='#repo-content-pjax-container']",
            )
        except Exception as e:
            print(f"Could not find title with primary selector: {e}")
            try:
                # Fallback to XPath with broader match
                title_element = driver.find_element(
                    By.XPATH,
                    "//strong[contains(@class, 'repo')]|//h1[contains(text(), '/')]|//a[contains(@class, 'repo')]|//a[contains(@href, '/blob/')]",
                )
            except Exception as e2:
                print(f"Could not find title with fallback selector: {e2}")

        # Find repository details container instead of star element
        details_element = None
        try:
            print("Finding repository details container...")
            # 查找仓库详情容器
            details_element = driver.find_element(
                By.CSS_SELECTOR, "#repository-details-container, .repository-content, #readme, .markdown-body"
            )
            if not details_element:
                # 备用选择器
                details_element = driver.find_element(
                    By.CSS_SELECTOR, ".Box-body, .markdown-body, .BorderGrid, .Box--condensed"
                )
        except Exception as e:
            print(f"Could not find repository details container: {e}")
            try:
                # 尝试使用更宽泛的选择器
                details_element = driver.find_element(
                    By.XPATH,
                    "//*[@id='readme'] | //*[contains(@class, 'markdown-body')] | //article[contains(@class, 'markdown')]",
                )
            except Exception as e2:
                print(f"Could not find repository details with fallback selector: {e2}")

        # If we couldn't find both elements, fallback to normal scrolling
        if not title_element or not details_element:
            print("Falling back to normal scrolling as elements could not be found")
            scroll_frames = capture_scrolling(driver, duration, fps, width, height, smooth_factor, readme_pause)
            frames.extend(scroll_frames)
            return frames

        # 跟踪实际使用的缩放系数（可能被capture_zoom_in调整）
        actual_zoom_factor = zoom_factor

        # 1. First position and zoom in on the title (centered)
        print(f"Focusing on title and zooming in ({zoom_in_duration:.1f}s)...")
        try:
            zoom_in_frames, actual_zoom_factor = capture_zoom_in(
                driver, title_element, zoom_in_duration, fps, width, height, zoom_factor, "center"
            )
            frames.extend(zoom_in_frames)
        except Exception as e:
            print(f"Zoom in failed: {e}")

        # 2. Stay on title for the specified focus time
        if title_focus_duration > 0:
            print(f"Staying on title for {title_focus_duration}s...")
            try:
                title_frames = capture_steady_zoom(
                    driver, title_element, title_focus_duration, fps, width, height, actual_zoom_factor
                )
                frames.extend(title_frames)
            except Exception as e:
                print(f"Error during title focus: {e}")

        # 3. Pan from title to repository details container while staying zoomed
        print(f"Panning from title to repository details ({pan_duration:.1f}s)...")
        try:
            # Use center positioning for both elements
            pan_frames = capture_pan_between_elements(
                driver,
                title_element,
                details_element,
                pan_duration,
                fps,
                width,
                height,
                actual_zoom_factor,
                "center",
                "center",
            )
            frames.extend(pan_frames)
        except Exception as e:
            print(f"Error during pan: {e}")

        # 4. Stay on repository details for the specified focus time
        if details_focus_duration > 0:
            print(f"Staying on repository details for {details_focus_duration}s...")
            try:
                details_frames = capture_steady_zoom(
                    driver, details_element, details_focus_duration, fps, width, height, actual_zoom_factor
                )
                frames.extend(details_frames)
            except Exception as e:
                print(f"Error during details focus: {e}")

        # 5. Zoom out from repository details
        print(f"Zooming out ({zoom_out_duration:.1f}s)...")
        try:
            zoom_out_frames = capture_zoom_out(
                driver, details_element, zoom_out_duration, fps, width, height, actual_zoom_factor
            )
            frames.extend(zoom_out_frames)
        except Exception as e:
            print(f"Error during zoom out: {e}")

        # 6. Scroll the page for the remaining duration
        if remaining_duration > 0:
            print(f"Starting smooth scroll for remaining {remaining_duration:.1f} seconds...")
            try:
                # Reset scroll position to top
                driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(0.2)  # Reduced time for reset

                scroll_frames = capture_scrolling(driver, remaining_duration, fps, width, height, smooth_factor, readme_pause)
                frames.extend(scroll_frames)
            except Exception as e:
                print(f"Error during smooth scroll: {e}")

    except Exception as e:
        print(f"捕获帧过程中出错: {e}")
        import traceback

        traceback.print_exc()

    return frames


def capture_scrolling(driver, duration, fps, width, height, smooth_factor, readme_pause=1.0):
    """Capture frames while smoothly scrolling the webpage."""
    # Calculate total frames to capture
    total_frames = int(duration * fps)
    frames = []

    # Get the total scrollable height
    scroll_height = get_scroll_height(driver)

    # 调整滚动速度 - 降低滚动距离和速度
    adjusted_scroll_height = scroll_height * 0.5  # 只滚动页面的50%
    
    # 增加平滑系数，使滚动更加缓慢
    adjusted_smooth_factor = smooth_factor * 0.3  # 降低平滑系数，使滚动更慢

    # Calculate scroll step per frame
    step = adjusted_scroll_height / total_frames

    # Current scroll position and velocity
    position = 0
    velocity = 0

    print(f"Scrolling {adjusted_scroll_height:.0f}px over {duration}s at {fps}fps")

    # 计算每帧的实际捕获间隔，确保总时长符合预期
    frame_interval = duration / total_frames

    # 记录开始时间
    start_time = time.time()

    # 查找readme元素
    readme_element = None
    try:
        readme_element = driver.find_element(By.CSS_SELECTOR, "[data-content='README']")
    except:
        print("Could not find readme element")

    # 记录是否已经暂停过
    has_paused = False
    # 记录暂停后的新起始位置
    new_start_position = None
    # 记录暂停结束后的帧计数
    frames_after_pause = 0
    # 定义重启加速时间（帧数）
    restart_acceleration_frames = int(fps * 1.5)  # 1.5秒的加速时间

    for i in range(total_frames):
        # 计算当前帧应该在的时间点
        target_time = start_time + i * frame_interval

        # 如果已经暂停过，使用新的起始位置和渐进式加速
        if has_paused and new_start_position is not None:
            frames_after_pause = i - pause_frame
            
            # 计算渐进式加速系数，从0逐渐增加到1
            acceleration_factor = min(1.0, frames_after_pause / restart_acceleration_frames)
            
            # 使用渐进式速度计算下一个位置
            target = new_start_position + frames_after_pause * step * acceleration_factor
            
            # 使用更平滑的加速
            velocity += (target - position) * adjusted_smooth_factor * 0.3 * (0.2 + 0.8 * acceleration_factor)
            position += velocity
        else:
            # 使用原始速度计算
            target = i * step
            # 降低速度变化率，使滚动更加平滑
            velocity += (target - position) * adjusted_smooth_factor * 0.5
            position += velocity

        # Scroll to position
        driver.execute_script(f"window.scrollTo(0, {position});")

        # 检查是否需要暂停在readme处
        if readme_element and not has_paused:
            try:
                # 获取readme元素的位置
                readme_rect = driver.execute_script(
                    """
                    var rect = arguments[0].getBoundingClientRect();
                    return {
                        top: rect.top,
                        bottom: rect.bottom
                    };
                    """,
                    readme_element
                )
                
                # 如果readme在视口顶部附近，则暂停
                if readme_rect['top'] < 50 and readme_rect['top'] > -50:  # 50px的容差
                    print(f"Pausing at readme for {readme_pause} seconds...")
                    # 记录当前帧号
                    pause_frame = i
                    # 记录暂停时的位置
                    pause_position = position
                    
                    # 捕获暂停期间的帧
                    pause_frames = int(readme_pause * fps)
                    for _ in range(pause_frames):
                        screenshot = driver.get_screenshot_as_png()
                        nparr = np.frombuffer(screenshot, np.uint8)
                        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                        img = cv2.resize(img, (width, height))
                        frames.append(img)
                    
                    has_paused = True
                    # 更新开始时间以补偿暂停时间
                    start_time += readme_pause
                    # 设置新的起始位置为当前暂停位置
                    new_start_position = pause_position
                    # 重置速度
                    velocity = 0
                    # 重置暂停后帧计数
                    frames_after_pause = 0
                    print("Resuming scroll with slow acceleration...")
            except Exception as e:
                print(f"Error checking readme position: {e}")

        # Capture screenshot
        screenshot = driver.get_screenshot_as_png()
        nparr = np.frombuffer(screenshot, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        img = cv2.resize(img, (width, height))
        frames.append(img)

        # 计算需要等待的时间，确保按照精确间隔捕获帧
        current_time = time.time()
        wait_time = max(0.001, target_time - current_time)
        if wait_time > 0:
            time.sleep(wait_time)

        # Print progress
        if i % fps == 0 or i == total_frames - 1:
            elapsed = time.time() - start_time
            print(f"Captured {i+1}/{total_frames} frames ({(i+1)/total_frames*100:.1f}%), elapsed: {elapsed:.1f}s")

    # 显示实际捕获时长
    total_time = time.time() - start_time
    print(f"Total capture time: {total_time:.2f}s for requested duration: {duration:.2f}s")

    return frames


def create_video(frames, output_path, fps, width, height):
    """Create a video file from captured frames."""
    # Ensure directory exists
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Use the same fps as captured to maintain original timing
    # 不再降低输出视频的帧率，确保视频时长与录制时长一致
    output_fps = fps
    print(f"Creating video with FPS: {output_fps}")

    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    out = cv2.VideoWriter(output_path, fourcc, output_fps, (width, height))

    # Write frames to video
    for frame in frames:
        out.write(frame)

    # Release resources
    out.release()
    print(f"Video saved to {output_path} (duration: {len(frames)/output_fps:.1f}s)")


def main():
    """Main function to run the script."""
    args = parse_arguments()

    # Setup WebDriver
    print("Setting up WebDriver...")
    driver = None
    try:
        driver = setup_driver(args.width, args.height)

        # Navigate to URL
        print(f"Loading {args.url}...")
        driver.get(args.url)

        # Wait for page to load
        print("等待页面加载完成...")
        time.sleep(3)

        # Capture frames with focus on title and repository details
        print(f"Starting capture for total of {args.duration} seconds at {args.fps} FPS...")
        frames = capture_frames_with_focus(
            driver,
            args.duration,
            args.fps,
            args.width,
            args.height,
            args.smooth_factor,
            args.title_focus,
            args.star_focus,
            args.zoom_factor,
            args.readme_pause
        )

        # Create video if frames were captured
        if frames:
            print(f"创建视频（共 {len(frames)} 帧）...")
            create_video(frames, args.output_path, args.fps, args.width, args.height)
            print(f"视频已保存到 {args.output_path}")
        else:
            print("未能捕获任何帧，无法创建视频")

    except Exception as e:
        print(f"执行过程中出错: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # Clean up
        if driver:
            try:
                driver.quit()
                print("WebDriver已关闭")
            except:
                print("关闭WebDriver时出错")
        print("Done.")


if __name__ == "__main__":
    main()
