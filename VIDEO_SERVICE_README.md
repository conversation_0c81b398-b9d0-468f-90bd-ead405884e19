# Feynman视频生成服务

基于AI的智能视频生成服务，支持通过URL、文件或聊天内容自动生成高质量的视频。

## 功能特点

- 🌐 **URL输入**: 支持GitHub项目、ArXiv论文、普通网页等
- 📁 **文件输入**: 支持各种文档格式（PDF、Markdown、图片等）
- 💬 **聊天输入**: 支持自然语言描述生成视频内容
- 🎯 **智能意图识别**: 自动识别输入内容的类型和目的
- 🎬 **自动视频生成**: 生成包含动画、字幕、背景音乐的视频
- 📊 **多种输出格式**: 支持MP4、AVI等视频格式

## 快速开始

### 1. 启动服务

```bash
# 方法1: 使用启动脚本
./start_video_service.sh

# 方法2: 直接使用uv运行
TAVILY_API_KEY=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 uv run python video_generation_service.py
```

### 2. 访问API文档

服务启动后，访问以下地址：
- 服务地址: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## API接口

### 1. 生成视频 (POST /generate_video)

**请求示例:**

```bash
curl -X POST "http://localhost:8000/generate_video" \
     -H "Content-Type: application/json" \
     -d '{
       "url": "https://danielchasehooper.com/posts/syscall-build-snooping/",
       "chat": "分析这个网页内容，为技术爱好者提供核心信息的总结"
     }'
```

**请求参数:**
- `url` (可选): 网页URL
- `file_path` (可选): 本地文件路径
- `chat` (可选): 聊天内容描述
- `output_dir` (可选): 输出目录，默认为"output"

**响应示例:**
```json
{
  "success": true,
  "video_name": "webpage_general_20250816_110902.mp4",
  "video_title": "系统调用构建窥探技术分析",
  "project_dir": "output/temp_abc12345",
  "timestamp": "2025-08-16T11:09:02.054"
}
```

### 2. 上传文件并生成视频 (POST /upload_and_generate)

**请求示例:**
```bash
curl -X POST "http://localhost:8000/upload_and_generate" \
     -F "file=@document.pdf" \
     -F "chat=分析这个PDF文档的主要内容"
```

### 3. 健康检查 (GET /health)

```bash
curl http://localhost:8000/health
```

## 使用示例

### 示例1: 通过URL生成视频

```python
import requests

data = {
    "url": "https://danielchasehooper.com/posts/syscall-build-snooping/",
    "chat": "分析这个网页内容，为技术爱好者提供核心信息的总结"
}

response = requests.post("http://localhost:8000/generate_video", json=data)
result = response.json()

if result["success"]:
    print(f"视频名称: {result['video_name']}")
    print(f"视频标题: {result['video_title']}")
    print(f"项目目录: {result['project_dir']}")
else:
    print(f"生成失败: {result['error_message']}")
```

### 示例2: 通过聊天内容生成视频

```python
import requests

data = {
    "chat": "为初学者介绍Python编程语言的基础概念，包括变量、函数、类等核心概念"
}

response = requests.post("http://localhost:8000/generate_video", json=data)
result = response.json()

if result["success"]:
    print(f"视频名称: {result['video_name']}")
    print(f"视频标题: {result['video_title']}")
else:
    print(f"生成失败: {result['error_message']}")
```

### 示例3: 通过文件路径生成视频

```python
import requests

data = {
    "file_path": "README.md",
    "chat": "分析这个项目的README文件，介绍项目的主要功能和特点"
}

response = requests.post("http://localhost:8000/generate_video", json=data)
result = response.json()

if result["success"]:
    print(f"视频名称: {result['video_name']}")
    print(f"视频标题: {result['video_title']}")
else:
    print(f"生成失败: {result['error_message']}")
```

## 测试服务

运行测试脚本：

```bash
uv run python test_video_service.py
```

## 配置说明

服务使用 `config/config.yaml` 配置文件，主要配置项包括：

- **模型配置**: AI模型类型和API设置
- **输入配置**: URL、文件、聊天内容的处理方式
- **输出配置**: 视频质量、格式、背景音乐等
- **工作流配置**: 各个处理阶段的开关控制

## 输出文件

生成的视频和相关文件保存在 `output/` 目录下，包括：

- 生成的视频文件 (MP4格式)
- 项目配置文件
- 中间处理文件
- 日志文件

## 注意事项

1. **API密钥**: 确保设置了正确的TAVILY_API_KEY环境变量
2. **网络连接**: 服务需要访问外部API，确保网络连接正常
3. **存储空间**: 视频生成需要足够的磁盘空间
4. **处理时间**: 视频生成可能需要几分钟到几十分钟，取决于内容复杂度

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查配置文件是否存在
   - 检查依赖包是否安装完整
   - 检查端口8000是否被占用

2. **视频生成失败**
   - 检查API密钥是否正确
   - 检查网络连接是否正常
   - 查看日志文件获取详细错误信息

3. **依赖包问题**
   ```bash
   # 重新安装依赖
   uv sync
   ```

## 技术支持

如有问题，请查看：
- 服务日志输出
- `output/` 目录下的错误文件
- API文档: http://localhost:8000/docs

