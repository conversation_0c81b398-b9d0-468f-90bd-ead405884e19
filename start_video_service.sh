#!/bin/bash

# 视频生成服务启动脚本

echo "🚀 启动Feynman视频生成服务..."

# 设置环境变量
export TAVILY_API_KEY="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"

# 创建必要的目录
mkdir -p output
mkdir -p temp_uploads
mkdir -p config

# 检查配置文件是否存在
if [ ! -f "config/config.yaml" ]; then
    echo "❌ 配置文件 config/config.yaml 不存在"
    exit 1
fi

# 启动服务
echo "📡 服务将在 http://localhost:8000 启动"
echo "📖 API文档地址: http://localhost:8000/docs"
echo "🔧 健康检查: http://localhost:8000/health"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 使用uv运行服务
uv run python video_generation_service.py

