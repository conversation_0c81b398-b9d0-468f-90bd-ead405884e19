[{"type": "animate_mindmap", "description": "创建交互式思维导图，支持节点聚焦、子树展开和动态布局调整。\n使用场景: 展示知识结构和概念层次关系; 教学中的概念图解和思路梳理; 项目规划和任务分解可视化; 复杂信息的结构化展示", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "data_source": {"type": "object", "description": "思维导图数据（根 + 3 级）。数据结构必须严格遵守三级层级（不含根节点）。第一级和第二级节点应作为核心标题，文字简练，且其下的子节点数量不少于3个。作为内容主体的第三级节点，则用于展示详细的关键信息，文字应相对丰富完整，每个第二级节点下的第三级节点数量应控制在3至6个之间，以确保信息的完整性和布局的美观性。", "properties": {"标题": {"type": "string", "description": "根节点标题"}, "子章节": {"type": "array", "description": "第一级节点列表", "items": {"type": "object", "properties": {"标题": {"type": "string"}, "子章节": {"type": "array", "description": "第二级节点列表", "items": {"type": "object", "properties": {"标题": {"type": "string"}, "子章节": {"type": "array", "description": "第三级节点列表（叶子）", "items": {"type": "object", "properties": {"标题": {"type": "string"}}, "required": ["标题"], "additionalProperties": false}}}, "required": ["标题"], "additionalProperties": false}}}, "required": ["标题"], "additionalProperties": false}}}, "additionalProperties": false}, "layout_style": {"type": "string", "description": "布局样式。可选值：'balance'（平衡布局）, 'left_to_right'（从左到右）, 'right_to_left'（从右到左）", "enum": ["balance", "left_to_right", "right_to_left"], "default": "balance"}, "max_depth": {"type": "integer", "description": "显示的最大层级深度，超过这个深度的节点不会显示", "default": 3}, "focus_sequence": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}, "description": "focus_sequence参数可以创建引导式的节点聚焦动画，如果有需要强调的节点才用，没有可以不用。根结点无需作为第一个focus元素，如果有整体的总结性narration，可以将根节点作为最后一个focus元素。list中的元素为要聚焦的节点，包括节点文本和对应的旁白文本两个string。如果有focus_sequence元素，每个元素节点尽量配合对应的旁白，并和整体的narration要连贯，过渡流畅，不要有重复内容。", "default": "None"}, "narration": {"type": "string", "description": "在思维导图显示时播放的语音旁白文本，对思维导图进行整体介绍"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["title", "data_source", "narration"]}, "strict": true}, {"type": "animate_cover", "description": "创建专业美观的封面页面，采用BPE开篇动画风格，支持疑问句标题和副标题展示。\n使用黑底白字配色，橙色装饰线，专业优雅的入场动画效果。\n使用场景: 学术报告封面：展示研究主题和核心观点; 技术分享开场：突出技术问题和解决方案; 在线课程封面：显示课程主题和学习收益; 企业培训页面：体现专业性和内容重点; 社交媒体内容：吸引点击和分享\n注意事项: 采用BPE开篇动画风格：黑底白字，橙色装饰线，专业优雅入场; 主标题建议疑问句，激发观众好奇心，提高点击率; 副标题统一输入，可包含：具体数据(92%、100万倍) + 核心结论 + 权威机构; 副标题使用 • 分隔不同信息，控制在合理长度内; BPE色彩配置：主标题#FFFFFF，副标题#94A3B8，装饰线#F18F01; 动画时序：Write主标题(2s) → Create装饰线+FadeIn副标题(1.5s) → Wait展示(2s); 内容保持在场景中，退出由系统统一控制", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "主标题，建议疑问句形式激发好奇心"}, "subtitle": {"type": "string", "description": "副标题，可包含关键数据、结论、机构等信息", "default": "None"}, "background_style": {"type": "string", "description": "背景样式（预留参数，当前统一黑色背景）", "default": "gradient"}, "color_scheme": {"type": "string", "description": "配色方案（预留参数，当前统一BPE配色）", "default": "blue"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "narration": {"type": "string", "description": "封面展示时播放的语音旁白文本"}}, "additionalProperties": false, "required": ["title", "narration"]}, "strict": true}, {"type": "animate_emoji_flowchart", "description": "创建一个交互式emoji流程图动画，支持emoji列表或包含emoji的文本输入生成对应的流程图。\n支持多种布局和动画风格，自动调整大小以适应屏幕。\n使用场景: 展示工作流程或业务流程; 可视化步骤序列或决策树; 创建教学演示的流程图; 显示系统架构或数据流\n注意事项: 支持直接使用emoji列表或从文本中自动提取emoji和关键词; 布局算法会自动防止重叠并适应屏幕尺寸; 连接线会智能避开emoji边界; emoji列表长度会影响布局效果", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "emoji_list": {"type": "array", "items": {"type": "object"}, "description": "emoji和对应关键词的列表，格式为[(emoji, keyword), ...]。与content二选一"}, "content": {"type": "string", "description": "包含emoji和描述的流程文本。与emoji_list二选一"}, "layout": {"type": "string", "description": "布局样式。可选值：horizontal, vertical, circular, grid", "default": "horizontal"}, "connection_style": {"type": "string", "description": "连接线样式。可选值：arrow, line, curve", "default": "arrow"}, "animation_style": {"type": "string", "description": "动画风格。可选值：sequence, simultaneous, cascade", "default": "sequence"}, "max_count": {"type": "integer", "description": "最大emoji数量限制（仅在使用content时有效）", "default": 8}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}}, "additionalProperties": false, "required": ["title", "narration"]}, "strict": true}, {"type": "animate_deep_insight", "description": "创建现代化的深度洞察卡片动画，采用\"Sparkling Insight Card\"设计风格，支持闪光特效和优雅的悬浮动画\n使用场景: 深度洞察分析结果展示; 思维启发和认知增强; 学术论文的核心观点提炼; 商业分析的关键发现; 创新思维的火花展示\n注意事项: 完全复刻HTML版本的\"Sparkling Insight Card\"设计; 柔和的浅蓝色背景营造轻松氛围; 白色卡片背景配合圆角和阴影效果; 金色✨图标和顶部闪光条特效; 轻微倾斜和悬浮动画增强视觉吸引力; 支持智能文本换行和响应式布局", "parameters": {"type": "object", "properties": {"insights_data": {"type": "array", "description": "深度洞察数据列表，每个元素包含insight_title和insight_description字段"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "cards_per_screen": {"type": "integer", "description": "每屏显示的卡片数量（建议最多3个）", "default": 3}, "duration_per_card": {"type": "number", "description": "每张卡片的展示时长（秒）", "default": 4.0}, "theme": {"type": "string", "description": "主题风格。可选值：'sparkling'（闪光）, 'elegant'（优雅）, 'modern'（现代）", "default": "sparkling"}, "narration": {"type": "string", "description": "语音旁白内容"}}, "additionalProperties": false, "required": ["insights_data", "title", "narration"]}, "strict": true}, {"type": "animate_competitive_analysis", "description": "创建多阶段的竞品对比分析动画，包括1v1对比、维度雷达图分析和综合评估结论。\n使用场景: 产品竞品分析报告展示; 技术方案选型对比分析; 多维度评估结果可视化; 决策建议和结论展示\n注意事项: 动画分为三个主要阶段：1v1对比、雷达图分析、综合评估; 会根据替代方案数量自动调整对比展示; 雷达图将展示五个维度的对比分析; 最终会给出推荐指数和决策建议; 调用此函数会清除屏幕上的其他内容", "parameters": {"type": "object", "properties": {"analysis_data": {"type": "object", "description": "竞品分析数据，包含当前方案、替代方案、维度对比分析和综合评估"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "narration": {"type": "string", "description": "整体分析过程的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["analysis_data", "title", "narration"]}, "strict": true}, {"type": "animate_side_by_side_comparison", "description": "创建左右两栏并排比较的布局，可以比较不同类型的内容（文本、代码、图像等）。\n使用场景: 对比两种不同的代码实现或算法; 比较\"之前\"与\"之后\"的状态; 并列展示问题和解决方案; 对比两个图像、图表或设计\n注意事项: 对于'image'类型，content应为图像文件的本地路径; 对于'code'和'json'类型，会自动应用语法高亮; 内容会自动缩放以适应各自的窗格大小; 调用此函数会清除屏幕上的其他内容", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "left_content": {"type": "string", "description": "左侧内容（文本、代码字符串或图像路径）"}, "left_type": {"type": "string", "description": "左侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown'"}, "right_content": {"type": "string", "description": "右侧内容（文本、代码字符串或图像路径）"}, "right_type": {"type": "string", "description": "右侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown'"}, "left_title": {"type": "string", "description": "左侧窗格的标题", "default": "None"}, "right_title": {"type": "string", "description": "右侧窗格的标题", "default": "None"}, "transition": {"type": "string", "description": "内容入场的动画效果。可选值：'fadeIn', 'slideUp', 'none'", "default": "fadeIn"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["title", "left_content", "left_type", "right_content", "right_type", "narration"]}, "strict": true}, {"type": "animate_timeline", "description": "在Manim场景中创建并播放一个动态的、分段构建的时间轴动画。\n每个事件包含时间点（年份）、标题、描述文本，并可选择性地包含图片和自定义颜色。\n事件详情会交替显示在时间轴的上方和下方。\n使用场景: 展示项目里程碑或历史事件序列，具有更强的视觉吸引力; 解释一个过程的各个阶段，每个阶段有清晰的标题和描述; 可视化产品发布路线图，突出显示关键节点\n注意事项: 事件列表按数组中的顺序呈现，而非按年份排序; 时间轴展示时会先显示主轴，然后顺序显示每个事件; 可以为每个事件指定颜色，或使用默认颜色方案; emoji属性会尝试下载并显示对应图标，如果失败则仅显示简单节点; 如果时间轴节点较多（超过5个），每个节点单独的narration会使视频变得冗长，可以设置简洁的整体content_narration来介绍时间轴的背景信息", "parameters": {"type": "object", "properties": {"events": {"type": "array", "items": {"oneOf": [{"type": "object"}, {"type": "object"}]}, "description": "时间轴事件列表。每个元素可以是EventData对象或字典，包含year(时间点), title(标题), description(描述), emoji(表情), color(颜色), narration(事件旁白)等属性，其中narration会在当前时间轴事件显示的同时作为旁白播放。如果有全局的content_narration，那么事件的narration将被忽略。"}, "intro_narration": {"type": "string", "description": "时间轴动画开始时播放的语音旁白文本（会配合开始标题的显示动画一起播放）", "default": "None"}, "outro_narration": {"type": "string", "description": "时间轴动画结束时播放的语音旁白文本（会配合最后整个时间轴缩放动画一起播放）", "default": "None"}, "content_narration": {"type": "string", "description": "时间轴动画播放时同步播放的语音旁白文本（与每个事件的narration互斥）"}, "title": {"type": "string", "description": "时间轴标题"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["events", "content_narration", "title"]}, "strict": true}, {"type": "animate_chart", "description": "创建并播放条形图、折线图或雷达图的动画，支持单个或多个数据集。\n使用场景: 可视化数据趋势和比较（折线图、条形图）; 展示多个类别在不同指标上的表现（雷达图）; 在视频演示中动态呈现统计数据; 对比不同数据集之间的关系\n注意事项: 对于条形图和折线图，数据键作为x轴标签，值作为y轴数据点; 对于雷达图，数据键作为各个轴的标签，值作为该轴上的数据点; 对于条形图，动画效果是固定的动态增长效果，animation_style参数会被忽略。", "parameters": {"type": "object", "properties": {"chart_type": {"type": "string", "description": "图表类型。可选值：'bar'（条形图）, 'line'（折线图）, 'radar'（雷达图）", "enum": ["bar", "line", "radar"]}, "data": {"type": "array", "items": {"type": "object"}, "description": "图表数据。每个数据集为dict格式（如{\"A\":10,\"B\":20}）"}, "narration": {"type": "string", "description": "在图表显示时播放的语音旁白文本"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "animation_style": {"type": "string", "description": "图表入场动画。可选值：'fadeIn', 'grow', 'draw', 'update', 'dynamic', bar图优先用dynamic", "enum": ["fadeIn", "grow", "draw", "update", "dynamic"], "default": "fadeIn"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "dataset_names": {"type": "array", "items": {"type": "string"}, "description": "数据集名称列表"}, "x_label": {"type": "string", "description": "x轴标签（条形图和折线图有效）", "default": "None"}, "y_label": {"type": "string", "description": "y轴标签（条形图和折线图有效）", "default": "None"}}, "additionalProperties": false, "required": ["chart_type", "data", "narration", "title", "dataset_names"]}, "strict": true}, {"type": "animate_architecture_diagram", "description": "在Manim场景中直接播放一个架构图动画。该架构图是使用ExcalidrawToolkit根据文本描述生成的视频。\n通常用于全屏展示复杂的系统架构或流程图。\n使用场景: 可视化软件架构; 展示系统组件及其交互; 解释数据流或业务流程; 需要通过Excalidraw风格图表进行说明的场景\n注意事项: ExcalidrawToolkit会根据提供的文本描述自动生成架构图视频; 内容描述越详细，生成的架构图越准确; 生成的视频会自动缩放以适应场景大小; 此功能需要与外部ExcalidrawToolkit组件配合使用", "parameters": {"type": "object", "properties": {"content_description": {"type": "string", "description": "用于生成Excalidraw视频的文本描述，ExcalidrawToolkit将使用此描述来创建图表内容"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "narration": {"type": "string", "description": "播放动画时同步播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["content_description", "title", "narration"]}, "strict": true}, {"type": "animate_step_by_step", "description": "在Manim场景中创建并播放一个分步骤讲解的动画。\n左侧展示步骤节点，每一步包含序号和操作描述，节点动态生成并向上移动。\n右侧展示每一步的具体内容（markdown格式），支持代码、列表、文本等。\n最后所有步骤节点缩放移动到画面正中，展示整体概念。\n使用场景: 教学演示中的分步骤讲解，如算法步骤、操作流程等; 产品功能介绍，逐步展示各个功能点; 项目开发流程演示，突出每个阶段的重点\n注意事项: 步骤按数组中的顺序呈现，每个步骤的内容支持完整的markdown语法; 左侧节点会动态生成并向上移动，右侧内容会淡出后显示新内容; 可以为每个步骤指定颜色，或使用默认颜色方案; 最后所有步骤节点会缩放移动到画面中央，形成整体概览; 与timeline的差别是，step_by_step需要讲解每个步骤中的详细内容，因此更适合例子讲解等场景，而timeline只是展示事件的整体脉络; title, subtitle与step title需要精简，不能字数太多，否则会导致文字重叠等问题", "parameters": {"type": "object", "properties": {"steps": {"type": "array", "description": "步骤列表，每个元素是一个dict，包括 step_number, title, content, color（可选）, narration（可选） 字段", "items": {"type": "object", "properties": {"step_number": {"type": "string", "description": "步骤序号"}, "title": {"type": "string", "description": "步骤标题"}, "content": {"type": "string", "description": "步骤内容（markdown）"}, "color": {"type": "string", "description": "节点颜色（可选）"}, "narration": {"type": "string", "description": "步骤旁白（可选）"}, "required": {"type": "string"}, "additionalProperties": {"type": "string"}}, "additionalProperties": false}}, "intro_narration": {"type": "string", "description": "开场介绍语音旁白文本"}, "outro_narration": {"type": "string", "description": "结尾总结语音旁白文本"}, "title": {"type": "string", "description": "整体标题"}, "subtitle": {"type": "string", "description": "副标题", "default": "None"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["steps", "intro_narration", "outro_narration", "title"]}, "strict": true}, {"type": "animate_math_step", "description": "在Manim场景中专业地展示数学解题步骤，支持逐步呈现和高亮强调。支持解析markdown格式的数学公式文本，\n智能识别变量、值、操作符等数学元素，并使用不同颜色和动画效果进行区分展示。\n使用场景: 数学定理证明的逐步展示; 方程求解过程的详细演示; 几何计算步骤的专业讲解; 代数运算过程的可视化\n注意事项: 支持标准markdown列表格式，每个`-`项为一个步骤; 数学公式用反引号包围，会被解析为数学表达式; 自动识别变量（字母）、值（数字）、操作符（+、-、=、/等）; 支持分数的专业显示格式; 每个步骤会逐步出现，带有淡入动画效果; 重要的数学元素会有高亮强调效果; 最终结果会有特殊的强调样式", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "解题标题，会显示在步骤上方"}, "steps_content": {"type": "string", "description": "markdown格式的数学步骤描述，每行一个步骤，支持数学公式"}, "narration": {"type": "string", "description": "在步骤展示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "step_delay": {"type": "number", "description": "每步之间的延迟时间（秒）", "default": 1.5}, "highlight_delay": {"type": "number", "description": "高亮效果的延迟时间（秒）", "default": 0.5}}, "additionalProperties": false, "required": ["title", "steps_content", "narration"]}, "strict": true}, {"type": "animate_counter", "description": "在Manim场景中创建并播放计数器动画，支持数字递增/递减计数器和曲线增长图两种类型。\n可以自定义起始值、目标值、标签、单位、动画时长和结束特效。\n使用场景: 显示随时间变化的数值，如统计数据、得分、加载进度等; 以曲线图形式展示增长或变化趋势，并在终点显示目标值; 强调关键性能指标(KPI)的变化\n注意事项: counter类型显示一个从起始值到目标值的数字动画; curve类型显示一条增长曲线，终点显示目标值; 结束特效在动画完成后应用，可增强视觉效果", "parameters": {"type": "object", "properties": {"target_value": {"type": "number", "description": "计数器动画的目标值"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "start_value": {"type": "number", "description": "计数器的起始值。仅用于'counter'类型", "default": 0}, "counter_type": {"type": "string", "description": "计数器类型。可选值：'counter'（数字计数器）, 'curve'（曲线增长图）", "default": "counter"}, "label": {"type": "string", "description": "计数器的标签或标题文本", "default": "None"}, "unit": {"type": "string", "description": "计数器的单位文本。对于'counter'类型，单位显示在数字之后；对于'curve'类型，单位与目标值一起显示在曲线末端", "default": "None"}, "duration": {"type": "number", "description": "计数器动画的持续时间（秒）", "default": 2.0}, "effect": {"type": "string", "description": "动画结束时应用的额外视觉效果。可选值：'flash', 'zoom'", "default": "None"}, "narration": {"type": "string", "description": "播放动画时同步播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["target_value", "title", "narration"]}, "strict": true}, {"type": "animate_markdown", "description": "将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。\n支持各种Markdown元素，包括标题、列表、代码块、表格等。\n使用场景: 展示格式化的文本内容，如教程说明、演示文稿; 在动画中展示结构化的信息，如列表和表格; 显示带有语法高亮的代码片段; 创建包含文本和图片的混合内容\n注意事项: 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等; 根据内容会自动调整大小以适应场景", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "Markdown格式的文本内容，尽量用格式化的元素增强语义丰富性，如标题、列表、加粗、斜体等，减少纯文本的使用。文字必须简洁精练，禁止直接展示长句形式的文本，详细信息可以通过旁白体现，屏幕上只能展示最重要的关键信息。"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "title": {"type": "string", "description": "当前内容的标题，必须言简意赅，概括内容要点。"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}}, "additionalProperties": false, "required": ["content", "title", "narration"]}, "strict": true}, {"type": "animate_video", "description": "在Manim场景中播放视频文件，支持可选的文本叠加和语音旁白。\n使用场景: 在教程或演示中展示屏幕录制或外部视频片段; 播放动画片段作为更复杂场景的一部分; 叠加解释性文本或字幕到视频内容上; 配合旁白同步视频演示\n注意事项: 视频文件必须存在且路径正确，否则函数会记录错误并返回; 视频会自动缩放以适应场景; 叠加文本会依次显示，每行之间有指定的延迟", "parameters": {"type": "object", "properties": {"video_path": {"type": "string", "description": "要播放的视频文件的本地路径"}, "title": {"type": "string", "description": "视频标题", "default": "None"}, "overlay_text": {"type": "string", "description": "可选的叠加文本，显示在视频之上。多行文本用\"\\n\"分隔", "default": "None"}, "overlay_animation_delay": {"type": "number", "description": "每行文本动画之间的延迟（秒）", "default": 1.0}, "narration": {"type": "string", "description": "在视频播放时同步播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["video_path", "narration"]}, "strict": true}, {"type": "animate_qa_cards", "description": "创建完全按照现代设计风格的QA卡片展示动画\n使用场景: 教育培训中的问答展示; 知识总结和回顾; 产品FAQ展示; 学术论文要点问答; 面试问题练习\n注意事项: 问题要简短，有洞察力; 回答言简意赅", "parameters": {"type": "object", "properties": {"qa_data": {"type": "array", "description": "QA数据列表，每个元素包含question和answer字段"}, "title": {"type": "string", "description": "展示标题"}, "cards_per_screen": {"type": "integer", "description": "每屏显示的卡片数量（建议最多3个）", "default": 3}, "duration_per_card": {"type": "number", "description": "每张卡片的展示时长（秒）", "default": 2.0}, "narration": {"type": "string", "description": "语音旁白内容"}}, "additionalProperties": false, "required": ["qa_data", "title", "narration"]}, "strict": true}, {"type": "animate_image", "description": "在Manim场景中显示图像，支持动态入场动画和叠加注释文本。图片会从右下角斜着入场，然后顺正并进行左右移动放大。\n使用场景: 展示需要详细解释的图片或截图; 展示图表、图解或可视化内容; 显示产品或界面截图并添加叠加注释说明; 突出显示图片中的关键要点\n注意事项: 图片文件必须存在且路径正确，否则会抛出FileNotFoundError; 图片动画分为四个步骤：1）从右下角斜着入场 2）移动到中心并放大 3）旋转顺正 4）左右移动并进一步放大; 如果提供annotation，它会以发光文字形式叠加显示在图片上，有半透明黑色背景; annotation支持字符串或列表格式，列表中每个元素会作为一行显示; 注释文字使用sequential动画逐行显示，提供更好的视觉效果", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "image_path": {"type": "string", "description": "要显示的图片的本地文件路径"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "narration": {"type": "string", "description": "在图片显示时播放的语音旁白文本"}, "annotation": {"type": "object", "description": "作为注释叠加显示在图片上的文本", "default": "None"}}, "additionalProperties": false, "required": ["title", "image_path", "narration"]}, "strict": true}, {"type": "animate_highlight_content", "description": "按顺序高亮一系列元素，或者对代码对象高亮特定行。\n使用场景: 逐步引导观众注意场景中的特定对象; 逐行解释代码片段，高亮当前讨论的行; 强调流程图或架构图中的特定组件序列\n注意事项: 被高亮的元素必须已经在场景中存在并且有指定的ID; 对于代码高亮，lines参数优先于highlight_type; 高亮效果是暂时的，结束后元素会恢复原始状态", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "elements": {"type": "array", "items": {"type": "string"}, "description": "要高亮的元素ID列表。如果指定lines参数，则此列表应只包含一个Code对象的ID"}, "highlight_type": {"type": "string", "description": "高亮效果类型。可选值：'flash'（闪烁）, 'box'（边框）, 'underline'（下划线）, 'color'（颜色变化）", "default": "box"}, "color": {"type": "string", "description": "高亮效果的颜色（十六进制或颜色名称）", "default": "#FFFF00"}, "duration_per_item": {"type": "number", "description": "每个元素或代码行组的高亮持续时间（秒）", "default": 1.0}, "lines": {"type": "string", "description": "要高亮的代码行范围，格式如\"1-3,5,7-10\"。如果提供此参数，elements应只有一个Code对象ID", "default": "None"}, "narration": {"type": "string", "description": "播放动画时同步播放的语音旁白文本", "default": "None"}}, "additionalProperties": false, "required": ["title", "elements"]}, "strict": true}, {"type": "animate_table", "description": "在Manim场景中创建并展示表格，支持逐行高亮显示效果。表格包含表头和数据行，单元格之间有间隙，支持黄色高亮状态。\n使用场景: 展示统计数据表格; 显示对比数据分析; 逐步突出重要数据行; 展示结构化信息\n注意事项: 表格内容不要超过8个字符; 表格会自动调整尺寸以适应屏幕; 高亮行会以黄色背景显示; 单元格之间的间距可以调整; 支持中文内容显示; 表格展示后会逐行进行高亮动画", "parameters": {"type": "object", "properties": {"headers": {"type": "array", "items": {"type": "string"}, "description": "表格表头列表"}, "data": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}, "description": "表格数据，每个子列表代表一行数据"}, "title": {"type": "string", "description": "表格标题"}, "highlight_rows": {"type": "array", "items": {"type": "integer"}, "description": "需要高亮的行索引列表（从0开始，不包括表头）", "default": []}, "cell_spacing": {"type": "number", "description": "单元格之间的间距", "default": 0.2}, "highlight_color": {"type": "string", "description": "高亮颜色，支持Manim颜色常量", "default": "YELLOW"}, "narration": {"type": "string", "description": "在表格展示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["headers", "data", "title", "narration"]}, "strict": true}, {"type": "animate_text_only", "description": "创建纯文本内容的动态展示动画，专门用于文字信息的呈现。支持清单、要点、步骤等文本内容的动态显示，\n包含渐变背景、发光字体效果、逐字显示动画等视觉特效，适合纯文本内容的精美展示。\n使用场景: 展示要点清单、任务列表、检查清单等文本内容; 创建纯文字的动态内容展示，如学习要点、工作计划等; 制作带有视觉特效的文本演示，突出重要信息; 文字内容的分步展示和强调\n注意事项: 专门用于纯文本内容的动态展示，不涉及图片或视频; 支持最多5个文本项目的展示; 每个文本项包含主文本、标签，颜色自动分配; 具有发光字体效果和逐字显示动画; 适合要点总结、清单展示、步骤说明等文本场景; 使用渐变背景和动态特效提升文本展示的视觉效果", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "文本展示的主标题"}, "items": {"type": "array", "items": {"type": "object"}, "description": "文本项目列表，每个项目包含text、tags字段，颜色会自动分配"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["title", "narration"]}, "strict": true}]