{"type": "animate_mindmap", "description": "创建交互式思维导图，支持节点聚焦、子树展开和动态布局调整。\n使用场景: 展示知识结构和概念层次关系; 教学中的概念图解和思路梳理; 项目规划和任务分解可视化; 复杂信息的结构化展示", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "data_source": {"type": "object", "description": "思维导图数据（根 + 3 级）。数据结构必须严格遵守三级层级（不含根节点）。第一级和第二级节点应作为核心标题，文字简练，且其下的子节点数量不少于3个。作为内容主体的第三级节点，则用于展示详细的关键信息，文字应相对丰富完整，每个第二级节点下的第三级节点数量应控制在3至6个之间，以确保信息的完整性和布局的美观性。", "properties": {"标题": {"type": "string", "description": "根节点标题"}, "子章节": {"type": "array", "description": "第一级节点列表", "items": {"type": "object", "properties": {"标题": {"type": "string"}, "子章节": {"type": "array", "description": "第二级节点列表", "items": {"type": "object", "properties": {"标题": {"type": "string"}, "子章节": {"type": "array", "description": "第三级节点列表（叶子）", "items": {"type": "object", "properties": {"标题": {"type": "string"}}, "required": ["标题"], "additionalProperties": false}}}, "required": ["标题"], "additionalProperties": false}}}, "required": ["标题"], "additionalProperties": false}}}, "additionalProperties": false}, "layout_style": {"type": "string", "description": "布局样式。可选值：'balance'（平衡布局）, 'left_to_right'（从左到右）, 'right_to_left'（从右到左）", "enum": ["balance", "left_to_right", "right_to_left"], "default": "balance"}, "max_depth": {"type": "integer", "description": "显示的最大层级深度，超过这个深度的节点不会显示", "default": 3}, "focus_sequence": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}, "description": "focus_sequence参数可以创建引导式的节点聚焦动画，如果有需要强调的节点才用，没有可以不用。根结点无需作为第一个focus元素，如果有整体的总结性narration，可以将根节点作为最后一个focus元素。list中的元素为要聚焦的节点，包括节点文本和对应的旁白文本两个string。如果有focus_sequence元素，每个元素节点尽量配合对应的旁白，并和整体的narration要连贯，过渡流畅，不要有重复内容。", "default": "None"}, "narration": {"type": "string", "description": "在思维导图显示时播放的语音旁白文本，对思维导图进行整体介绍"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["title", "data_source", "narration"]}, "strict": true}