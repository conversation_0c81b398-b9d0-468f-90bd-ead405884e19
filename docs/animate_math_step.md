# animate_math_step

## 效果

在Manim场景中专业地展示数学解题步骤，支持逐步呈现和高亮强调。支持解析markdown格式的数学公式文本，
智能识别变量、值、操作符等数学元素，并使用不同颜色和动画效果进行区分展示。


## 使用场景

- 数学定理证明的逐步展示
- 方程求解过程的详细演示
- 几何计算步骤的专业讲解
- 代数运算过程的可视化

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 解题标题，会显示在步骤上方 | 是 | - |
| steps_content | str | markdown格式的数学步骤描述，每行一个步骤，支持数学公式 | 是 | - |
| narration | str | 在步骤展示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| step_delay | float | 每步之间的延迟时间（秒） | 否 | 1.5 |
| highlight_delay | float | 高亮效果的延迟时间（秒） | 否 | 0.5 |

## DSL示例

### 示例 1

```json
{
  "type": "animate_math_step",
  "params": {
    "title": "三角函数值计算",
    "steps_content": "- `AH = x√3 = 3 + √3`\n- `BH = BD - DH = 3 - √3`\n- `tan(B) = AH / BH`\n  `= (3 + √3) / (3 - √3)`\n  `= 2 + √3`\n",
    "narration": "让我们一步步计算这个三角函数的值，首先确定AH和BH的长度，然后应用正切函数的定义。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_math_step",
  "params": {
    "title": "二次方程求解",
    "steps_content": "- `x² + 2x - 3 = 0`\n- `(x + 3)(x - 1) = 0`\n- `x = -3 或 x = 1`\n",
    "narration": "这是一个标准的二次方程求解过程，我们使用因式分解的方法来找到解。",
    "step_delay": 2.0
  }
}
```

### 示例 3

```json
{
  "type": "animate_math_step",
  "params": {
    "title": "复杂方程组求解过程",
    "steps_content": "- `2x + 3y = 12`\n- `4x - y = 8`\n- `y = 4x - 8`\n- `2x + 3(4x - 8) = 12`\n- `2x + 12x - 24 = 12`\n- `14x = 36`\n- `x = 36/14 = 18/7`\n- `y = 4(18/7) - 8`\n- `y = 72/7 - 56/7`\n- `y = 16/7`\n",
    "narration": "这是一个复杂的方程组求解过程，我们使用替换法逐步消元求解，演示分屏显示功能。",
    "step_delay": 1.2
  }
}
```

## 注意事项

- 支持标准markdown列表格式，每个`-`项为一个步骤
- 数学公式用反引号包围，会被解析为数学表达式
- 自动识别变量（字母）、值（数字）、操作符（+、-、=、/等）
- 支持分数的专业显示格式
- 每个步骤会逐步出现，带有淡入动画效果
- 重要的数学元素会有高亮强调效果
- 最终结果会有特殊的强调样式