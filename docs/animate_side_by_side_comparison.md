# animate_side_by_side_comparison

## 效果

创建左右两栏并排比较的布局，可以比较不同类型的内容（文本、代码、图像等）。


## 使用场景

- 对比两种不同的代码实现或算法
- 比较"之前"与"之后"的状态
- 并列展示问题和解决方案
- 对比两个图像、图表或设计

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| left_content | str | 左侧内容（文本、代码字符串或图像路径） | 是 | - |
| left_type | str | 左侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown' | 是 | - |
| right_content | str | 右侧内容（文本、代码字符串或图像路径） | 是 | - |
| right_type | str | 右侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown' | 是 | - |
| left_title | str | 左侧窗格的标题 | 否 | None |
| right_title | str | 右侧窗格的标题 | 否 | None |
| transition | str | 内容入场的动画效果。可选值：'fadeIn', 'slideUp', 'none' | 否 | fadeIn |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "title": "斐波那契数列的递归和迭代实现",
    "left_content": "def fib_recursive(n):\n    if n <= 1:\n        return n\n    return fib_recursive(n-1) + fib_recursive(n-2)\n",
    "left_type": "code",
    "left_title": "递归斐波那契",
    "right_content": "def fib_iterative(n):\n    a, b = 0, 1\n    for _ in range(n):\n        a, b = b, a + b\n    return a\n",
    "right_type": "code",
    "right_title": "迭代斐波那契",
    "narration": "让我们比较斐波那契数列的递归和迭代实现。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "title": "Python 和 Java 的信息对比",
    "left_content": "{\n    \"name\": \"Python\",\n    \"year\": 1991,\n    \"creator\": \"Guido van Rossum\",\n    \"paradigms\": [\"面向对象\", \"命令式\", \"函数式\"]\n}\n",
    "left_type": "json",
    "left_title": "Python 信息",
    "right_content": "{\n    \"name\": \"Java\",\n    \"year\": 1995,\n    \"creator\": \"James Gosling\",\n    \"paradigms\": [\"面向对象\", \"命令式\"]\n}\n",
    "right_type": "json",
    "right_title": "Java 信息",
    "transition": "fadeIn",
    "narration": "Python 和 Java 的信息对比。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "title": "Python 和 Java 的信息对比",
    "left_content": "# Python\n\n- 创建于1991年\n\n- 由Guido van Rossum开发\n\n- 支持面向对象、命令式和函数式编程\n",
    "left_type": "markdown",
    "left_title": "Python 信息",
    "right_content": "# Java\n\n- 创建于1995年\n\n- 由James Gosling开发\n\n- 主要支持面向对象编程\n",
    "right_type": "markdown",
    "right_title": "Java 信息",
    "transition": "fadeIn",
    "narration": "Python 和 Java 的信息对比。"
  }
}
```

## 注意事项

- 对于'image'类型，content应为图像文件的本地路径
- 对于'code'和'json'类型，会自动应用语法高亮
- 内容会自动缩放以适应各自的窗格大小
- 调用此函数会清除屏幕上的其他内容