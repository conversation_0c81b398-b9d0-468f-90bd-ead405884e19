# animate_cover

## 效果

创建专业美观的封面页面，采用BPE开篇动画风格，支持疑问句标题和副标题展示。
使用黑底白字配色，橙色装饰线，专业优雅的入场动画效果。


## 使用场景

- 学术报告封面：展示研究主题和核心观点
- 技术分享开场：突出技术问题和解决方案
- 在线课程封面：显示课程主题和学习收益
- 企业培训页面：体现专业性和内容重点
- 社交媒体内容：吸引点击和分享

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 主标题，建议疑问句形式激发好奇心 | 是 | - |
| subtitle | str | 副标题，可包含关键数据、结论、机构等信息 | 否 | None |
| background_style | str | 背景样式（预留参数，当前统一黑色背景） | 否 | gradient |
| color_scheme | str | 配色方案（预留参数，当前统一BPE配色） | 否 | blue |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 封面展示时播放的语音旁白文本 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_cover",
  "params": {
    "title": "AI能否完全替代程序员工作？",
    "subtitle": "调查显示92%认同 • 创造力仍是人类优势",
    "narration": "探索人工智能与人类工作的未来关系。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_cover",
  "params": {
    "title": "量子计算何时商用化？",
    "subtitle": "速度快100万倍 • 算力瓶颈即将突破 • 中科院研究",
    "narration": "量子计算将彻底改变计算能力的边界。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_cover",
  "params": {
    "title": "0基础3个月能学会编程吗？",
    "subtitle": "学员87%成功率 • 高效学习路径已验证 • 字节跳动",
    "id": "python_course_cover",
    "narration": "掌握科学的学习方法，编程入门不再困难。"
  }
}
```

### 示例 4

```json
{
  "type": "animate_cover",
  "params": {
    "title": "深度学习真的那么神奇吗？",
    "subtitle": "准确率提升40% • 突破传统算法极限",
    "narration": "让我们揭开深度学习的神秘面纱。"
  }
}
```

### 示例 5

```json
{
  "type": "animate_cover",
  "params": {
    "title": "区块链技术能解决信任问题吗？",
    "subtitle": "去中心化革命 • MIT实验室验证",
    "narration": "探讨区块链技术的实际应用价值。"
  }
}
```

## 注意事项

- 采用BPE开篇动画风格：黑底白字，橙色装饰线，专业优雅入场
- 主标题建议疑问句，激发观众好奇心，提高点击率
- 副标题统一输入，可包含：具体数据(92%、100万倍) + 核心结论 + 权威机构
- 副标题使用 • 分隔不同信息，控制在合理长度内
- BPE色彩配置：主标题#FFFFFF，副标题#94A3B8，装饰线#F18F01
- 动画时序：Write主标题(2s) → Create装饰线+FadeIn副标题(1.5s) → Wait展示(2s)
- 内容保持在场景中，退出由系统统一控制