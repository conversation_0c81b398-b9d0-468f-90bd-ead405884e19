# animate_image

## 效果

在Manim场景中显示图像，支持动态入场动画和叠加注释文本。图片会从右下角斜着入场，然后顺正并进行左右移动放大。


## 使用场景

- 展示需要详细解释的图片或截图
- 展示图表、图解或可视化内容
- 显示产品或界面截图并添加叠加注释说明
- 突出显示图片中的关键要点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| image_path | str | 要显示的图片的本地文件路径 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在图片显示时播放的语音旁白文本 | 是 | - |
| annotation | str | list[str] | 作为注释叠加显示在图片上的文本 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_image",
  "params": {
    "title": "示例图片展示",
    "image_path": "assets/manim_logo.png",
    "narration": "让我们看看这张图片。",
    "annotation": "这是一张示例图片，展示了重要的内容。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_image",
  "params": {
    "title": "系统架构图",
    "image_path": "assets/manim_logo.png",
    "id": "architecture_diagram",
    "annotation": [
      "系统架构关键要点",
      "前端组件负责用户交互",
      "后端服务处理业务逻辑",
      "数据存储确保数据持久化"
    ],
    "narration": "这张架构图展示了系统的主要组件和它们之间的关系。"
  }
}
```

## 注意事项

- 图片文件必须存在且路径正确，否则会抛出FileNotFoundError
- 图片动画分为四个步骤：1）从右下角斜着入场 2）移动到中心并放大 3）旋转顺正 4）左右移动并进一步放大
- 如果提供annotation，它会以发光文字形式叠加显示在图片上，有半透明黑色背景
- annotation支持字符串或列表格式，列表中每个元素会作为一行显示
- 注释文字使用sequential动画逐行显示，提供更好的视觉效果

