{"type": "animate_text_only", "description": "创建纯文本内容的动态展示动画，专门用于文字信息的呈现。支持清单、要点、步骤等文本内容的动态显示，\n包含渐变背景、发光字体效果、逐字显示动画等视觉特效，适合纯文本内容的精美展示。\n使用场景: 展示要点清单、任务列表、检查清单等文本内容; 创建纯文字的动态内容展示，如学习要点、工作计划等; 制作带有视觉特效的文本演示，突出重要信息; 文字内容的分步展示和强调\n注意事项: 专门用于纯文本内容的动态展示，不涉及图片或视频; 支持最多5个文本项目的展示; 每个文本项包含主文本、标签，颜色自动分配; 具有发光字体效果和逐字显示动画; 适合要点总结、清单展示、步骤说明等文本场景; 使用渐变背景和动态特效提升文本展示的视觉效果", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "文本展示的主标题"}, "items": {"type": "array", "items": {"type": "object"}, "description": "文本项目列表，每个项目包含text、tags字段，颜色会自动分配"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["title", "narration"]}, "strict": true}