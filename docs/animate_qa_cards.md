# animate_qa_cards

## 效果

创建完全按照现代设计风格的QA卡片展示动画


## 使用场景

- 教育培训中的问答展示
- 知识总结和回顾
- 产品FAQ展示
- 学术论文要点问答
- 面试问题练习

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| qa_data | list | QA数据列表，每个元素包含question和answer字段 | 是 | - |
| title | str | 展示标题 | 是 | - |
| cards_per_screen | int | 每屏显示的卡片数量（建议最多3个） | 否 | 3 |
| duration_per_card | float | 每张卡片的展示时长（秒） | 否 | 2.0 |
| narration | str | 语音旁白内容 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_qa_cards",
  "params": {
    "qa_data": [
      {
        "question": "什么是'第一性原理'思维？",
        "answer": "第一性原理是一种解决问题的思维方式，它强调回归事物的本质和基本公理进行分析，而不是依赖类比或既有经验。"
      },
      {
        "question": "什么是'SMART原则'？",
        "answer": "SMART原则是设定目标的经典方法论，确保目标清晰可行。它代表：具体的、可衡量的、可达成的、相关的和有时限的。"
      },
      {
        "question": "什么是'心流'状态？",
        "answer": "心流是一种个体完全沉浸、全神贯注于某项活动时的心理状态。在这种状态下，人会感到极大的愉悦和满足。"
      }
    ],
    "title": "知识问答卡片",
    "narration": "让我们通过这些精美的卡片来学习重要概念"
  }
}
```

## 注意事项

- 问题要简短，有洞察力
- 回答言简意赅