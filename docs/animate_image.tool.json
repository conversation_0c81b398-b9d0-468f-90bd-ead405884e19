{"type": "animate_image", "description": "在Manim场景中显示图像，支持动态入场动画和叠加注释文本。图片会从右下角斜着入场，然后顺正并进行左右移动放大。\n使用场景: 展示需要详细解释的图片或截图; 展示图表、图解或可视化内容; 显示产品或界面截图并添加叠加注释说明; 突出显示图片中的关键要点\n注意事项: 图片文件必须存在且路径正确，否则会抛出FileNotFoundError; 图片动画分为四个步骤：1）从右下角斜着入场 2）移动到中心并放大 3）旋转顺正 4）左右移动并进一步放大; 如果提供annotation，它会以发光文字形式叠加显示在图片上，有半透明黑色背景; annotation支持字符串或列表格式，列表中每个元素会作为一行显示; 注释文字使用sequential动画逐行显示，提供更好的视觉效果", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "image_path": {"type": "string", "description": "要显示的图片的本地文件路径"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "narration": {"type": "string", "description": "在图片显示时播放的语音旁白文本"}, "annotation": {"type": "object", "description": "作为注释叠加显示在图片上的文本", "default": "None"}}, "additionalProperties": false, "required": ["title", "image_path", "narration"]}, "strict": true}