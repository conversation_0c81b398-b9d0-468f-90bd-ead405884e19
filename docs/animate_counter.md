# animate_counter

## 效果

在Manim场景中创建并播放计数器动画，支持数字递增/递减计数器和曲线增长图两种类型。
可以自定义起始值、目标值、标签、单位、动画时长和结束特效。


## 使用场景

- 显示随时间变化的数值，如统计数据、得分、加载进度等
- 以曲线图形式展示增长或变化趋势，并在终点显示目标值
- 强调关键性能指标(KPI)的变化

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| target_value | float | 计数器动画的目标值 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| start_value | float | 计数器的起始值。仅用于'counter'类型 | 否 | 0 |
| counter_type | str | 计数器类型。可选值：'counter'（数字计数器）, 'curve'（曲线增长图） | 否 | counter |
| label | str | 计数器的标签或标题文本 | 否 | None |
| unit | str | 计数器的单位文本。对于'counter'类型，单位显示在数字之后；对于'curve'类型，单位与目标值一起显示在曲线末端 | 否 | None |
| duration | float | 计数器动画的持续时间（秒） | 否 | 2.0 |
| effect | str | 动画结束时应用的额外视觉效果。可选值：'flash', 'zoom' | 否 | None |
| narration | str | 播放动画时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_counter",
  "params": {
    "counter_type": "counter",
    "target_value": 100,
    "title": "进度统计",
    "label": "进度",
    "unit": "%",
    "duration": 3,
    "effect": "flash",
    "narration": "加载进度达到100%。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_counter",
  "params": {
    "counter_type": "curve",
    "target_value": 500,
    "title": "用户增长",
    "label": "用户增长曲线",
    "unit": "用户",
    "duration": 4,
    "narration": "用户数量快速增长至500。"
  }
}
```

## 注意事项

- counter类型显示一个从起始值到目标值的数字动画
- curve类型显示一条增长曲线，终点显示目标值
- 结束特效在动画完成后应用，可增强视觉效果