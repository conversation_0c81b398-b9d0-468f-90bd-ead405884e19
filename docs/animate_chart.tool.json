{"type": "animate_chart", "description": "创建并播放条形图、折线图或雷达图的动画，支持单个或多个数据集。\n使用场景: 可视化数据趋势和比较（折线图、条形图）; 展示多个类别在不同指标上的表现（雷达图）; 在视频演示中动态呈现统计数据; 对比不同数据集之间的关系\n注意事项: 对于条形图和折线图，数据键作为x轴标签，值作为y轴数据点; 对于雷达图，数据键作为各个轴的标签，值作为该轴上的数据点; 对于条形图，动画效果是固定的动态增长效果，animation_style参数会被忽略。", "parameters": {"type": "object", "properties": {"chart_type": {"type": "string", "description": "图表类型。可选值：'bar'（条形图）, 'line'（折线图）, 'radar'（雷达图）", "enum": ["bar", "line", "radar"]}, "data": {"type": "array", "items": {"type": "object"}, "description": "图表数据。每个数据集为dict格式（如{\"A\":10,\"B\":20}）"}, "narration": {"type": "string", "description": "在图表显示时播放的语音旁白文本"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "animation_style": {"type": "string", "description": "图表入场动画。可选值：'fadeIn', 'grow', 'draw', 'update', 'dynamic', bar图优先用dynamic", "enum": ["fadeIn", "grow", "draw", "update", "dynamic"], "default": "fadeIn"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "dataset_names": {"type": "array", "items": {"type": "string"}, "description": "数据集名称列表"}, "x_label": {"type": "string", "description": "x轴标签（条形图和折线图有效）", "default": "None"}, "y_label": {"type": "string", "description": "y轴标签（条形图和折线图有效）", "default": "None"}}, "additionalProperties": false, "required": ["chart_type", "data", "narration", "title", "dataset_names"]}, "strict": true}