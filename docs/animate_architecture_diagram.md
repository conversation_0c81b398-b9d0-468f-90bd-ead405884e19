# animate_architecture_diagram

## 效果

在Manim场景中直接播放一个架构图动画。该架构图是使用ExcalidrawToolkit根据文本描述生成的视频。
通常用于全屏展示复杂的系统架构或流程图。


## 使用场景

- 可视化软件架构
- 展示系统组件及其交互
- 解释数据流或业务流程
- 需要通过Excalidraw风格图表进行说明的场景

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content_description | str | 用于生成Excalidraw视频的文本描述，ExcalidrawToolkit将使用此描述来创建图表内容 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| narration | str | 播放动画时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_architecture_diagram",
  "params": {
    "content_description": "一个微服务架构图，包含前端应用、API网关和多个后端服务。\n前端应用连接到API网关，网关将请求路由到用户服务、产品服务和支付服务。\n这些服务分别连接到各自的数据库。\n",
    "title": "微服务架构图",
    "narration": "这个架构图展示了我们的微服务系统结构，以及各组件之间的数据流。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_architecture_diagram",
  "params": {
    "content_description": "一个数据处理流程图，展示从数据收集到分析的完整过程。\n包含数据源、数据收集器、数据存储、处理引擎和可视化工具等组件。\n显示数据如何从原始数据转换为可行性见解。\n",
    "title": "数据处理流程图",
    "id": "data_pipeline",
    "narration": "这个数据处理流程展示了从原始数据到最终分析结果的完整路径。"
  }
}
```

## 注意事项

- ExcalidrawToolkit会根据提供的文本描述自动生成架构图视频
- 内容描述越详细，生成的架构图越准确
- 生成的视频会自动缩放以适应场景大小
- 此功能需要与外部ExcalidrawToolkit组件配合使用