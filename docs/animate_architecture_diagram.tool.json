{"type": "animate_architecture_diagram", "description": "在Manim场景中直接播放一个架构图动画。该架构图是使用ExcalidrawToolkit根据文本描述生成的视频。\n通常用于全屏展示复杂的系统架构或流程图。\n使用场景: 可视化软件架构; 展示系统组件及其交互; 解释数据流或业务流程; 需要通过Excalidraw风格图表进行说明的场景\n注意事项: ExcalidrawToolkit会根据提供的文本描述自动生成架构图视频; 内容描述越详细，生成的架构图越准确; 生成的视频会自动缩放以适应场景大小; 此功能需要与外部ExcalidrawToolkit组件配合使用", "parameters": {"type": "object", "properties": {"content_description": {"type": "string", "description": "用于生成Excalidraw视频的文本描述，ExcalidrawToolkit将使用此描述来创建图表内容"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "narration": {"type": "string", "description": "播放动画时同步播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["content_description", "title", "narration"]}, "strict": true}