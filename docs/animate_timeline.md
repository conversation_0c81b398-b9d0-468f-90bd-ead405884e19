# animate_timeline

## 效果

在Manim场景中创建并播放一个动态的、分段构建的时间轴动画。
每个事件包含时间点（年份）、标题、描述文本，并可选择性地包含图片和自定义颜色。
事件详情会交替显示在时间轴的上方和下方。


## 使用场景

- 展示项目里程碑或历史事件序列，具有更强的视觉吸引力
- 解释一个过程的各个阶段，每个阶段有清晰的标题和描述
- 可视化产品发布路线图，突出显示关键节点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| events | list[EventData | dict[str, Any]] | 时间轴事件列表。每个元素可以是EventData对象或字典，包含year(时间点), title(标题), description(描述), emoji(表情), color(颜色), narration(事件旁白)等属性，其中narration会在当前时间轴事件显示的同时作为旁白播放。如果有全局的content_narration，那么事件的narration将被忽略。 | 是 | - |
| intro_narration | str | 时间轴动画开始时播放的语音旁白文本（会配合开始标题的显示动画一起播放） | 否 | None |
| outro_narration | str | 时间轴动画结束时播放的语音旁白文本（会配合最后整个时间轴缩放动画一起播放） | 否 | None |
| content_narration | str | 时间轴动画播放时同步播放的语音旁白文本（与每个事件的narration互斥） | 是 | - |
| title | str | 时间轴标题 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_timeline",
  "params": {
    "events": [
      {
        "year": "1950",
        "title": "The Beginning",
        "description": "A new era starts.",
        "emoji": "🌍",
        "color": "#F0E68C",
        "narration": "A new era starts."
      },
      {
        "year": "1965",
        "title": "Major Discovery",
        "description": "Key findings published.",
        "emoji": "🌞",
        "color": "#FFA500",
        "narration": "Key findings published."
      },
      {
        "year": "1980",
        "title": "Expansion",
        "description": "Growth and development.",
        "emoji": "🌛",
        "color": "#B22222",
        "narration": "Growth and development."
      },
      {
        "year": "2000",
        "title": "New Century",
        "description": "Looking ahead.",
        "emoji": "🪵",
        "color": "#FFC0CB",
        "narration": "Looking ahead."
      },
      {
        "year": "2020",
        "title": "Modern Times",
        "description": "Current state of affairs.",
        "emoji": "🚀",
        "color": "#9370DB",
        "narration": "Current state of affairs."
      }
    ],
    "title": "Historical Timeline",
    "intro_narration": "A journey through time, highlighting key moments.",
    "outro_narration": "This is the end of the timeline."
  }
}
```

## 注意事项

- 事件列表按数组中的顺序呈现，而非按年份排序
- 时间轴展示时会先显示主轴，然后顺序显示每个事件
- 可以为每个事件指定颜色，或使用默认颜色方案
- emoji属性会尝试下载并显示对应图标，如果失败则仅显示简单节点
- 如果时间轴节点较多（超过5个），每个节点单独的narration会使视频变得冗长，可以设置简洁的整体content_narration来介绍时间轴的背景信息