{"type": "animate_math_step", "description": "在Manim场景中专业地展示数学解题步骤，支持逐步呈现和高亮强调。支持解析markdown格式的数学公式文本，\n智能识别变量、值、操作符等数学元素，并使用不同颜色和动画效果进行区分展示。\n使用场景: 数学定理证明的逐步展示; 方程求解过程的详细演示; 几何计算步骤的专业讲解; 代数运算过程的可视化\n注意事项: 支持标准markdown列表格式，每个`-`项为一个步骤; 数学公式用反引号包围，会被解析为数学表达式; 自动识别变量（字母）、值（数字）、操作符（+、-、=、/等）; 支持分数的专业显示格式; 每个步骤会逐步出现，带有淡入动画效果; 重要的数学元素会有高亮强调效果; 最终结果会有特殊的强调样式", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "解题标题，会显示在步骤上方"}, "steps_content": {"type": "string", "description": "markdown格式的数学步骤描述，每行一个步骤，支持数学公式"}, "narration": {"type": "string", "description": "在步骤展示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "step_delay": {"type": "number", "description": "每步之间的延迟时间（秒）", "default": 1.5}, "highlight_delay": {"type": "number", "description": "高亮效果的延迟时间（秒）", "default": 0.5}}, "additionalProperties": false, "required": ["title", "steps_content", "narration"]}, "strict": true}