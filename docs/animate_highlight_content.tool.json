{"type": "animate_highlight_content", "description": "按顺序高亮一系列元素，或者对代码对象高亮特定行。\n使用场景: 逐步引导观众注意场景中的特定对象; 逐行解释代码片段，高亮当前讨论的行; 强调流程图或架构图中的特定组件序列\n注意事项: 被高亮的元素必须已经在场景中存在并且有指定的ID; 对于代码高亮，lines参数优先于highlight_type; 高亮效果是暂时的，结束后元素会恢复原始状态", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "elements": {"type": "array", "items": {"type": "string"}, "description": "要高亮的元素ID列表。如果指定lines参数，则此列表应只包含一个Code对象的ID"}, "highlight_type": {"type": "string", "description": "高亮效果类型。可选值：'flash'（闪烁）, 'box'（边框）, 'underline'（下划线）, 'color'（颜色变化）", "default": "box"}, "color": {"type": "string", "description": "高亮效果的颜色（十六进制或颜色名称）", "default": "#FFFF00"}, "duration_per_item": {"type": "number", "description": "每个元素或代码行组的高亮持续时间（秒）", "default": 1.0}, "lines": {"type": "string", "description": "要高亮的代码行范围，格式如\"1-3,5,7-10\"。如果提供此参数，elements应只有一个Code对象ID", "default": "None"}, "narration": {"type": "string", "description": "播放动画时同步播放的语音旁白文本", "default": "None"}}, "additionalProperties": false, "required": ["title", "elements"]}, "strict": true}