# animate_table

## 效果

在Manim场景中创建并展示表格，支持逐行高亮显示效果。表格包含表头和数据行，单元格之间有间隙，支持黄色高亮状态。


## 使用场景

- 展示统计数据表格
- 显示对比数据分析
- 逐步突出重要数据行
- 展示结构化信息

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| headers | List[str] | 表格表头列表 | 是 | - |
| data | List[List[str]] | 表格数据，每个子列表代表一行数据 | 是 | - |
| title | str | 表格标题 | 是 | - |
| highlight_rows | List[int] | 需要高亮的行索引列表（从0开始，不包括表头） | 否 | [] |
| cell_spacing | float | 单元格之间的间距 | 否 | 0.2 |
| highlight_color | str | 高亮颜色，支持Manim颜色常量 | 否 | YELLOW |
| narration | str | 在表格展示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_table",
  "params": {
    "title": "城市职业变化统计表",
    "headers": [
      "城市",
      "4月入职",
      "5月离职",
      "5月入职",
      "5月离职",
      "6月入职",
      "6月离职",
      "主要离职原因"
    ],
    "data": [
      [
        "北京",
        "23",
        "11",
        "15",
        "7",
        "17",
        "3",
        "家庭原因"
      ],
      [
        "上海",
        "43",
        "2",
        "12",
        "8",
        "19",
        "11",
        "薪资原因"
      ],
      [
        "深圳",
        "11",
        "3",
        "15",
        "2",
        "16",
        "5",
        "个人发展"
      ],
      [
        "杭州",
        "15",
        "1",
        "19",
        "3",
        "15",
        "0",
        "家庭原因"
      ],
      [
        "武汉",
        "17",
        "0",
        "9",
        "7",
        "7",
        "8",
        "晋升"
      ],
      [
        "广州",
        "9",
        "1",
        "3",
        "0",
        "2",
        "2",
        "个人问题"
      ],
      [
        "合计",
        "118",
        "18",
        "73",
        "27",
        "76",
        "29",
        "/"
      ]
    ],
    "highlight_rows": [
      1
    ],
    "narration": "让我们来看看这个城市职业变化的统计表格。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_table",
  "params": {
    "title": "产品销量对比分析表",
    "headers": [
      "产品",
      "Q1销量",
      "Q2销量",
      "增长率"
    ],
    "data": [
      [
        "产品A",
        "100",
        "120",
        "20%"
      ],
      [
        "产品B",
        "80",
        "95",
        "18.8%"
      ],
      [
        "产品C",
        "60",
        "90",
        "50%"
      ]
    ],
    "highlight_rows": [
      0,
      2
    ],
    "narration": "这是产品销量的对比分析表格。"
  }
}
```

## 注意事项

- 表格内容不要超过8个字符
- 表格会自动调整尺寸以适应屏幕
- 高亮行会以黄色背景显示
- 单元格之间的间距可以调整
- 支持中文内容显示
- 表格展示后会逐行进行高亮动画