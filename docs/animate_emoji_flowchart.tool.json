{"type": "animate_emoji_flowchart", "description": "创建一个交互式emoji流程图动画，支持emoji列表或包含emoji的文本输入生成对应的流程图。\n支持多种布局和动画风格，自动调整大小以适应屏幕。\n使用场景: 展示工作流程或业务流程; 可视化步骤序列或决策树; 创建教学演示的流程图; 显示系统架构或数据流\n注意事项: 支持直接使用emoji列表或从文本中自动提取emoji和关键词; 布局算法会自动防止重叠并适应屏幕尺寸; 连接线会智能避开emoji边界; emoji列表长度会影响布局效果", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "emoji_list": {"type": "array", "items": {"type": "object"}, "description": "emoji和对应关键词的列表，格式为[(emoji, keyword), ...]。与content二选一"}, "content": {"type": "string", "description": "包含emoji和描述的流程文本。与emoji_list二选一"}, "layout": {"type": "string", "description": "布局样式。可选值：horizontal, vertical, circular, grid", "default": "horizontal"}, "connection_style": {"type": "string", "description": "连接线样式。可选值：arrow, line, curve", "default": "arrow"}, "animation_style": {"type": "string", "description": "动画风格。可选值：sequence, simultaneous, cascade", "default": "sequence"}, "max_count": {"type": "integer", "description": "最大emoji数量限制（仅在使用content时有效）", "default": 8}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}}, "additionalProperties": false, "required": ["title", "narration"]}, "strict": true}