{"type": "animate_step_by_step", "description": "在Manim场景中创建并播放一个分步骤讲解的动画。\n左侧展示步骤节点，每一步包含序号和操作描述，节点动态生成并向上移动。\n右侧展示每一步的具体内容（markdown格式），支持代码、列表、文本等。\n最后所有步骤节点缩放移动到画面正中，展示整体概念。\n使用场景: 教学演示中的分步骤讲解，如算法步骤、操作流程等; 产品功能介绍，逐步展示各个功能点; 项目开发流程演示，突出每个阶段的重点\n注意事项: 步骤按数组中的顺序呈现，每个步骤的内容支持完整的markdown语法; 左侧节点会动态生成并向上移动，右侧内容会淡出后显示新内容; 可以为每个步骤指定颜色，或使用默认颜色方案; 最后所有步骤节点会缩放移动到画面中央，形成整体概览; 与timeline的差别是，step_by_step需要讲解每个步骤中的详细内容，因此更适合例子讲解等场景，而timeline只是展示事件的整体脉络; title, subtitle与step title需要精简，不能字数太多，否则会导致文字重叠等问题", "parameters": {"type": "object", "properties": {"steps": {"type": "array", "description": "步骤列表，每个元素是一个dict，包括 step_number, title, content, color（可选）, narration（可选） 字段", "items": {"type": "object", "properties": {"step_number": {"type": "string", "description": "步骤序号"}, "title": {"type": "string", "description": "步骤标题"}, "content": {"type": "string", "description": "步骤内容（markdown）"}, "color": {"type": "string", "description": "节点颜色（可选）"}, "narration": {"type": "string", "description": "步骤旁白（可选）"}, "required": {"type": "string"}, "additionalProperties": {"type": "string"}}, "additionalProperties": false}}, "intro_narration": {"type": "string", "description": "开场介绍语音旁白文本"}, "outro_narration": {"type": "string", "description": "结尾总结语音旁白文本"}, "title": {"type": "string", "description": "整体标题"}, "subtitle": {"type": "string", "description": "副标题", "default": "None"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["steps", "intro_narration", "outro_narration", "title"]}, "strict": true}