{"type": "animate_side_by_side_comparison", "description": "创建左右两栏并排比较的布局，可以比较不同类型的内容（文本、代码、图像等）。\n使用场景: 对比两种不同的代码实现或算法; 比较\"之前\"与\"之后\"的状态; 并列展示问题和解决方案; 对比两个图像、图表或设计\n注意事项: 对于'image'类型，content应为图像文件的本地路径; 对于'code'和'json'类型，会自动应用语法高亮; 内容会自动缩放以适应各自的窗格大小; 调用此函数会清除屏幕上的其他内容", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "left_content": {"type": "string", "description": "左侧内容（文本、代码字符串或图像路径）"}, "left_type": {"type": "string", "description": "左侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown'"}, "right_content": {"type": "string", "description": "右侧内容（文本、代码字符串或图像路径）"}, "right_type": {"type": "string", "description": "右侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown'"}, "left_title": {"type": "string", "description": "左侧窗格的标题", "default": "None"}, "right_title": {"type": "string", "description": "右侧窗格的标题", "default": "None"}, "transition": {"type": "string", "description": "内容入场的动画效果。可选值：'fadeIn', 'slideUp', 'none'", "default": "fadeIn"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["title", "left_content", "left_type", "right_content", "right_type", "narration"]}, "strict": true}