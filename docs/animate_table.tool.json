{"type": "animate_table", "description": "在Manim场景中创建并展示表格，支持逐行高亮显示效果。表格包含表头和数据行，单元格之间有间隙，支持黄色高亮状态。\n使用场景: 展示统计数据表格; 显示对比数据分析; 逐步突出重要数据行; 展示结构化信息\n注意事项: 表格内容不要超过8个字符; 表格会自动调整尺寸以适应屏幕; 高亮行会以黄色背景显示; 单元格之间的间距可以调整; 支持中文内容显示; 表格展示后会逐行进行高亮动画", "parameters": {"type": "object", "properties": {"headers": {"type": "array", "items": {"type": "string"}, "description": "表格表头列表"}, "data": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}, "description": "表格数据，每个子列表代表一行数据"}, "title": {"type": "string", "description": "表格标题"}, "highlight_rows": {"type": "array", "items": {"type": "integer"}, "description": "需要高亮的行索引列表（从0开始，不包括表头）", "default": []}, "cell_spacing": {"type": "number", "description": "单元格之间的间距", "default": 0.2}, "highlight_color": {"type": "string", "description": "高亮颜色，支持Manim颜色常量", "default": "YELLOW"}, "narration": {"type": "string", "description": "在表格展示时播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["headers", "data", "title", "narration"]}, "strict": true}