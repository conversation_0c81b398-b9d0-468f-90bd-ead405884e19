{"type": "animate_video", "description": "在Manim场景中播放视频文件，支持可选的文本叠加和语音旁白。\n使用场景: 在教程或演示中展示屏幕录制或外部视频片段; 播放动画片段作为更复杂场景的一部分; 叠加解释性文本或字幕到视频内容上; 配合旁白同步视频演示\n注意事项: 视频文件必须存在且路径正确，否则函数会记录错误并返回; 视频会自动缩放以适应场景; 叠加文本会依次显示，每行之间有指定的延迟", "parameters": {"type": "object", "properties": {"video_path": {"type": "string", "description": "要播放的视频文件的本地路径"}, "title": {"type": "string", "description": "视频标题", "default": "None"}, "overlay_text": {"type": "string", "description": "可选的叠加文本，显示在视频之上。多行文本用\"\\n\"分隔", "default": "None"}, "overlay_animation_delay": {"type": "number", "description": "每行文本动画之间的延迟（秒）", "default": 1.0}, "narration": {"type": "string", "description": "在视频播放时同步播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["video_path", "narration"]}, "strict": true}