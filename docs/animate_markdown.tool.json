{"type": "animate_markdown", "description": "将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。\n支持各种Markdown元素，包括标题、列表、代码块、表格等。\n使用场景: 展示格式化的文本内容，如教程说明、演示文稿; 在动画中展示结构化的信息，如列表和表格; 显示带有语法高亮的代码片段; 创建包含文本和图片的混合内容\n注意事项: 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等; 根据内容会自动调整大小以适应场景", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "Markdown格式的文本内容，尽量用格式化的元素增强语义丰富性，如标题、列表、加粗、斜体等，减少纯文本的使用。文字必须简洁精练，禁止直接展示长句形式的文本，详细信息可以通过旁白体现，屏幕上只能展示最重要的关键信息。"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "title": {"type": "string", "description": "当前内容的标题，必须言简意赅，概括内容要点。"}, "narration": {"type": "string", "description": "在内容显示时播放的语音旁白文本"}}, "additionalProperties": false, "required": ["content", "title", "narration"]}, "strict": true}