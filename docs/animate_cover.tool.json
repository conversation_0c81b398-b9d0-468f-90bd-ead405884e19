{"type": "animate_cover", "description": "创建专业美观的封面页面，采用BPE开篇动画风格，支持疑问句标题和副标题展示。\n使用黑底白字配色，橙色装饰线，专业优雅的入场动画效果。\n使用场景: 学术报告封面：展示研究主题和核心观点; 技术分享开场：突出技术问题和解决方案; 在线课程封面：显示课程主题和学习收益; 企业培训页面：体现专业性和内容重点; 社交媒体内容：吸引点击和分享\n注意事项: 采用BPE开篇动画风格：黑底白字，橙色装饰线，专业优雅入场; 主标题建议疑问句，激发观众好奇心，提高点击率; 副标题统一输入，可包含：具体数据(92%、100万倍) + 核心结论 + 权威机构; 副标题使用 • 分隔不同信息，控制在合理长度内; BPE色彩配置：主标题#FFFFFF，副标题#94A3B8，装饰线#F18F01; 动画时序：Write主标题(2s) → Create装饰线+FadeIn副标题(1.5s) → Wait展示(2s); 内容保持在场景中，退出由系统统一控制", "parameters": {"type": "object", "properties": {"title": {"type": "string", "description": "主标题，建议疑问句形式激发好奇心"}, "subtitle": {"type": "string", "description": "副标题，可包含关键数据、结论、机构等信息", "default": "None"}, "background_style": {"type": "string", "description": "背景样式（预留参数，当前统一黑色背景）", "default": "gradient"}, "color_scheme": {"type": "string", "description": "配色方案（预留参数，当前统一BPE配色）", "default": "blue"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}, "narration": {"type": "string", "description": "封面展示时播放的语音旁白文本"}}, "additionalProperties": false, "required": ["title", "narration"]}, "strict": true}