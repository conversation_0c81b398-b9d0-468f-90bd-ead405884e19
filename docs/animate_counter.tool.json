{"type": "animate_counter", "description": "在Manim场景中创建并播放计数器动画，支持数字递增/递减计数器和曲线增长图两种类型。\n可以自定义起始值、目标值、标签、单位、动画时长和结束特效。\n使用场景: 显示随时间变化的数值，如统计数据、得分、加载进度等; 以曲线图形式展示增长或变化趋势，并在终点显示目标值; 强调关键性能指标(KPI)的变化\n注意事项: counter类型显示一个从起始值到目标值的数字动画; curve类型显示一条增长曲线，终点显示目标值; 结束特效在动画完成后应用，可增强视觉效果", "parameters": {"type": "object", "properties": {"target_value": {"type": "number", "description": "计数器动画的目标值"}, "title": {"type": "string", "description": "当前内容的标题，会显示在内容上方"}, "start_value": {"type": "number", "description": "计数器的起始值。仅用于'counter'类型", "default": 0}, "counter_type": {"type": "string", "description": "计数器类型。可选值：'counter'（数字计数器）, 'curve'（曲线增长图）", "default": "counter"}, "label": {"type": "string", "description": "计数器的标签或标题文本", "default": "None"}, "unit": {"type": "string", "description": "计数器的单位文本。对于'counter'类型，单位显示在数字之后；对于'curve'类型，单位与目标值一起显示在曲线末端", "default": "None"}, "duration": {"type": "number", "description": "计数器动画的持续时间（秒）", "default": 2.0}, "effect": {"type": "string", "description": "动画结束时应用的额外视觉效果。可选值：'flash', 'zoom'", "default": "None"}, "narration": {"type": "string", "description": "播放动画时同步播放的语音旁白文本"}, "id": {"type": "string", "description": "创建的<PERSON>im <PERSON>bject的唯一标识符", "default": "None"}}, "additionalProperties": false, "required": ["target_value", "title", "narration"]}, "strict": true}