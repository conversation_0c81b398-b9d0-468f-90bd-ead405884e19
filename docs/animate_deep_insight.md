# animate_deep_insight

## 效果

创建现代化的深度洞察卡片动画，采用"Sparkling Insight Card"设计风格，支持闪光特效和优雅的悬浮动画


## 使用场景

- 深度洞察分析结果展示
- 思维启发和认知增强
- 学术论文的核心观点提炼
- 商业分析的关键发现
- 创新思维的火花展示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| insights_data | list | 深度洞察数据列表，每个元素包含insight_title和insight_description字段 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| cards_per_screen | int | 每屏显示的卡片数量（建议最多3个） | 否 | 3 |
| duration_per_card | float | 每张卡片的展示时长（秒） | 否 | 4.0 |
| theme | str | 主题风格。可选值：'sparkling'（闪光）, 'elegant'（优雅）, 'modern'（现代） | 否 | sparkling |
| narration | str | 语音旁白内容 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_deep_insight",
  "params": {
    "insights_data": [
      {
        "insight_title": "用户流失的真正原因",
        "insight_description": "用户流失的真正原因，不是功能缺失，而是体验上的细微摩擦累积。"
      },
      {
        "insight_title": "数据驱动决策的核心",
        "insight_description": "有效的数据分析不在于数据量的大小，而在于找到能直接影响业务结果的关键指标。"
      },
      {
        "insight_title": "创新的本质特征",
        "insight_description": "真正的创新往往来自于对现有问题的重新定义，而不是对现有解决方案的改进。"
      }
    ],
    "title": "商业洞察精选",
    "theme": "sparkling",
    "narration": "让我们一起探索这些闪闪发光的商业洞察，每一个都可能改变我们的思维方式"
  }
}
```

### 示例 2

```json
{
  "type": "animate_deep_insight",
  "params": {
    "insights_data": [
      {
        "insight_title": "算法偏见的根源",
        "insight_description": "机器学习算法的偏见不是技术问题，而是训练数据中隐含的社会偏见的放大。"
      }
    ],
    "title": "AI伦理思考",
    "cards_per_screen": 1,
    "duration_per_card": 6.0,
    "theme": "elegant",
    "narration": "深入思考人工智能发展过程中的伦理挑战和社会责任"
  }
}
```

## 注意事项

- 完全复刻HTML版本的"Sparkling Insight Card"设计
- 柔和的浅蓝色背景营造轻松氛围
- 白色卡片背景配合圆角和阴影效果
- 金色✨图标和顶部闪光条特效
- 轻微倾斜和悬浮动画增强视觉吸引力
- 支持智能文本换行和响应式布局