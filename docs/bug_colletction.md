#功能
1 chat根据类型增加默认prompt
2 进度
    2.1 进度条放大一些，尽量准，不需要给出开始时间，可以参考videotutor
    2.2 是否可以给出大步骤提醒，比如正在分析文档内容，正在生成讲解视频，类似manus增加一些过程性日志
3 默认生成视频都保存到草稿箱
4 目前不需要搜索功能
5 视频生成增加语言，TTS，BGM选择
6 视频生成后是否和输入生成在一起，这样重新生成不需要返回到主页
7 原始素材是否可点击展示，增加对应类型的封面，比如github,arxiv,pdf等，便于对比视频
8 视频暂停、重新跑的，是否需要删除storyboard
9 视频生成失败没有报错，是否删除storyboard重试
10 是否可以增加Debug模式，中间文件和日志能看到
11 支持文件拖拽
12 不同类型文件用图表区别，后来直接使用类型，并且绑定默认prompt（如果chat输入则替代），是否只有chat才能输入prompt
13 无中生有后台结束了，前端显示始终90%

#展现
1 视频封面像素太低
2 视频生成后下面标题、描述、类型、生成指令、按钮可以简化，视频标题是否算法端透传出来这个字段
3 文件太大，错误提醒直接是日志报错，需要美观的弹窗
4 异常返回也需要谈窗说明，同3，比如辱骂黄反，链接异常，文件异常等

#内容能力
1 github项目没讲清楚
2 无中生有没有声音？
3 无中生有讲解很乱
4 excel文件没解析
5 doc文件能解析讲解逻辑差
6 制定评测标准，便于快速迭代和优化
