# animate_competitive_analysis

## 效果

创建多阶段的竞品对比分析动画，包括1v1对比、维度雷达图分析和综合评估结论。


## 使用场景

- 产品竞品分析报告展示
- 技术方案选型对比分析
- 多维度评估结果可视化
- 决策建议和结论展示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| analysis_data | dict | 竞品分析数据，包含当前方案、替代方案、维度对比分析和综合评估 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| narration | str | 整体分析过程的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_competitive_analysis",
  "params": {
    "title": "竞品对比分析",
    "analysis_data": {
      "当前方案": {
        "名称": "Quarkdown",
        "定位": "通用文档格式转换器，支持多种输入输出格式",
        "优势": "编程逻辑支持，广泛的格式支持，命令行操作方便集成",
        "劣势": "复杂排版限制",
        "适用场景": "文档转换，报告生成"
      },
      "替代方案": [
        {
          "名称": "Pandoc",
          "定位": "通用文档格式转换器，支持多种输入输出格式",
          "优势": "社区成熟度，广泛的格式支持，高度灵活",
          "劣势": "静态内容局限",
          "适用场景": "文档转换，报告生成"
        },
        {
          "名称": "Jupyter Notebook",
          "定位": "代码、文本和输出结果混合编程文档",
          "优势": "实时内核交互，强数据分析能力，结果可交互展示",
          "劣势": "排版能力受限，难以生成专业印刷书籍",
          "适用场景": "数据科学报告，研究分析"
        },
        {
          "名称": "LaTeX",
          "定位": "科学出版和技术文档领域的标准排版系统",
          "优势": "印刷级精度，排版质量极高，公式图表处理强大",
          "劣势": "学习曲线陡峭，语法复杂",
          "适用场景": "学术论文，书籍出版"
        }
      ],
      "维度对比分析": {
        "功能特性": "Quarkdown强调编程与动态内容，Pandoc重在格式转换",
        "编程能力": "Quarkdown图灵完备，Jupyter侧重数据处理",
        "输出格式": "Quarkdown/Pandoc支持多种格式",
        "易用性": "Quarkdown基于Markdown中等难度"
      },
      "综合评估": {
        "推荐指数": "4星",
        "关键结论": "Quarkdown是创新型可编程排版工具",
        "决策建议": "对于需工程化内容创作用户值得投入学习"
      }
    },
    "narration": "接下来我们将进行全面的竞品对比分析，从1v1对比开始，再展示维度雷达图，最后给出综合评估结论。"
  }
}
```

## 注意事项

- 动画分为三个主要阶段：1v1对比、雷达图分析、综合评估
- 会根据替代方案数量自动调整对比展示
- 雷达图将展示五个维度的对比分析
- 最终会给出推荐指数和决策建议
- 调用此函数会清除屏幕上的其他内容