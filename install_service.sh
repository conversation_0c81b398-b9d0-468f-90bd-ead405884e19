#!/bin/bash

# Feynman视频生成服务安装脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVICE_NAME="feynman-video-service"
SERVICE_FILE="feynman-video-service.service"
CURRENT_USER=$(whoami)
CURRENT_DIR=$(pwd)

echo -e "${BLUE}🚀 Feynman视频生成服务安装脚本${NC}"
echo ""

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}❌ 请不要以root用户运行此脚本${NC}"
#    exit 1
fi

# 检查必要文件是否存在
echo -e "${YELLOW}📋 检查必要文件...${NC}"
if [ ! -f "$SERVICE_FILE" ]; then
    echo -e "${RED}❌ 服务文件 $SERVICE_FILE 不存在${NC}"
    exit 1
fi

if [ ! -f "monitor_video_service.sh" ]; then
    echo -e "${RED}❌ 监控脚本 monitor_video_service.sh 不存在${NC}"
    exit 1
fi

if [ ! -f "start_video_service.sh" ]; then
    echo -e "${RED}❌ 启动脚本 start_video_service.sh 不存在${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 所有必要文件都存在${NC}"
echo ""

# 更新服务文件中的用户和工作目录
echo -e "${YELLOW}🔧 配置服务文件...${NC}"
sed -i.bak "s|User=.*|User=$CURRENT_USER|g" "$SERVICE_FILE"
# 注意：服务文件现在使用相对路径，所以这里需要设置为绝对路径
sed -i.bak "s|WorkingDirectory=.*|WorkingDirectory=$CURRENT_DIR|g" "$SERVICE_FILE"
sed -i.bak "s|ExecStart=.*|ExecStart=$CURRENT_DIR/monitor_video_service.sh|g" "$SERVICE_FILE"
sed -i.bak "s|PYTHONPATH=.*|PYTHONPATH=$CURRENT_DIR|g" "$SERVICE_FILE"
sed -i.bak "s|ReadWritePaths=.*|ReadWritePaths=$CURRENT_DIR/output|g" "$SERVICE_FILE"
sed -i.bak "s|ReadWritePaths=.*|ReadWritePaths=$CURRENT_DIR/temp_uploads|g" "$SERVICE_FILE"
sed -i.bak "s|ReadWritePaths=.*|ReadWritePaths=$CURRENT_DIR/logs|g" "$SERVICE_FILE"

echo -e "${GREEN}✅ 服务文件配置完成${NC}"
echo ""

# 创建必要的目录
echo -e "${YELLOW}📁 创建必要目录...${NC}"
mkdir -p output
mkdir -p temp_uploads
mkdir -p logs
mkdir -p config

echo -e "${GREEN}✅ 目录创建完成${NC}"
echo ""

# 给脚本执行权限
echo -e "${YELLOW}🔐 设置执行权限...${NC}"
chmod +x monitor_video_service.sh
chmod +x start_video_service.sh

echo -e "${GREEN}✅ 执行权限设置完成${NC}"
echo ""

# 检查systemd是否可用
if ! command -v systemctl &> /dev/null; then
    echo -e "${YELLOW}⚠️  systemd不可用，将使用launchd (macOS)${NC}"
    
    # 创建launchd plist文件
    cat > "com.feynman.video.service.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.feynman.video.service</string>
    <key>ProgramArguments</key>
    <array>
        <string>$CURRENT_DIR/monitor_video_service.sh</string>
    </array>
    <key>WorkingDirectory</key>
    <string>$CURRENT_DIR</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>$CURRENT_DIR/logs/service.log</string>
    <key>StandardErrorPath</key>
    <string>$CURRENT_DIR/logs/service_error.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>TAVILY_API_KEY</key>
        <string>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</string>
        <key>PYTHONPATH</key>
        <string>$CURRENT_DIR</string>
    </dict>
</dict>
</plist>
EOF

    echo -e "${GREEN}✅ launchd配置文件创建完成${NC}"
    echo ""
    
    # 安装launchd服务
    echo -e "${YELLOW}📦 安装launchd服务...${NC}"
    cp "com.feynman.video.service.plist" ~/Library/LaunchAgents/
    launchctl load ~/Library/LaunchAgents/com.feynman.video.service.plist
    
    echo -e "${GREEN}✅ launchd服务安装完成${NC}"
    echo ""
    
    echo -e "${BLUE}📋 服务管理命令:${NC}"
    echo "  启动服务: launchctl start com.feynman.video.service"
    echo "  停止服务: launchctl stop com.feynman.video.service"
    echo "  查看状态: launchctl list | grep feynman"
    echo "  卸载服务: launchctl unload ~/Library/LaunchAgents/com.feynman.video.service.plist"
    
else
    # 安装systemd服务
    echo -e "${YELLOW}📦 安装systemd服务...${NC}"
    sudo cp "$SERVICE_FILE" /etc/systemd/system/
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    
    echo -e "${GREEN}✅ systemd服务安装完成${NC}"
    echo ""
    
    echo -e "${BLUE}📋 服务管理命令:${NC}"
    echo "  启动服务: sudo systemctl start $SERVICE_NAME"
    echo "  停止服务: sudo systemctl stop $SERVICE_NAME"
    echo "  查看状态: sudo systemctl status $SERVICE_NAME"
    echo "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
    echo "  重启服务: sudo systemctl restart $SERVICE_NAME"
fi

echo ""
echo -e "${GREEN}🎉 安装完成！${NC}"
echo ""
echo -e "${BLUE}📝 下一步:${NC}"
echo "1. 检查配置文件 config/config.yaml 是否存在"
echo "2. 确保所有依赖已安装"
echo "3. 启动服务进行测试"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo "- 服务日志将保存在 logs/ 目录中"
echo "- 监控日志文件: video_service_monitor.log"
echo "- 服务日志文件: video_service.log"
