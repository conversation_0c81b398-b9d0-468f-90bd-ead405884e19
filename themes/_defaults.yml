# themes/_defaults.yml
# 所有主题的默认基础配置
# 新主题会继承这些值，并只覆盖需要修改的部分

# 对应 ColorPalette
colors:
  text: '#0D47A1'
  background: '#FFFFFF'
  # 主次颜色三级系统
  primary: '#2196F3'      # 主色
  primary_light: '#BBDEFB' # 主色浅色
  primary_dark: '#1565C0'  # 主色深色
  secondary: '#4CAF50'     # 次色
  secondary_light: '#C8E6C9' # 次色浅色  
  secondary_dark: '#2E7D32'  # 次色深色
  accent: '#FFC107'        # 强调色
  accent_light: '#FFF9C4'  # 强调色浅色
  accent_dark: '#F57F17'   # 强调色深色
  # 背景色系统
  surface: '#F5F5F5'       # 表面色
  surface_light: '#FAFAFA' # 浅色表面
  surface_dark: '#EEEEEE'  # 深色表面
  cyclic_colors: ['#2196F3', '#4CAF50', '#FFC107', '#F44336', '#9C27B0']
  h1: '#1565C0'
  h2: '#1976D2'
  h3: '#1E88E5'

# 对应 Typography
typography:
  fonts:
    body: "PingFang SC"
    code: "Menlo"
  sizes:
    h1: 48
    h2: 40
    h3: 32
    h4: 28
    body: 24
    code: 20
    small: 18
  # 行间距等级系统
  line_spacing:
    tight: 1.0      # 紧密间距
    normal: 1.2     # 正常间距
    relaxed: 1.4    # 放松间距
    loose: 1.6      # 宽松间距
    extra_loose: 1.8 # 超宽松间距

# 对应 ComponentStyles
components:
  background_opacity: 0.9
  background_corner_radius: 0.3
  stroke_width: 2.0
  list:
    background_opacity: 0.8
    corner_radius: 0.2
    padding: 0.3
  code_block:
    background_color: '#EEEEEE'
    corner_radius: 0.2
    padding: 0.3
  blockquote:
    border_width: 3.0
  timeline:
    node_radius: 0.6
    connector_width: 4.0
  table:
    line_width: 1.5
    cell_padding: 0.4
    row_padding: 0.2
    style: 1
