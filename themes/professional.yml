# themes/professional.yml
# 专业风格，只定义与 _defaults.yml 不同的地方

colors:
  text: '#FFFFFF'
  background: '#000000'
  # 主次颜色三级系统（暗色主题）
  primary: '#58C4DD'       # 主色
  primary_light: '#9CDCEB' # 主色浅色
  primary_dark: '#0277BD'  # 主色深色
  secondary: '#81C784'     # 次色
  secondary_light: '#C8E6C9' # 次色浅色  
  secondary_dark: '#388E3C'  # 次色深色
  accent: '#FFD54F'        # 强调色
  accent_light: '#FFF9C4'  # 强调色浅色
  accent_dark: '#F57F17'   # 强调色深色
  # 背景色系统（暗色主题）
  surface: '#212121'       # 表面色
  surface_light: '#424242' # 浅色表面
  surface_dark: '#121212'  # 深色表面
  h1: '#9CDCEB'
  h2: '#58C4DD'
  h3: '#C7E9F1'

typography:
  fonts:
    body: "Microsoft YaHei" # 商业场景更常用的字体
  sizes:
    h1: 46 # 稍微小一点，更稳重
    body: 22
