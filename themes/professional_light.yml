# themes/professional_light.yml
# 专业浅色风格，基于 professional.yml 的浅色版本
# 只定义与 _defaults.yml 不同的地方

colors:
  text: '#000000'
  background: '#FED3A8'
  # 主次颜色三级系统（浅色主题）
  primary: '#0277BD'       # 主色 (更深的蓝色，适合浅色背景)
  primary_light: '#B3E5FC' # 主色浅色
  primary_dark: '#01579B'  # 主色深色
  secondary: '#2E7D32'     # 次色 (更深的绿色)
  secondary_light: '#E8F5E8' # 次色浅色  
  secondary_dark: '#1B5E20'  # 次色深色
  accent: '#D32F2F'        # 强调色 (红色)
  accent_light: '#FFEBEE'  # 强调色浅色
  accent_dark: '#B71C1C'   # 强调色深色
  # 背景色系统（浅色主题）
  surface: '#F8F9FA'       # 表面色
  surface_light: '#FFFFFF' # 浅色表面
  surface_dark: '#E9ECEF'  # 深色表面
  h1: '#01579B'
  h2: '#0277BD'
  h3: '#0288D1'

typography:
  fonts:
    body: "Microsoft YaHei" # 商业场景更常用的字体
  sizes:
    h1: 46 # 稍微小一点，更稳重
    body: 22

# 专业主题特定组件样式调整
components:
  background_opacity: 0.95
  list:
    background_color: '#F8F9FA'
    background_opacity: 0.9
  code_block:
    background_color: '#F1F3F4'
  blockquote:
    border_color: '#0277BD'
