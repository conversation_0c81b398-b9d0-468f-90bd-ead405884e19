# Generated by Feynman DSL v2 Code Generator
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
sys.path.insert(0, root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class A(FeynmanScene):
    config.background_color = '#000000'

    def construct(self):
        self.add_background()

        # Action 1: animate_markdown
        self.scene_method_name = 'animate_markdown'
        animate_markdown(
            scene=self,
            title="语言的秘密：顺序的力量",
            content="# 为什么语序很重要？\n\n- “我打了他” vs “他打了我”\n- **Token (词)** + **Position (位置)** = **Meaning (意义)**",
            narration="大家好！今天我们来聊一个AI领域里很有意思的话题。大家看这两句话，'我打了他'和'他打了我'，用的词一模一样，但意思天差地别。这就是语序的力量。对于我们来说，理解一句话不仅要知道它有哪些词，更要明白这些词的排列顺序。那么，我们怎么才能让计算机模型也掌握这个秘密呢？"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
