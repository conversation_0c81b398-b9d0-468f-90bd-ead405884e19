#!/usr/bin/env python3
"""
Unified Storyboard Agent: From Analysis to Storyboard in a Single Step.
"""

import json
import os
import sys
from pathlib import Path
from typing import Any

from loguru import logger

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.agents import ChatAgent
from camel.messages import BaseMessage

from utils.common import CommonUtils, Config
from utils.create_llm_model import create_model


class UnifiedStoryboardAgent:
    """
    An agent that reads a technical analysis document and generates a complete
    Manim DSL storyboard JSON in a single LLM call.
    """

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        Initializes the UnifiedStoryboardAgent.

        Args:
            config_path: Path to the configuration file.
        """
        self.config = Config(config_path)
        self.model = create_model()
        self.agent = self._create_agent()
        self.animation_functions_guide = self._load_animation_functions()
        logger.info("UnifiedStoryboardAgent initialized.")

    def _create_agent(self) -> ChatAgent:
        """Creates the backing ChatAgent."""
        system_message = BaseMessage.make_assistant_message(
            role_name="Unified Video Producer",
            content="You are an expert video producer and visual storyteller. Your mission is to take a raw technical analysis document and, in a single step, convert it into a complete, high-level animation storyboard in JSON format. You must think step-by-step: first, devise a narrative, then for each part of the narrative, select the best visual function and extract its parameters from the source material. Use objective language, prioritize visual content, include specific data, and follow purpose requirements precisely. Your final output must be only the valid JSON.",
        )
        return ChatAgent(system_message=system_message, model=self.model)

    def _load_animation_functions(self) -> str:
        """
        Loads the animation functions documentation and filters it based on the
        settings in config.yaml.
        """
        doc_path = "docs/animation_functions.md"
        if not os.path.exists(doc_path):
            logger.error(f"Animation functions document not found at: {doc_path}")
            return "Error: Animation functions document not found."

        logger.info(f"Loading animation functions from {doc_path}")
        full_content = CommonUtils.read_file(doc_path)

        # Get the enabled functions from the config, default to an empty dict if not found
        enabled_functions_config = self.config.config.get("animate_functions", {})
        if not enabled_functions_config:
            logger.warning("`animate_functions` section not found in config.yaml. All functions will be loaded.")
            return full_content
        all_docs = {}
        for func, is_enabled in enabled_functions_config.items():
            if not is_enabled:
                logger.debug(f"{func} is disabled")
                continue
            doc_path = f"docs/{func}.md"
            if os.path.exists(doc_path):
                content = open(doc_path).read()
                all_docs[func] = content
            else:
                logger.warning(f"{doc_path} does not exist")
        if not all_docs:
            logger.warning(
                "No animation functions are enabled in config.yaml. The prompt will lack function definitions."
            )
            return "# Animation Functions\n\n(No functions enabled in config.yaml)"
        final_content = "\n---\n".join(all_docs[k] for k in sorted(all_docs.keys()))

        logger.info(f"Successfully loaded {len(all_docs)} enabled animation functions.")
        return final_content

    def _parse_duration_to_minutes(self, duration_str: str) -> int:
        """Parse duration string to minutes (e.g., '2min' -> 2, '5分钟' -> 5)."""
        import re
        # Extract numbers from duration string
        numbers = re.findall(r'\d+', duration_str)
        if numbers:
            return int(numbers[0])
        return 2  # Default to 2 minutes if parsing fails
    
    def _calculate_target_word_count(self, duration_str: str) -> int:
        """Calculate target narration word count based on duration (150 words per minute)."""
        minutes = self._parse_duration_to_minutes(duration_str)
        return minutes * 150

    def _build_master_prompt(self, analysis_content: str, purpose: str) -> str:
        """Builds the master prompt for the single LLM call."""
        
        # Get video settings from config
        duration_setting = self.config.config.get("video", {}).get("duration", "2min")
        content_language = self.config.config.get("video", {}).get("content_language", "Chinese")
        
        # Calculate target word count for narration
        target_word_count = self._calculate_target_word_count(duration_setting)

        prompt = f"""
You are an expert video producer and visual storyteller. Your mission is to take a raw technical analysis document and, in a single step, convert it into a complete, high-level animation storyboard in JSON format.

**User's Goal for the Video:**
<purpose>
{purpose}
</purpose>

**CRITICAL VIDEO SPECIFICATIONS:**
<content_language>
Content Language: {content_language}
</content_language>

<narration_word_count>
Total Narration Word Count: {target_word_count} words (calculated at 150 words per minute)
</narration_word_count>

**Your Thought Process & Instructions:**

1.  **Analyze and Strategize**: Read the entire analysis report and the user's goal. Identify the core message, the key supporting points, and the most logical sequence to present them to achieve the user's goal.
2.  **Narrate and Visualize Simultaneously**: As you mentally walk through your narrative, for each piece of information you want to present, immediately ask yourself: "Which function from your toolbox is the absolute best way to visualize this?"
3.  **Extract and Format**: Once you've chosen a function, meticulously extract the required data from the analysis report and format it into the `params` object for that function. Ensure all required parameters are present. The `narration` parameter is crucial – write a concise and engaging voiceover for each scene that explains the visual. IMPORTANT: The total word count of ALL narration texts across all scenes must equal approximately {target_word_count} words.
4.  **Generate Final JSON**: Assemble all the scenes into a final `storyboard` JSON array. Your entire output must be **only the valid JSON**, with no extra text, explanations, or markdown code blocks. Start with `{{` and end with `}}`.

**Additional Requirements:**
- Use objective language: avoid subjective terms and first-person pronouns (I, we, us, our)
- Prioritize video files and key images when available in **Input: Technical Analysis Report**
- Strictly follow ALL specifications above, especially the content language and word count of narration requirements

**Final Output Requirement:**
Produce the complete storyboard JSON based on the analysis report. The root of the JSON object must be a key named "actions" containing a list of animation functions.

**Your Toolbox (Available Animation Functions):**
Here is the complete documentation for the animation functions you can use. You MUST adhere to this schema precisely.
```markdown
{self.animation_functions_guide}
```

**Input: Technical Analysis Report**
```markdown
{analysis_content}
```

"""
        return prompt

    def _parse_and_validate_json(self, content: str) -> Any:
        """Parses and validates the JSON output from the LLM."""
        try:
            # Clean up potential markdown fences
            if content.strip().startswith("```json"):
                content = content.strip()[7:-4]

            parsed_json = json.loads(content)

            if "actions" not in parsed_json or not isinstance(parsed_json["actions"], list):
                raise ValueError("JSON output is missing 'actions' list.")

            logger.info(f"Successfully parsed JSON. Found {len(parsed_json['actions'])} scenes.")
            return parsed_json
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON: {e}")
            logger.error(f"Problematic content: {content[:500]}...")
            raise
        except ValueError as e:
            logger.error(f"JSON validation failed: {e}")
            raise

    def run(self, analysis_content: str, purpose: str, output_file: str) -> dict:
        """
        Executes the agent's main logic.

        Args:
            analysis_content: The content of the analysis.md file.
            purpose: The user's purpose for the video.
            output_file: The path to save the final storyboard.json.

        Returns:
            A dictionary with the status and output file path.
        """
        try:
            logger.info("Building the master prompt...")
            master_prompt = self._build_master_prompt(analysis_content, purpose)

            # For debugging, save the prompt
            prompt_debug_path = os.path.join(os.path.dirname(output_file), "unified_agent_prompt.md")
            CommonUtils.save_file(master_prompt, prompt_debug_path)
            logger.info(f"Master prompt saved to {prompt_debug_path}")

            logger.info("Calling LLM to generate the unified storyboard...")
            user_message = BaseMessage.make_user_message(role_name="User", content=master_prompt)
            self.agent.reset()
            response = self.agent.step(user_message)

            if not response.msgs:
                raise Exception("LLM returned no response.")

            generated_content = response.msgs[0].content

            logger.info("Parsing and validating LLM response...")
            final_storyboard_json = self._parse_and_validate_json(generated_content)

            logger.info(f"Saving storyboard to {output_file}...")
            CommonUtils.save_file(
                json.dumps(final_storyboard_json, ensure_ascii=False, indent=2),
                output_file,
            )

            return {
                "status": "success",
                "output_file": output_file,
                "scene_count": len(final_storyboard_json["actions"]),
            }

        except Exception as e:
            logger.error(f"An error occurred in the unified agent run: {e}")
            import traceback

            traceback.print_exc()
            return {"status": "error", "error": str(e)}


if __name__ == "__main__":
    # This is a simple test runner for the agent.
    # Usage: python agents/unified_storyboard_agent.py <path_to_analysis.md> <purpose> <output_storyboard.json>
    if len(sys.argv) != 3:
        print("Usage: python agents/unified_storyboard_agent.py <path_to_analysis.md> <purpose>")
        sys.exit(1)

    analysis_file = sys.argv[1]
    purpose_arg = sys.argv[2]
    output_file_arg = str(Path(analysis_file).with_suffix(".json"))

    if not os.path.exists(analysis_file):
        print(f"Error: Analysis file not found at {analysis_file}")
        sys.exit(1)

    analysis_md_content = CommonUtils.read_file(analysis_file)

    agent = UnifiedStoryboardAgent()
    result = agent.run(analysis_md_content, purpose_arg, output_file_arg)

    if result["status"] == "success":
        print("✅ Unified storyboard generation successful!")
        print(f"   - Output file: {result['output_file']}")
        print(f"   - Scenes created: {result['scene_count']}")
    else:
        print(f"❌ Unified storyboard generation failed: {result['error']}")
