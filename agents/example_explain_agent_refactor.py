from dotenv import load_dotenv

load_dotenv()

import os
import re
import sys
from typing import Any

from camel.messages import BaseMessage
from loguru import logger

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from camel.agents import ChatAgent

# 导入create_model函数
from utils.create_llm_model import create_model

"""
markdown格式要求：
# 为什么要学习这个概念？(≤100字)
[用一段话先说明这个概念名称、定义、实际价值和应用场景，然后抛出一个吸引人的问题或现象，激发读者的兴趣和好奇心]

## 背景设定
[设置一个具体且简洁的应用场景, 数据规模适合视频呈现]

# 总结
## 核心原理揭示(≤100字)
[用简短句子深入解释为什么这样做，揭示本质机制。同时要回扣到开头抛出的问题，说明这个例子如何解决了那个问题]

## 核心步骤总结(≤100字)
1. 步骤1：[详细说明]
2. 步骤2：[详细说明]
3. ...

"""

# 定义综合例子生成专家角色提示
EXAMPLE_GENERATOR_PROMPT = """
你是一位顶级的动画故事板生成专家，专门为复杂概念创造高质量、易懂的动画故事板。

## 核心任务
面向对象：{purpose}
主题：{topic}

## 核心能力
1. **概念提取与分析**：准确识别核心概念、原理或题目，分析其关键要素和步骤
2. **例子设计**：根据核心概念的分析，设计一个具体、量化、准确的例子来解释抽象概念，保证例子逻辑完整
3. **动画设计**：根据整体例子的描述，设计一个主体元素，每一步都围绕这个主体展开介绍，例如
    - 介绍排序算法，主体动画是一组数字卡片之间的位置变化
    - 介绍RNN算法，围绕RNN的网络结构流程来设计动画
4. **数学公式处理**：使用LaTeX格式正确表示数学公式或方程
5. **针对性讲解**：紧扣purpose和topic，确保内容符合目标受众需求

## 讲解提纲原则
**重要**生动形象的介绍一个简单、具体的例子，重点体现出讲解思路
1. 详细规划阶段数量和每个阶段的核心内容，保证核心内容清晰完整，前后逻辑顺畅
2. 阶段数量不超过10个，每个阶段的核心内容20-30字
3. 根据主题内容，最后阶段根据情况添加实际应用

## Group组织原则
**重要**元素必须精简，适合动画展现，体现出含义即可
1. 同一个数据结构的元素组织成Group，详细描述元素内容、数量、朝向、相对位置，必须准确到单步代码执行的程度
2. Group数量必须精简，数据结构相似的元素组织成一个Group，并在多个阶段复用，禁止重复创建相同的Group
2. Group内部元素必须精简，表达出意思即可，不管什么维度都不要超过4个元素，包括表格行列数，文字数量，列表数量等，超出部分用省略号代替
3. 文本描述元素集中在文字介绍区域和结果区域，主内容区域必须完全通过视觉元素的行为传递语义（最大限度减少文字）
4. 避免装饰性元素，专注于信息传达，特别是在背景介绍中，避免生成机器人、书籍等纯装饰性元素

## 核心动作原则
1. 核心动作必须包含与主题相关的灵魂动画，通过动画体现出主要思想，例如元素移动、旋转、删除、合并、替换等
    - BPE中的两个字符合成为新字符
    - 排序中的数字位置移动交换
    - 归并排序中的数据原地分组和位置交换排序
2. **重要**核心动作描述必须准确到单步代码执行的程度，避免笼统模糊的动作描述
    - 动作描述必须详细，包括形状、位置、朝向、颜色、大小、数量等变化，描述清楚初始状态、如何变化和最终状态
    - 动作描述必须以相对位置作为描述基础，不要使用绝对位置，例如移动到x上方，绕x旋转
    - 核心动作必须保持精简，尽可能减少辅助元素和绝对位置移动，保证动画内容准确

## 区域设置要求（严格字数限制）
1. 标题区域：视频主题，限制6个字以内，全程保持不动
2. 阶段介绍区域：阶段名称，限制10个字以内
3. 主内容区域：主要内容展示区，10.0×5.0的长条空间
4. 辅助区域：辅助区域，4.0×5.0的长条空间，非必要不展示
5. 结果区域：重点结果展示区，限制15个字以内，非必要不展示

请按照以下markdown格式输出

## 输出格式
```markdown
<SCENE_OUTLINE>

## 讲解提纲
- [阶段1名称]：[阶段1核心内容]
- [阶段2名称]：[阶段1核心内容]
- [阶段N名称]：[阶段1核心内容]

**Group定义**：
- [Group1名称]：[详细描述元素内容、数量、相对位置]
- [Group2名称]：[详细描述元素内容、数量、相对位置]
- [Group3名称]：[详细描述元素内容、数量、相对位置]
- 举例：**Group_Corpus**: 由多个`VGroup(Rectangle(), Tex())`组成，每个`VGroup`代表一个字符或子词单元。

## 详细步骤解释
### 阶段1：[阶段名称]
**区域布局**：
- 标题区域：[Group名称]
- 文字介绍区域：[Group名称]
- 主内容区域：[Group名称]
- 辅助区域：[Group名称]
- 结果区域：[Group名称]
**核心动作**：[时间顺序]
- 动作1：[group元素+动作+结果]
- 动作2：[group元素+动作+结果]
- 举例：每一个`(u,g)`对，`g方块`和`u方块`[合并]为一个新的、稍宽的`ug方块`。
**讲解文案**：[当前阶段内容讲解，逻辑和动画逻辑一致，字数尽量精炼，不超过50字]

### 阶段N：[阶段名称]
**区域布局**：
- 标题区域：[Group名称]
- 文字介绍区域：[Group名称]
- 主内容区域：[Group名称]
- 辅助区域：[Group名称]
- 结果区域：[Group名称]
**核心动作**：[时间顺序]
- 动作1：[group元素+动作+结果]
- 动作2：[group元素+动作+结果]
**讲解文案**：[当前阶段内容讲解，逻辑和动画逻辑一致，字数尽量精炼，不超过50字]

</SCENE_OUTLINE>
```

## 质量检查点
- Group数量是否精简？内部元素是否足够精简？
- 核心动作是否包含主题相关的灵魂动画？
- 动画描述是否准确到代码可执行？
- 讲解逻辑是否正确？逻辑和内容是否准确对应？避免出现逻辑错误的情况
"""



class ExampleExplainAgent:
    """
    例子解释代理

    职责：
    1. 从配置文件读取purpose和topic
    2. 通过roleplay方式生成高质量例子
    3. 质量评审和迭代改进
    4. 输出最终的markdown文件
    """

    class Config:
        """例子解释代理配置子模块"""

        def __init__(self, config_dict=None):
            """初始化配置"""
            if not config_dict:
                config_dict = {}

            # 模型配置
            self.model = {
                "type": config_dict.get("model", {}).get("type", "google/gemini-2.5-flash-preview-05-20"),
                "temperature": config_dict.get("model", {}).get("temperature", 0.7),
                "api": config_dict.get("model", {}).get("api", {}),
            }

            # 例子解释代理特定配置
            example_config = config_dict.get("example_explain", {})
            self.purpose = example_config.get("purpose", "为一般受众解释复杂概念")
            self.topic = example_config.get("topic", "通用概念")
            self.max_rounds = example_config.get("max_rounds", 2)
            self.quality_threshold = example_config.get("quality_threshold", "良好")
            self.output_dir = f"output/{self.topic}"

    def __init__(self, config_path="config/config.yaml"):
        """初始化例子解释代理"""
        # 加载配置
        config_dict = self._load_yaml_config(config_path)

        # 初始化配置子模块
        self.config = self.Config(config_dict)

        # 初始化模型
        self.model = self._create_model()

        logger.info("例子解释代理初始化完成")

    def _load_yaml_config(self, config_path):
        """从YAML文件加载配置"""
        try:
            with open(config_path, encoding="utf-8") as file:
                config = yaml.safe_load(file)
            logger.info("从 %s 加载配置", config_path)
            return config
        except Exception as e:
            logger.error(f"加载配置出错: {str(e)}")
            return {}

    def _create_model(self):
        """创建模型实例"""
        # 直接使用utils中的create_model函数
        return create_model(config_file="config/config.yaml")

    def generate_example(self) -> str:
        """
        直接用ChatAgent单轮生成高质量例子

        返回:
        - str: 最终的例子内容
        """
        try:
            # 构造 system_message
            formatted_generator_prompt = EXAMPLE_GENERATOR_PROMPT.format(
                purpose=self.config.purpose, topic=self.config.topic
            )
            system_message = BaseMessage.make_assistant_message(
                role_name="例子生成专家", content=formatted_generator_prompt
            )
            # 构造 user prompt
            user_prompt = (
                f"请为主题“{self.config.topic}”面向对象“{self.config.purpose}”生成高质量例子，严格按上述格式输出。"
            )
            # 实例化 ChatAgent
            agent = ChatAgent(system_message=system_message, model=self.model, output_language="chinese")
            logger.info("开始单轮ChatAgent生成例子")
            response = agent.step(user_prompt)
            content = response.msg.content
            # 尝试提取Markdown
            extracted_markdown = self._extract_markdown(content)
            if extracted_markdown and len(extracted_markdown) > 100:
                return extracted_markdown.strip()
            # 若未能提取，直接返回原内容
            return content.strip()
        except Exception as e:
            import traceback

            logger.error(f"ChatAgent生成例子失败: {str(e)}")
            logger.error(traceback.format_exc())
            return ""

    def save_example(self, content: str, output_file: str = None) -> str:
        """
        保存例子内容到文件

        参数:
        - content: 例子内容
        - output_file: 输出文件路径

        返回:
        - str: 保存的文件路径
        """
        if output_file is None:
            output_file = f"{self.config.output_dir}/example_explain.md"

        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)

        # 写入文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(content)
            logger.info(f"例子内容已保存到 {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存例子内容失败: {str(e)}")
            # 尝试使用备用路径
            backup_file = "output/example_backup.md"
            try:
                with open(backup_file, "w", encoding="utf-8") as f:
                    f.write(content)
                logger.info(f"例子内容已保存到备用文件 {backup_file}")
                return backup_file
            except Exception as e2:
                logger.error(f"保存到备用文件也失败: {str(e2)}")
                return ""

    def run(self, output_file: str = None, max_rounds: int = None) -> dict[str, Any]:
        """
        运行例子解释的完整流程

        参数:
        - output_file: 输出文件路径
        - max_rounds: 最大迭代轮数

        返回:
        - Dict: 包含处理结果的字典
        """
        result = {}

        try:
            logger.info(f"开始处理主题: {self.config.topic}")
            logger.info(f"面向对象: {self.config.purpose}")

            # 通过角色对话生成例子
            logger.info("开始通过角色对话生成例子")
            final_example = self.generate_example()
            result["final_example_length"] = len(final_example)

            # 保存结果
            logger.info("保存生成的例子")
            saved_file = self.save_example(final_example, output_file)
            result["saved_file"] = saved_file
            result["success"] = True
            result["topic"] = self.config.topic
            result["purpose"] = self.config.purpose

            logger.info("例子解释处理完成")
            return result

        except Exception as e:
            logger.error(f"例子解释处理出错: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            result["error"] = str(e)
            result["success"] = False
            return result

    def _extract_markdown(self, text: str) -> str:
        """从文本中提取Markdown内容"""
        # 首先检查是否有明确标记的Markdown段落
        markdown_markers = [
            "===开始：最终例子===",
            "```markdown",
            "# 核心概念识别",
            "## 核心概念识别",
            "# 核心概念例子解释",
        ]

        # 尝试通过明确的标记寻找Markdown起始位置
        start_index = -1
        for marker in markdown_markers:
            if marker in text:
                possible_start = text.find(marker)
                if start_index == -1 or possible_start < start_index:
                    start_index = possible_start

        # 如果找到了标记，从标记处开始提取
        if start_index != -1:
            # 从标记处开始，但跳过标记本身（除非是Markdown标题）
            if text[start_index : start_index + 2] == "# ":
                extracted_text = text[start_index:]
            elif text[start_index : start_index + 3] == "## ":
                extracted_text = text[start_index:]
            else:
                # 找到标记后的第一个换行
                next_line = text.find("\n", start_index)
                if next_line != -1:
                    extracted_text = text[next_line + 1 :]
                else:
                    extracted_text = text[start_index:]

            # 寻找终止标记
            end_markers = ["===结束：最终例子===", "```", "---", "以上是生成的例子"]
            for marker in end_markers:
                if marker in extracted_text:
                    extracted_text = extracted_text.split(marker)[0]

            return extracted_text.strip()

        # 如果没有明确的标记，尝试通过Markdown代码块提取
        markdown_pattern = r"```(?:markdown)?\s*([\s\S]*?)```"
        matches = re.findall(markdown_pattern, text, re.DOTALL)

        if matches:
            # 选择最长的匹配结果
            longest_match = max(matches, key=len).strip()
            # 确保这是真正的Markdown内容而不是代码
            if "#" in longest_match and len(longest_match) > 200:
                return longest_match

        # 如果没有明确的Markdown代码块，但文本看起来已经是Markdown格式
        if re.search(r"^#\s+", text, re.MULTILINE):
            # 尝试找到第一个标题
            match = re.search(r"^#\s+", text, re.MULTILINE)
            if match:
                start = match.start()
                extracted = text[start:].strip()

                # 检查是否包含完整的结构（不只是代码）
                if "##" in extracted and len(extracted) > 500:
                    return extracted

        # 如果以上都失败，检查是否整个文本就是Markdown格式
        if text.strip().startswith("#") and "##" in text:
            return text.strip()

        # 无法提取有效的Markdown，返回空字符串让调用者使用原始文本
        logger.warning("无法提取有效的Markdown内容")
        return ""


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="例子解释代理 - 生成高质量的概念解释例子")
    parser.add_argument("--config", type=str, default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--output", type=str, help="输出文件路径")
    parser.add_argument("--max-rounds", type=int, help="最大迭代轮数")

    args = parser.parse_args()

    try:
        # 创建代理
        print("🚀 正在初始化例子解释代理...")
        agent = ExampleExplainAgent(config_path=args.config)

        print(f"📁 项目名称: {agent.config.topic}")
        print(f"📂 输出目录: {agent.config.output_dir}")

        # 运行处理
        print("⚡ 开始处理，请稍候...")
        result = agent.run(output_file=args.output, max_rounds=args.max_rounds)

        # 输出结果
        if result.get("success"):
            print("✅ 例子解释处理成功！")
            print(f"📋 主题: {result.get('topic', '未知')}")
            print(f"👥 面向对象: {result.get('purpose', '未知')}")
            print(f"📝 最终例子长度: {result.get('final_example_length', 0)} 字符")
            print(f"💾 保存文件: {result.get('saved_file', '未保存')}")
        else:
            print(f"❌ 例子解释处理失败: {result.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 程序运行出错: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
