#!/usr/bin/env python3
"""
GitHub源代理 - 重构简化版本
移除过度设计的逻辑，专注于核心功能
"""

import os
import re
import sys
from pathlib import Path
from typing import Optional

import requests
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.common import AgentFactory, CommonUtils, Config, FileExtensions, MediaClassifier
from utils.create_llm_model import create_model


class GitHubAPI:
    """简化的GitHub API客户端"""

    def __init__(self):
        self.token = os.getenv("GITHUB_TOKEN")
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
            "Authorization": f"token {self.token}" if self.token else "",
        }

    def get_repo_info(self, owner: str, repo: str) -> dict:
        """获取基本仓库信息"""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}"
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()

            data = response.json()
            return {
                "name": data.get("name", ""),
                "description": data.get("description", ""),
                "stars": data.get("stargazers_count", 0),
                "forks": data.get("forks_count", 0),
                "language": data.get("language", ""),
                "created_at": data.get("created_at", ""),
                "updated_at": data.get("updated_at", ""),
                "topics": data.get("topics", []),
            }
        except Exception as e:
            logger.warning(f"获取仓库信息失败: {e}")
            return {"name": repo, "error": str(e)}


class MediaDownloader:
    """改进版媒体下载器"""

    def __init__(self, output_dir: str, project_name: str):
        self.output_dir = Path(output_dir) / "media"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.project_name = project_name

    def download_from_readme(self, owner: str, repo: str, branch: str = "main") -> list[dict]:
        """从README下载重要媒体"""
        try:
            # 获取README内容
            readme_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/README.md"
            response = requests.get(readme_url, timeout=10)
            if response.status_code != 200:
                # 尝试master分支
                readme_url = f"https://raw.githubusercontent.com/{owner}/{repo}/master/README.md"
                response = requests.get(readme_url, timeout=10)
                if response.status_code != 200:
                    return []

            readme_content = response.text
            media_files = []

            # 提取图片和媒体文件
            # 查找markdown格式的图片: ![alt](url)
            md_image_pattern = r"!\[([^\]]*)\]\(([^)]+)\)"
            md_images = re.findall(md_image_pattern, readme_content)

            # 查找HTML格式的图片: <img src="url" alt="alt">
            html_image_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*(?:alt=["\']([^"\']*)["\'])?[^>]*>'
            html_images = re.findall(html_image_pattern, readme_content)

            # 查找HTML格式的视频: <video src="url">
            html_video_pattern = r'<video[^>]+src=["\']([^"\']+)["\'][^>]*/?>'
            html_videos = re.findall(html_video_pattern, readme_content)

            # 查找markdown格式的视频链接
            video_pattern = r"!\[([^\]]*)\]\(([^)]+\.(?:mp4|avi|mov|wmv|flv|webm))\)"
            videos = re.findall(video_pattern, readme_content)

            # 查找其他媒体链接
            media_pattern = r"\[([^\]]*)\]\(([^)]+\.(?:jpg|jpeg|png|gif|svg|webp|mp4|avi|mov|wmv|flv|webm))\)"
            other_media = re.findall(media_pattern, readme_content)

            logger.info(
                f"从README中找到: {len(md_images)}个MD图片, {len(html_images)}个HTML图片, {len(html_videos)}个HTML视频, {len(videos)}个MD视频, {len(other_media)}个总媒体链接"
            )

            # 处理所有找到的媒体
            all_found_media = []

            # 添加markdown图片
            for alt, url in md_images:
                all_found_media.append(("image", alt, url, "markdown"))

            # 添加HTML图片
            for url, alt in html_images:
                all_found_media.append(("image", alt or "", url, "html"))

            # 添加HTML视频
            for url in html_videos:
                # 从上下文中提取视频描述
                context = self._extract_context(readme_content, url)  # noqa: F841
                alt = self._extract_video_title(readme_content, url)
                all_found_media.append(("video", alt, url, "html_video"))

            # 添加markdown视频
            for alt, url in videos:
                all_found_media.append(("video", alt, url, "markdown_video"))

            # 添加其他媒体
            for alt, url in other_media:
                media_type = (
                    "video" if url.lower().endswith((".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm")) else "image"
                )
                all_found_media.append((media_type, alt, url, "other"))

            # 下载媒体文件
            for media_type, alt, url, source in all_found_media:
                # 使用共用的分类器分类媒体重要性
                is_important, category, enhanced_desc = MediaClassifier.classify_importance(
                    f"{alt} {url}", media_type, self._extract_context(readme_content, url)
                )

                # 跳过不重要的媒体
                if not is_important:
                    continue

                # 处理相对路径
                if not url.startswith(("http://", "https://")):
                    # 尝试多个可能的分支
                    possible_branches = [branch, "main", "master"]
                    for test_branch in possible_branches:
                        if url.startswith("./"):
                            test_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{test_branch}/{url[2:]}"
                        elif url.startswith("/"):
                            test_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{test_branch}{url}"
                        else:
                            test_url = f"https://raw.githubusercontent.com/{owner}/{repo}/{test_branch}/{url}"

                        # 测试URL是否可访问
                        try:
                            test_response = requests.head(test_url, timeout=5)
                            if test_response.status_code == 200:
                                url = test_url
                                logger.info(f"找到可访问的媒体URL: {url}")
                                break
                        except Exception:
                            continue
                    else:
                        # 如果所有分支都失败，使用默认分支
                        if url.startswith("./"):
                            url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{url[2:]}"
                        elif url.startswith("/"):
                            url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}{url}"
                        else:
                            url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{url}"

                file_info = self._download_file(url, alt)
                if file_info:
                    file_info.update(
                        {
                            "is_important": is_important,
                            "category": category,
                            "alt_text": alt,
                            "enhanced_description": enhanced_desc,
                            "context": self._extract_context(readme_content, url),
                            "file_type": "video"
                            if any(file_info["filename"].lower().endswith(ext) for ext in FileExtensions.VIDEO)
                            else "image",
                        }
                    )
                    media_files.append(file_info)

            # 只返回重要的媒体文件
            important_media = [media for media in media_files if media.get("is_important", False)]
            return important_media[:15]  # 最多下载15个重要文件

        except Exception as e:
            logger.warning(f"下载媒体失败: {e}")
            return []

    def _extract_context(self, readme_content: str, media_url: str) -> str:
        """提取媒体文件周围的上下文信息"""
        try:
            # 找到媒体文件在README中的位置
            lines = readme_content.split("\n")
            for i, line in enumerate(lines):
                if media_url in line:
                    # 提取前后各3行作为上下文
                    start = max(0, i - 3)
                    end = min(len(lines), i + 4)
                    context_lines = lines[start:end]

                    # 过滤空行并限制长度
                    context_lines = [line.strip() for line in context_lines if line.strip()]
                    context = "\n".join(context_lines)
                    return context[:300]  # 限制长度为300字符
            return ""
        except Exception:
            return ""

    def _extract_video_title(self, readme_content: str, video_url: str) -> str:
        """提取视频标题"""
        try:
            lines = readme_content.split("\n")
            for i, line in enumerate(lines):
                if video_url in line:
                    # 向上查找最近的标题
                    for j in range(i - 1, max(0, i - 10), -1):
                        prev_line = lines[j].strip()
                        if prev_line.startswith("<h3>") and prev_line.endswith("</h3>"):
                            return prev_line.replace("<h3>", "").replace("</h3>", "")
                        elif prev_line.startswith("###"):
                            return prev_line.replace("###", "").strip()
                    break
            return ""
        except Exception:
            return ""

    def _download_file(self, url: str, alt_text: str = "") -> Optional[dict]:
        """下载单个文件 - 改进版本"""
        try:
            # 尝试构建正确的GitHub raw URL
            processed_url = self._process_github_url(url)

            # 设置更好的请求头
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "image/webp,image/apng,image/*,video/*,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
            }

            response = requests.get(processed_url, headers=headers, timeout=15, allow_redirects=True)
            response.raise_for_status()

            # 改进的内容验证
            content = response.content
            content_type = response.headers.get("content-type", "").lower()

            logger.info(
                f"下载响应: URL={processed_url}, Status={response.status_code}, ContentType={content_type}, Size={len(content)}字节"
            )

            # 更精准的HTML检测
            if self._is_html_content(content, content_type):
                logger.warning(f"跳过HTML内容: {processed_url}")
                return None

            # 验证是否为有效媒体文件
            if not self._is_valid_media_file(content, content_type, processed_url):
                logger.warning(f"不是有效的媒体文件: {processed_url}")
                return None

            # 确定正确的文件扩展名
            file_ext = self._detect_file_extension(content, content_type, processed_url)
            filename = f"media_{len(list(self.output_dir.glob('*')))}{file_ext}"
            file_path = self.output_dir / filename

            # 保存文件
            with open(file_path, "wb") as f:
                f.write(content)

            logger.info(f"✅ 成功下载媒体文件: {filename} ({len(content)/1024:.1f}KB)")

            # 构造相对路径用于markdown引用
            relative_path = f"output/{self.project_name}/media/{filename}"

            return {
                "filename": filename,
                "path": str(file_path),
                "relative_path": relative_path,
                "url": processed_url,
                "original_url": url,
                "size": len(content),
                "content_type": content_type,
            }
        except Exception as e:
            logger.warning(f"下载文件失败 {url}: {e}")
            return None

    def _process_github_url(self, url: str) -> str:
        """处理GitHub URL，确保指向raw文件"""
        if not url.startswith(("http://", "https://")):
            return url  # 相对路径已在上级函数处理

        # 如果已经是raw.githubusercontent.com的URL，直接返回
        if "raw.githubusercontent.com" in url:
            return url

        # 如果是github.com的文件URL，转换为raw URL
        if "github.com" in url and "/blob/" in url:
            # 将 github.com/owner/repo/blob/branch/path 转换为 raw.githubusercontent.com/owner/repo/branch/path
            url = url.replace("github.com", "raw.githubusercontent.com").replace("/blob/", "/")
            logger.info(f"转换GitHub URL为raw格式: {url}")

        return url

    def _is_html_content(self, content: bytes, content_type: str) -> bool:
        """检测是否为HTML内容"""
        # 检查Content-Type
        if "text/html" in content_type:
            return True

        # 检查文件内容前512字节
        try:
            text_content = content[:512].decode("utf-8", errors="ignore").lower()
            html_indicators = ["<!doctype html", "<html", "<head>", "<body>", "</html>", "<title>", "text/html"]
            return any(indicator in text_content for indicator in html_indicators)
        except Exception:
            return False

    def _is_valid_media_file(self, content: bytes, content_type: str, url: str) -> bool:
        """验证是否为有效的媒体文件"""
        # 检查文件大小（HTML页面通常比较大）
        if len(content) < 100:
            return False

        # 检查文件扩展名
        url_lower = url.lower()
        valid_extensions = FileExtensions.IMAGE + FileExtensions.VIDEO
        if any(url_lower.endswith(ext) for ext in valid_extensions):
            return True

        # 检查Content-Type
        if content_type:
            valid_types = ["image/", "video/", "application/octet-stream"]
            if any(media_type in content_type for media_type in valid_types):
                return True

        # 检查文件头（魔数）
        return self._check_file_signature(content)

    def _check_file_signature(self, content: bytes) -> bool:
        """检查文件头魔数"""
        if len(content) < 8:
            return False

        # 常见图片和视频文件的魔数
        signatures = [
            b"\x89PNG\r\n\x1a\n",  # PNG
            b"\xff\xd8\xff",  # JPEG
            b"GIF87a",  # GIF87a
            b"GIF89a",  # GIF89a
            b"RIFF",  # WebP/AVI (需要进一步检查)
            b"\x00\x00\x00\x14ftypqt",  # QuickTime
            b"\x00\x00\x00\x18ftypmp4",  # MP4
            b"\x00\x00\x00 ftypmp42",  # MP4
        ]

        for sig in signatures:
            if content.startswith(sig):
                return True

        # WebP特殊检查
        if content.startswith(b"RIFF") and b"WEBP" in content[:20]:
            return True

        # SVG检查
        try:
            text_start = content[:100].decode("utf-8", errors="ignore").strip()
            if text_start.startswith("<?xml") or text_start.startswith("<svg"):
                return True
        except Exception:
            pass

        return False

    def _detect_file_extension(self, content: bytes, content_type: str, url: str) -> str:
        """检测正确的文件扩展名"""
        # 先从URL获取
        url_lower = url.lower()
        for ext in FileExtensions.IMAGE + FileExtensions.VIDEO:
            if url_lower.endswith(ext):
                return ext

        # 从文件头检测
        if content.startswith(b"\x89PNG\r\n\x1a\n"):
            return ".png"
        elif content.startswith(b"\xff\xd8\xff"):
            return ".jpg"
        elif content.startswith((b"GIF87a", b"GIF89a")):
            return ".gif"
        elif content.startswith(b"RIFF") and b"WEBP" in content[:20]:
            return ".webp"
        elif any(
            content.startswith(sig)
            for sig in [b"\x00\x00\x00\x14ftypqt", b"\x00\x00\x00\x18ftypmp4", b"\x00\x00\x00 ftypmp42"]
        ):
            return ".mp4"

        # SVG检查
        try:
            text_start = content[:100].decode("utf-8", errors="ignore").strip()
            if text_start.startswith("<?xml") or text_start.startswith("<svg"):
                return ".svg"
        except Exception:
            pass

        # 从Content-Type获取
        if "image/jpeg" in content_type or "image/jpg" in content_type:
            return ".jpg"
        elif "image/png" in content_type:
            return ".png"
        elif "image/gif" in content_type:
            return ".gif"
        elif "image/webp" in content_type:
            return ".webp"
        elif "image/svg" in content_type:
            return ".svg"
        elif "video/mp4" in content_type:
            return ".mp4"
        elif "video/quicktime" in content_type:
            return ".mov"

        # 默认扩展名
        return ".jpg"


class GitHubAnalyzer:
    """简化的GitHub分析器主类"""

    def __init__(self, config_path: str = "config/config.yaml"):
        self.config = Config(config_path)
        self.api = GitHubAPI()
        self.model = create_model()
        self.agents = self._create_agents()

    def _create_agents(self) -> dict:
        """创建AI代理"""
        return {
            "analyzer": AgentFactory.create_analyzer_agent(
                self.model,
                "GitHub项目分析师",
                "你负责分析GitHub项目并生成结构化的分析报告。专注于项目的核心功能、技术栈和使用价值。",
            ),
            "reviewer": AgentFactory.create_reviewer_agent(
                self.model, "内容审查员", "你负责审查和完善项目分析报告，确保内容准确、完整且易于理解。"
            ),
        }

    def analyze_repository(self, repo_url: str = None) -> str:
        """分析GitHub仓库 - 简化主流程"""
        if not repo_url:
            github_config = self.config.get_github_config()
            repo_url = github_config["url"]

        # 解析仓库信息
        owner, repo_name = CommonUtils.extract_github_url_parts(repo_url)
        output_dir = Path("output") / repo_name
        output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"开始分析仓库: {repo_url}")

        # 1. 获取基本信息
        repo_info = self.api.get_repo_info(owner, repo_name)
        logger.info(f"仓库信息: {repo_info}")

        # 2. 下载重要媒体
        downloader = MediaDownloader(str(output_dir), repo_name)
        media_files = downloader.download_from_readme(owner, repo_name)
        logger.debug(f"下载了 {len(media_files)} 个媒体文件")

        # 3. 获取README内容
        readme_content = self._get_readme_content(owner, repo_name)
        logger.debug(f"README内容: {readme_content[:100]}...")

        # 4. 代码分析（如果启用）
        code_analysis = ""
        github_config = self.config.get_github_config()
        if github_config.get("deep_analysis", True):
            code_analysis = self._analyze_code_structure(owner, repo_name)

        # 5. 生成分析报告
        analysis_data = {
            "repo_info": repo_info,
            "readme": readme_content,
            "media_files": media_files,
            "code_analysis": code_analysis,
            "purpose": github_config.get("purpose", "分析GitHub项目"),
        }

        report = self._generate_report(analysis_data)

        # 6. 保存结果
        output_file = output_dir / "project_analysis.md"
        CommonUtils.save_file(report, str(output_file))

        logger.info(f"分析完成，报告保存至: {output_file}")
        return str(output_file)

    def _get_readme_content(self, owner: str, repo: str) -> str:
        """获取README内容"""
        try:
            url = f"https://raw.githubusercontent.com/{owner}/{repo}/main/README.md"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                return response.text

            # 尝试master分支
            url = f"https://raw.githubusercontent.com/{owner}/{repo}/master/README.md"
            response = requests.get(url, timeout=10)
            return response.text if response.status_code == 200 else ""
        except Exception as e:
            logger.warning(f"获取README失败: {e}")
            return ""

    def _analyze_code_structure(self, owner: str, repo: str) -> str:
        """简化的代码结构分析"""
        # 这里可以添加代码结构分析逻辑，暂时返回空字符串
        return ""

    def _generate_report(self, data: dict) -> str:
        """生成分析报告"""
        # 构造媒体文件信息
        media_info = ""
        if data["media_files"]:
            media_info = "\n媒体文件信息：\n"
            important_media = [media for media in data["media_files"] if media.get("is_important", False)]
            for media in important_media:
                file_type = media.get("file_type", "image")
                enhanced_desc = media.get("enhanced_description", media.get("alt_text", "无描述"))
                media_info += f"- {media['filename']} (类型: {media['category']}, 文件类型: {file_type}, 描述: {enhanced_desc}, 路径: {media.get('relative_path', '')})\n"

        # 构造分析提示
        prompt = f"""
请分析以下GitHub项目并生成一份专业的Markdown分析报告。

项目信息：
- 名称：{data['repo_info'].get('name', 'Unknown')}
- 描述：{data['repo_info'].get('description', '无描述')}
- 语言：{data['repo_info'].get('language', 'Unknown')}
- Stars：{data['repo_info'].get('stars', 0)}
- 主题：{', '.join(data['repo_info'].get('topics', []))}

分析目标：{data['purpose']}

README内容：
{data['readme']}

{media_info}

请生成一个Markdown分析报告，包含：
1. 项目概览 - 简要介绍项目背景、目标、定位、Stars数量
2. 核心能力 - 详细说明项目的主要功能和特性
3. 技术亮点 - 分析项目的技术架构和创新点
4. 应用场景 - 列举项目的实际应用场景和价值

**重要要求**：
1. **重点展示多媒体素材**：如果README中包含demo演示、使用例子、架构图、流程图、截图等，请在报告相应章节中详细描述这些素材的内容和价值
2. **在报告中引用媒体文件**：对于重要的媒体文件，请在报告中使用markdown语法引用，格式为：![描述](路径)，例如：![架构图](output/项目名/media/media_0.png)
3. 多媒体素材只能使用本地路径，不要使用网络路径的内容，包括URL
4. 项目的核心价值、关键认知一定要在报告中体现完整，全面，突出项目的独特优势
5. Readme里核心能力介绍要完整，不要遗漏
6. 使用中文输出，保持报告专业、实用、易于理解

**输出格式要求**：
- 直接输出Markdown格式的报告正文，不要包含任何Agent对话、思考过程或交互信息
- 按Markdown的层级结构输出
"""
        logger.debug(f"分析提示: {prompt[:1000]}...")

        # 使用分析代理生成报告
        from camel.messages import BaseMessage

        analysis = (
            self.agents["analyzer"].step(BaseMessage.make_user_message(role_name="User", content=prompt)).msg.content
        )

        logger.debug(f"分析结果: {analysis[:1000]}...")

        return analysis


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="简化版GitHub项目分析工具")
    parser.add_argument("--repo", "-r", type=str, help="GitHub仓库URL")
    parser.add_argument("--config", "-c", type=str, default="config/config.yaml", help="配置文件路径")

    args = parser.parse_args()

    try:
        analyzer = GitHubAnalyzer(args.config)
        result = analyzer.analyze_repository(args.repo)
        print(f"\n✅ 分析完成！报告已保存至: {result}")
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
