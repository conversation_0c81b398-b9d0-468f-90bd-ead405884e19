#!/usr/bin/env python3
"""
意图分类Agent - 智能内容分析

主要功能：
1. 从config.yaml的intent_input读取url、file、chat三个字段
2. 智能意图识别和内容分类（通过一次LLM调用完成）
3. 内容安全检查（由LLM同时判断）
4. 返回分类结果和提取的信息，供后续流程直接调用对应功能

用法：
python feynman_workflow_refactored.py  # 自动从config读取intent_input
"""

import argparse
import json
import re
import sys
import time
from pathlib import Path
from typing import Dict, Optional, Any

import yaml
from loguru import logger

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.create_llm_model import create_model
from camel.messages import BaseMessage
from camel.agents import ChatAgent


class SmartIntentClassifier:
    """智能意图分类器 - 单次LLM调用完成分类+安全审查"""
    
    def __init__(self, chat_agent: Optional[ChatAgent] = None):
        self.chat_agent = chat_agent

    def classify_content(self, url: str = None, file: str = None, chat: str = None) -> Dict[str, Any]:
        """优先使用一次LLM调用得到包括安全检查在内的完整结果"""
        try:
            if self.chat_agent is None:
                raise RuntimeError("ChatAgent未初始化")
            return self._classify_with_llm(url, file, chat)
        except Exception as e:
            logger.warning(f"LLM分类失败，返回最小默认结果: {e}")
            return {
            'type': 'chat',
            'category': 'chat',
            'subcategory': 'general',
                'extracted_info': {
                    'topic': (chat[:20] if chat else 'chat_content')
                },
                'confidence': 0.3,
                'safety': {'is_safe': True, 'reason': 'llm-fallback'}
            }

    def _classify_with_llm(self, url: str = None, file: str = None, chat: str = None) -> Dict[str, Any]:
        """通过一次LLM调用完成分类与安全审查，严格输出JSON"""
        input_payload = {
            'url': url or None,
            'file': file or None,
            'chat': chat or None,
        }
        # 输出schema提示
        schema_example = {
            "type": "github",  # one of [github, paper, url_file, webpage, local_file, math_problem, chat, concept_explain]
            "category": "github",
            "subcategory": "repository",
            "extracted_info": {
                "topic": "",
                "url": "",
                "file_path": "",
                "image_type": "",
                "content_type": "",
                "paper_type": "",
                "arxiv_id": "",
                "purpose": ""
            },
            "confidence": 0.0,
            "safety": {
                "is_safe": True,
                "reason": ""
            }
        }

        instructions = (
            "你是专业的'意图分类与安全审查'专家。请仅依据提供的输入(url, file, chat)进行判断，不进行网络访问。"
            "请严格输出UTF-8 JSON对象且不包含任何多余文本或解释。"
            "分类规则提示："
            "1) 如果url包含github.com，则type=github, category=github, subcategory=repository。"
            "2) 如果url包含arxiv.org，则type=paper, category=paper，subcategory需在[paper_survey, paper_research, paper_technical_report, document]中选择，尽量从chat中推断paper_type。"
            "3) 如果url结尾为文件扩展名[.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx]（不区分大小写），则type=url_file, category=[扩展名类型], subcategory为general。"
            "4) 如果url存在但不符合上述规则，则type=webpage, category=webpage, subcategory=general。"
            "5) 如果file是图片扩展名[png,jpg,jpeg,gif,bmp,tiff,svg,webp]，且chat包含题目/习题/数学/几何/代数等，则type=math_problem, category=image, subcategory=math_problem；否则type=local_file, category=image, subcategory可为[screenshot, diagram, document, general]之一。"
            "6) 如果file是文件扩展名[pdf,doc,docx,txt,md,html,xml,json,csv,xls,xlsx,ppt,pptx]，则type=local_file, category=[扩展名类型], subcategory从chat中推断file内容的类型，如果chat为空，则为general。"
            "7) 如果仅有chat：若意在解释概念(如包含'什么是/介绍/解释/概念/原理/科普/theory/what is/explain')则type=concept_explain, category=chat, subcategory=concept_introduction；否则type=chat, category=chat, subcategory=general。"
            "提取extracted_info：- topic: 从chat中尽量提取主题短语；- url/file_path：原样返回；- purpose：针对不同type生成一句用途描述，面向一般技术受众，精炼自然。"
            "安全(safety)：判断chat是否包含明显违法/暴力/色情/政治极端等风险，若有则is_safe=false并简述reason。"
            "置信度confidence：0.0~1.0。"
            "必须输出与如下schema结构一致的JSON，仅一个对象："
        )

        user_prompt = (
            f"指令输入(JSON)：\n{json.dumps(input_payload, ensure_ascii=False)}\n\n"
            f"输出schema示例(仅供参考，不要照抄值)：\n{json.dumps(schema_example, ensure_ascii=False)}"
        )

        response = self.chat_agent.step(instructions + user_prompt)
        content = response.msg.content if hasattr(response, 'msg') else str(response)
        parsed = self._safe_parse_json(content)
        if not isinstance(parsed, dict):
            raise ValueError("LLM未返回有效JSON对象")

        # 字段兜底
        parsed.setdefault('type', 'chat')
        parsed.setdefault('category', 'chat')
        parsed.setdefault('subcategory', 'general')
        parsed.setdefault('extracted_info', {})
        parsed.setdefault('confidence', 0.7)
        parsed.setdefault('safety', {"is_safe": True, "reason": ""})

        # 归一化extracted_info中的url/file_path
        ei = parsed.get('extracted_info', {}) or {}
        if url and 'url' not in ei:
            ei['url'] = url
        if file and 'file_path' not in ei:
            ei['file_path'] = str(Path(file).resolve()) if Path(file or '').exists() else (file or '')
        parsed['extracted_info'] = ei

        return parsed

    def _safe_parse_json(self, text: str) -> Any:
        """从任意文本中尽力提取单个JSON对象"""
        text = text.strip()
        if text.startswith("```"):
            text = re.sub(r'^```(?:json|JSON|\w+)?\n', '', text)
            text = re.sub(r'\n```\s*$', '', text)
        start = text.find('{')
        end = text.rfind('}')
        if start != -1 and end != -1 and end > start:
            candidate = text[start:end + 1]
            try:
                return json.loads(candidate)
            except Exception:
                pass
        try:
            return json.loads(text)
        except Exception:
            return None


class SmartIntentClassificationAgent:
    """智能意图分类代理主类（仅LLM路径）"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        # 加载意图分类专用模型配置
        self.model = self._create_intent_model()
        system_message = BaseMessage.make_assistant_message(
            role_name="意图分类与安全审查专家",
            content=(
                "你是一名可靠的意图分类与安全审查专家。\n"
                "- 仅根据输入(url, file, chat)进行推断，不要访问网络。\n"
                "- 返回严格的JSON且不包含多余文本。\n"
                "- 尽量根据常见模式推断类型，并给出简洁的purpose。\n"
            ),
        )
        self.chat_agent = ChatAgent(system_message=system_message, model=self.model, output_language="chinese")
        self.classifier = SmartIntentClassifier(chat_agent=self.chat_agent)

    def _create_intent_model(self):
        """创建意图分类专用模型"""
        config = self._load_config()
        intent_model_config = config.get('intent_classification_model', {})
        model_config = config.get('model', {})
        api_config = model_config.get('api', {})
        use_cheap_model = api_config.get('use_cheap_model', True)
        
        # 获取模型类型，默认为google/gemini-2.5-flash-lite-preview-06-17
        model_type = intent_model_config.get('type', 'google/gemini-2.5-flash-lite-preview-06-17')
        if use_cheap_model:
            model_type = 'gemini-2.5-flash-lite-preview-06-17'
        
        logger.info(f"意图分类模型配置: {model_type}")
        
        # 直接调用create_model函数，传入模型类型
        return create_model(
            config_file=self.config_path,
            model_type=model_type
        )

    def _load_config(self) -> Dict[str, Any]:
        """加载原始配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}

    def process_intent_from_config(self) -> Dict[str, Any]:
        """
        从配置文件的material.intent_input读取输入并处理意图分类
        
        Returns:
            处理结果字典
        """
        config = self._load_config()
        material_config = config.get('material', {})
        intent_input = material_config.get('intent_input', {})
        url = intent_input.get('url', '').strip()
        file = intent_input.get('file', '').strip()
        chat = intent_input.get('chat', '').strip()
        
        # 将空字符串转换为None
        url = url if url else None
        file = file if file else None
        chat = chat if chat else None
        
        return self.process_intent(url, file, chat)

    def process_intent(self, url: str = None, file: str = None, chat: str = None) -> Dict[str, Any]:
        """
        处理意图分类的主流程（依赖单次LLM调用）
        """
        try:
            if not any([url, file, chat]):
                return {'error': '必须提供url、file或chat中的至少一个参数'}
            
            # 通过LLM完成安全检查与智能分类
            classification = self.classifier.classify_content(url, file, chat)
            
            # 根据LLM安全结果决定是否继续
            safety = classification.get('safety', {}) if isinstance(classification, dict) else {}
            if isinstance(safety, dict) and not safety.get('is_safe', True):
                reason = safety.get('reason', '未通过安全检查')
                return {'error': f'聊天指令安全检查失败: {reason}'}

            # URL可访问性检查
            if url and not self._check_url_accessibility(url):
                return {'error': f'URL无法访问或下载失败: {url}'}
            
            # 文件存在性检查
            if file and not Path(file).exists():
                return {'error': f'文件不存在: {file}'}
            
            return {
                'success': True,
                'classification': classification,
                'safety_check': '通过',
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.exception(f"意图处理失败: {e}")
            return {'error': f'处理过程中发生错误: {str(e)}'}

    def _check_url_accessibility(self, url: str) -> bool:
        """检查URL可访问性 - 增强版本，支持多种检查方式和代理"""
        try:
            import requests
            import os
            
            # 构建请求会话，支持代理设置
            session = requests.Session()
            
            # 检查环境变量中的代理设置
            proxies = {}
            if os.environ.get('http_proxy'):
                proxies['http'] = os.environ.get('http_proxy')
            if os.environ.get('https_proxy'):
                proxies['https'] = os.environ.get('https_proxy')
            if os.environ.get('HTTP_PROXY'):
                proxies['http'] = os.environ.get('HTTP_PROXY')
            if os.environ.get('HTTPS_PROXY'):
                proxies['https'] = os.environ.get('HTTPS_PROXY')
            
            # 如果没有明确的代理设置，尝试常见的代理端口
            if not proxies:
                # 检查常见代理端口（简单连接测试，不依赖外部服务）
                for proxy_port in ['7890', '1080', '8080']:
                    try:
                        import socket
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        result = sock.connect_ex(('127.0.0.1', int(proxy_port)))
                        sock.close()
                        if result == 0:  # 端口可用
                            proxy_url = f'http://127.0.0.1:{proxy_port}'
                            proxies = {'http': proxy_url, 'https': proxy_url}
                            logger.debug(f"检测到可用代理端口: {proxy_port}")
                            break
                    except:
                        continue
            
            if proxies:
                session.proxies.update(proxies)
                logger.debug(f"使用代理设置: {proxies}")
            
            # 设置通用headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            # 方法1: 先尝试HEAD请求（快速但可能被某些网站拒绝）
            try:
                response = session.head(url, timeout=15, allow_redirects=True, headers=headers)
                if response.status_code < 400:
                    logger.debug(f"URL通过HEAD请求检查: {url} (状态码: {response.status_code})")
                    return True
                else:
                    logger.debug(f"HEAD请求返回状态码: {response.status_code}")
            except Exception as e:
                logger.debug(f"HEAD请求失败，尝试GET请求: {e}")
            
            # 方法2: 如果HEAD失败，尝试GET请求前1024字节（作为fallback）
            try:
                response = session.get(url, timeout=20, allow_redirects=True, stream=True, headers=headers)
                if response.status_code < 400:
                    # 读取前1024字节验证内容存在
                    content = next(response.iter_content(1024), b'')
                    if content:
                        logger.debug(f"URL通过GET请求检查: {url} (状态码: {response.status_code}, 内容大小: {len(content)}字节)")
                        return True
                else:
                    logger.debug(f"GET请求返回状态码: {response.status_code}")
            except Exception as e:
                logger.debug(f"GET请求也失败: {e}")
            
            return False
            
        except Exception as e:
            logger.warning(f"URL可访问性检查完全失败: {e}")
            # 如果所有方法都失败，但URL格式看起来正常，给予有限信任
            if url.startswith(('http://', 'https://')) and '.' in url and len(url) > 10:
                logger.info(f"URL格式正常，允许继续处理: {url}")
                return True
            return False


def main():
    """主函数 - 简化的意图分类测试"""
    
    # 核心测试用例 - 覆盖主要场景
    test_cases = [
        # 数学题目
        {
            "name": "数学题目",
            "file": "./output/初中几何题_compressed_3.png",
            "chat": "讲解这道几何题的解题思路",
            "expected": "math_problem/image/math_problem"
        },
        
        # GitHub项目
        {
            "name": "GitHub项目",
            "url": "https://github.com/openai/whisper",
            "chat": "介绍这个AI项目的核心功能",
            "expected": "github/github/repository"
        },
        
        # ArXiv论文
        {
            "name": "ArXiv论文",
            "url": "https://arxiv.org/pdf/2507.16632",
            "chat": "深度分析这篇研究论文",
            "expected": "paper/paper/paper_research",
            "skip_url_check": True
        },
        
        # 在线文件
        {
            "name": "在线PDF",
            "url": "https://example.com/document.pdf",
            "chat": "分析这个PDF文档",
            "expected": "url_file/pdf/general",
            "skip_url_check": True
        },
        
        # 通用网页
        {
            "name": "通用网页",
            "url": "https://www.bbc.com/news/technology-12345678",
            "chat": "分析这篇科技新闻",
            "expected": "webpage/webpage/general",
            "skip_url_check": True
        },
        
        # 普通图片
        {
            "name": "普通图片",
            "file": "./output/初中几何题_compressed_3.png",
            "chat": "分析这个界面截图",
            "expected": "local_file/image/screenshot"
        },
        
        # 概念解释
        {
            "name": "概念解释",
            "chat": "解释深度学习的基本原理",
            "expected": "concept_explain/chat/concept_introduction"
        },
        
        # 普通聊天
        {
            "name": "普通聊天",
            "chat": "分析这个问题的解决方案",
            "expected": "chat/chat/general"
        }
    ]
    
    print("🧪 意图分类测试")
    print("=" * 50)
    
    agent = SmartIntentClassificationAgent()
    passed = 0
    total = len(test_cases)
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. {test['name']}")
        
        try:
            result = agent.process_intent(
                url=test.get('url'),
                file=test.get('file'),
                chat=test.get('chat')
            )
            
            # 处理跳过URL检查的情况
            if 'error' in result and test.get('skip_url_check', False):
                classification = agent.classifier.classify_content(
                    url=test.get('url'),
                    file=test.get('file'),
                    chat=test.get('chat')
                )
                result = {'success': True, 'classification': classification}
            
            if 'error' in result:
                print(f"   ❌ {result['error']}")
                continue
            
            # 验证结果
            c = result['classification']
            actual = f"{c['type']}/{c['category']}/{c['subcategory']}"
            expected = test['expected']
            
            if actual == expected:
                print(f"   ✅ {actual}")
                passed += 1
            else:
                print(f"   ❌ 期望:{expected} 实际:{actual}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    # 结果统计
    print(f"\n{'='*50}")
    print(f"结果: {passed}/{total} 通过 ({passed/total*100:.0f}%)")
    
    return passed == total


if __name__ == "__main__":
    main() 