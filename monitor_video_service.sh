#!/bin/bash

# 视频生成服务监控和自动重启脚本

# 配置参数
SERVICE_NAME="Feynman视频生成服务"
SERVICE_URL="http://178.157.51.21:8000/health"
CHECK_INTERVAL=30  # 检查间隔（秒）
MAX_RESTART_ATTEMPTS=5  # 最大重启尝试次数
RESTART_COOLDOWN=60  # 重启冷却时间（秒）
LOG_FILE="video_service_monitor.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# 检查服务状态
check_service() {
    local response
    local status_code
    
    # 首先检查进程是否存在
    local pids=$(pgrep -f "video_generation_service.py")
    if [ -z "$pids" ]; then
        return 1  # 进程不存在
    fi
    
    # 尝试连接服务
    response=$(curl -s -w "%{http_code}" -o /dev/null "$SERVICE_URL" 2>/dev/null)
    status_code=$?
    
    if [ $status_code -eq 0 ] && [ "$response" = "200" ]; then
        return 0  # 服务正常
    else
        return 1  # 服务异常
    fi
}

# 启动服务
start_service() {
    log "INFO" "🔄 正在启动 $SERVICE_NAME..."
    
    # 检查启动脚本是否存在
    if [ ! -f "start_video_service.sh" ]; then
        log "ERROR" "❌ 启动脚本 start_video_service.sh 不存在"
        return 1
    fi
    
    # 给启动脚本执行权限
    chmod +x start_video_service.sh
    
    # 在后台启动服务
    nohup ./start_video_service.sh > video_service.log 2>&1 &
    local pid=$!
    
    # 等待几秒让服务启动
    sleep 5
    
    # 检查进程是否还在运行
    if kill -0 $pid 2>/dev/null; then
        log "SUCCESS" "✅ $SERVICE_NAME 启动成功 (PID: $pid)"
        return 0
    else
        log "ERROR" "❌ $SERVICE_NAME 启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    log "INFO" "🛑 正在停止 $SERVICE_NAME..."
    
    # 查找服务进程
    local pids=$(pgrep -f "video_generation_service.py")
    
    if [ -n "$pids" ]; then
        for pid in $pids; do
            log "INFO" "终止进程 PID: $pid"
            kill -TERM $pid 2>/dev/null
            sleep 2
            
            # 如果进程还在运行，强制终止
            if kill -0 $pid 2>/dev/null; then
                log "WARN" "强制终止进程 PID: $pid"
                kill -KILL $pid 2>/dev/null
            fi
        done
        log "SUCCESS" "✅ $SERVICE_NAME 已停止"
    else
        log "INFO" "ℹ️  没有找到运行中的 $SERVICE_NAME 进程"
    fi
}

# 重启服务
restart_service() {
    log "INFO" "🔄 正在重启 $SERVICE_NAME..."
    stop_service
    sleep 3
    start_service
}

# 主监控循环
main() {
    local restart_count=0
    local last_restart_time=0
    
    log "INFO" "🚀 开始监控 $SERVICE_NAME"
    log "INFO" "📡 服务地址: $SERVICE_URL"
    log "INFO" "⏱️  检查间隔: ${CHECK_INTERVAL}秒"
    log "INFO" "📝 日志文件: $LOG_FILE"
    echo ""
    
    # 确保服务正在运行
    if ! check_service; then
        log "WARN" "⚠️  服务未运行，正在启动..."
        if start_service; then
            log "SUCCESS" "✅ 初始启动成功"
        else
            log "ERROR" "❌ 初始启动失败"
            exit 1
        fi
    else
        log "SUCCESS" "✅ 服务已在运行"
    fi
    
    # 主循环
    while true; do
        if check_service; then
            echo -e "${GREEN}✅${NC} $SERVICE_NAME 运行正常 $(date '+%H:%M:%S')"
            restart_count=0  # 重置重启计数
        else
            echo -e "${RED}❌${NC} $SERVICE_NAME 服务异常 $(date '+%H:%M:%S')"
            
            # 检查重启限制
            local current_time=$(date +%s)
            if [ $restart_count -ge $MAX_RESTART_ATTEMPTS ]; then
                if [ $((current_time - last_restart_time)) -lt $RESTART_COOLDOWN ]; then
                    log "WARN" "⚠️  达到最大重启次数限制，等待冷却时间..."
                    sleep $RESTART_COOLDOWN
                    restart_count=0
                fi
            fi
            
            # 尝试重启
            log "WARN" "⚠️  尝试重启服务 (第 $((restart_count + 1)) 次)"
            if restart_service; then
                log "SUCCESS" "✅ 重启成功"
                restart_count=0
            else
                log "ERROR" "❌ 重启失败"
                restart_count=$((restart_count + 1))
                last_restart_time=$current_time
                
                if [ $restart_count -ge $MAX_RESTART_ATTEMPTS ]; then
                    log "ERROR" "❌ 达到最大重启尝试次数 ($MAX_RESTART_ATTEMPTS)，停止监控"
                    exit 1
                fi
            fi
        fi
        
        sleep $CHECK_INTERVAL
    done
}

# 信号处理
cleanup() {
    log "INFO" "🛑 收到停止信号，正在清理..."
    stop_service
    log "INFO" "👋 监控脚本已停止"
    exit 0
}

# 注册信号处理器
trap cleanup SIGINT SIGTERM

# 显示帮助信息
show_help() {
    echo "Feynman视频生成服务监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -s, --start    启动服务"
    echo "  -t, --stop     停止服务"
    echo "  -r, --restart  重启服务"
    echo "  -c, --check    检查服务状态"
    echo "  -m, --monitor  启动监控模式（默认）"
    echo ""
    echo "示例:"
    echo "  $0              # 启动监控模式"
    echo "  $0 --start      # 仅启动服务"
    echo "  $0 --stop       # 仅停止服务"
    echo "  $0 --check      # 检查服务状态"
}

# 解析命令行参数
case "${1:-monitor}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -s|--start)
        start_service
        exit $?
        ;;
    -t|--stop)
        stop_service
        exit 0
        ;;
    -r|--restart)
        restart_service
        exit $?
        ;;
    -c|--check)
        if check_service; then
            echo -e "${GREEN}✅ 服务运行正常${NC}"
            exit 0
        else
            echo -e "${RED}❌ 服务异常${NC}"
            exit 1
        fi
        ;;
    -m|--monitor)
        main
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
