#!/usr/bin/env python3
"""
统一的Feynman工作流 - 智能意图识别 + 完整视频处理流程

整合了意图识别、原始材料抽取、并行录屏、故事板生成、视频渲染、字幕拼接、
视频拼接、背景音乐等完整功能。

支持的内容类型：
- GitHub项目 (github) - 支持页面录屏
- PDF文档/论文 (pdf) - 支持ArXiv录屏
- 网页内容 (webpage) - 支持页面录屏
- 本地文件 (local_file) - 图片AI分析、PDF解析等
- 数学题目 (math_problem) - 专用图片分析
- 概念解释 (concept_explain) - 使用example_explain流程
- 普通聊天 (chat) - 直接生成素材

用法：
python unified_feynman_workflow.py [--config config.yaml] [--verbose]
"""

import argparse
import json
import os
import re
import sys
import time
import urllib.request
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

import yaml
from loguru import logger

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

from utils.create_llm_model import create_model
from utils.audio_utils import AudioUtils


class UnifiedFeynmanWorkflow:
    """统一的Feynman工作流控制器"""

    def __init__(self, config_path: str = "config/config.yaml", output_dir: str = None):
        self.config_path = config_path
        self.config = self._load_config()
        self.setup_logging()
        self.project_name_ = ""
        self.newtag = output_dir

    def _load_config(self):
        """加载配置文件"""
        logger.info(f"正在加载配置文件: {self.config_path}")
        try:
            with open(self.config_path, encoding="utf-8") as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            sys.exit(1)

    def setup_logging(self, verbose=False):
        """设置日志"""
        if verbose:
            logger.remove()
            logger.add(sys.stderr, level="DEBUG")

    def _download_file_from_url(self, url, output_dir, filename=None):
        """通用文件下载函数"""
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 如果没有提供文件名，从URL中提取
            if not filename:
                parsed_url = urllib.parse.urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename or '.' not in filename:
                    # 根据Content-Type确定扩展名
                    import requests
                    try:
                        response = requests.head(url, timeout=10)
                        content_type = response.headers.get('content-type', '').lower()
                        if 'pdf' in content_type:
                            ext = '.pdf'
                        elif 'word' in content_type or 'docx' in content_type:
                            ext = '.docx'
                        elif 'excel' in content_type or 'xlsx' in content_type:
                            ext = '.xlsx'
                        elif 'powerpoint' in content_type or 'pptx' in content_type:
                            ext = '.pptx'
                        else:
                            ext = '.bin'  # 默认扩展名

                        # 从URL路径或参数中提取可能的文件名
                        if parsed_url.path:
                            base_name = os.path.splitext(os.path.basename(parsed_url.path))[0]
                        else:
                            base_name = "downloaded_file"

                        filename = f"{base_name}{ext}"
                    except:
                        filename = "downloaded_file.bin"

            # 生成本地文件路径
            local_file_path = os.path.join(output_dir, filename)

            # 如果文件已存在，返回现有文件路径
            if os.path.exists(local_file_path):
                logger.info(f"文件已存在: {local_file_path}")
                return local_file_path

            # 下载文件
            logger.info(f"正在下载文件: {url} -> {local_file_path}")

            # 使用requests下载以支持更好的错误处理
            import requests
            response = requests.get(url, timeout=60, stream=True)
            response.raise_for_status()

            with open(local_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            logger.success(f"✅ 文件下载完成: {local_file_path}")
            return local_file_path

        except Exception as e:
            logger.error(f"文件下载失败: {url}, 错误: {e}")
            return None

    def run_intent_classification(self):
        """运行智能意图分类"""
        try:
            from agents.intent_classification_agent import SmartIntentClassificationAgent

            logger.info("🎯 开始智能意图分类...")
            agent = SmartIntentClassificationAgent(self.config_path)
            result = agent.process_intent_from_config()

            if "error" in result:
                logger.error(f"❌ 意图分类失败: {result['error']}")
                return False, None

            classification = result["classification"]
            logger.info("✅ 智能意图分类完成！")
            logger.info(f"📋 处理类型: {classification['type']}")
            logger.info(f"📋 主分类: {classification['category']}")
            logger.info(f"🏷️  子分类: {classification['subcategory']}")

            return True, classification

        except Exception as e:
            logger.exception(f"意图分类过程中发生错误: {e}")
            return False, None

    def generate_project_name(self, classification):
        """根据分类结果生成项目名称"""
        info = classification.get("extracted_info", {})
        category = classification.get("category", "")
        subcategory = classification.get("subcategory", "")

        if category == "github":
            # 优先尝试从repo字段获取
            repo_name = info.get("repo")
            if repo_name:
                return repo_name

            # 如果repo字段不存在，从URL中提取
            github_url = info.get("url", "")
            if github_url:
                import re
                # 从GitHub URL提取项目名
                match = re.search(r"github\.com/([\w\-\.]+)/([\w\-\.]+)", github_url)
                if match:
                    repo_name = match.group(2)
                    # 移除.git后缀
                    if repo_name.endswith(".git"):
                        repo_name = repo_name[:-4]
                    return repo_name

            # 如果都失败了，返回默认值
            return "github_project"
        elif category == "paper":
            arxiv_id = info.get("arxiv_id", "")
            if arxiv_id:
                return arxiv_id
            return f"paper_{subcategory}"
        elif category == "pdf":
            # 处理本地PDF文件或在线PDF文件
            file_path = info.get("file_path", "")
            if file_path:
                # 本地PDF文件，使用文件名（不含后缀）
                from pathlib import Path
                file_name = Path(file_path).stem
                if file_name:
                    import re
                    clean_name = re.sub(r"[^\w\u4e00-\u9fff]", "_", file_name)
                    return clean_name

            # 在线PDF文件，从URL提取
            pdf_url = info.get("url", "")
            if pdf_url:
                if "arxiv.org" in pdf_url:
                    # ArXiv论文
                    import re
                    match = re.search(r"(\d{4}\.\d{4,5})", pdf_url)
                    if match:
                        return match.group(1)
                else:
                    # 其他PDF URL
                    from pathlib import Path
                    from urllib.parse import urlparse
                    parsed_url = urlparse(pdf_url)
                    filename = Path(parsed_url.path).stem
                    if filename:
                        import re
                        clean_name = re.sub(r"[^\w\u4e00-\u9fff]", "_", filename)
                        return clean_name

            # 回退到默认名称
            return f"pdf_{subcategory}"
        elif category == "image" or category == "local_file":
            file_path = info.get("file_path", "")
            if file_path:
                import re
                from pathlib import Path

                file_name = Path(file_path).stem
                if file_name:
                    clean_name = re.sub(r"[^\w\u4e00-\u9fff]", "_", file_name)
                    return clean_name
            if subcategory == "math_problem":
                topic = info.get("topic", "数学题目")
                return f"math_{topic}"
            else:
                topic = info.get("topic", "local_file")
                return topic
        elif category in ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"]:
            # 处理在线文件URL，从URL中提取文件名
            file_url = info.get("url", "")
            if file_url:
                from pathlib import Path
                from urllib.parse import urlparse
                parsed_url = urlparse(file_url)
                filename = Path(parsed_url.path).stem
                if filename:
                    import re
                    clean_name = re.sub(r"[^\w\u4e00-\u9fff]", "_", filename)
                    return clean_name

            # 如果从URL提取失败，使用默认名称
            return f"{category}_{subcategory}"
        elif category == "chat":
            if subcategory == "concept_introduction":
                topic = info.get("topic", "概念解释")
                return f"concept_{topic}"
            else:
                topic = info.get("topic", "chat_content")
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                return f"chat_{topic}_{timestamp}"
        else:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            return f"{category}_{subcategory}_{timestamp}"

    def ensure_output_directory(self, project_name):
        """确保输出目录存在"""
        if self.newtag is not None:
            output_dir = f"output/{self.newtag}/{project_name}"
        else:
            output_dir = f"output/{project_name}"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    def run(self, verbose=False):
        """运行完整工作流"""
        self.setup_logging(verbose)

        try:
            # 检查配置
            material_config = self.config.get("material", {})
            intent_input = material_config.get("intent_input", {})
            has_intent_input = any(
                [
                    intent_input.get("url", "").strip(),
                    intent_input.get("file", "").strip(),
                    intent_input.get("chat", "").strip(),
                ]
            )

            if not has_intent_input:
                logger.error("配置文件中未找到有效的intent_input配置")
                return False

            # 步骤1: 智能意图分类
            success, classification = self.run_intent_classification()
            if not success:
                logger.error("智能意图分类失败，终止工作流")
                return False

            # 步骤2: 生成项目名称和目录
            project_name = self.generate_project_name(classification)
            output_dir = self.ensure_output_directory(project_name)
            logger.info(f"📂 项目名称: {project_name}")
            logger.info(f"📁 项目目录: {output_dir}")
            self.project_name_ = output_dir

            # 步骤3: 内容处理
            content_processor = ContentProcessor(self.config)
            content_result = content_processor.process(classification, project_name, output_dir)
            if not content_result["success"]:
                logger.error("内容处理失败")
                return False

            # 检查是否是concept_explain类型，如果是则直接完成
            if content_result.get("concept_explain_completed", False):
                logger.success("🎉 概念解释处理完成！")
                logger.info(f"📁 项目目录: {output_dir}")
                logger.info(f"📄 解释文件: {content_result['analysis_file']}")
                logger.info(f"💻 代码目录: {output_dir}/code/{classification['extracted_info'].get('topic', '概念解释')}")

                # 显示视频TTS处理结果
                video_result = content_result.get("video_with_tts")
                if video_result:
                    logger.info("🎬 视频TTS处理结果:")
                    logger.info(f"   📹 原始视频: {video_result.get('original_video', 'N/A')}")
                    if video_result.get('final_video_with_audio'):
                        logger.info(f"   🎵 带音频视频: {video_result['final_video_with_audio']}")
                    else:
                        logger.info(f"   🎬 处理后视频: {video_result.get('processed_video', 'N/A')}")
                    logger.info(f"   📝 使用的讲解文案: {video_result.get('md_file', 'N/A')}")
                    logger.info(f"   ⏱️ 时间戳文件: {video_result.get('log_file', 'N/A')}")
                else:
                    logger.info("🎬 视频TTS处理: 未启用或失败")

                return True

            # 步骤4: 故事板生成（除了concept_explain，其他类型都需要）
            storyboard_generator = StoryboardGenerator(self.config)
            storyboard_file = storyboard_generator.generate(
                content_result["analysis_file"],
                classification["extracted_info"].get("purpose", "生成动画视频"),
                content_result.get("media_files", []),
                output_dir,
                project_name,
            )
            if not storyboard_file:
                logger.error("故事板生成失败")
                return False

            # 步骤5: 视频处理（除了concept_explain，其他类型都需要）
            video_processor = VideoProcessor(self.config, config_path=self.config_path)
            final_video = video_processor.render(storyboard_file, output_dir, project_name)
            if not final_video:
                logger.error("视频处理失败")
                return False

            logger.success("🎉 工作流执行成功！")
            logger.info(f"📁 项目目录: {output_dir}")
            logger.info(f"🎬 最终视频: {final_video}")
            return True

        except KeyboardInterrupt:
            logger.warning("⚠️  用户中断操作")
            return False
        except Exception as e:
            logger.exception(f"工作流执行过程中发生错误: {e}")
            return False


class ContentProcessor:
    """内容处理模块"""

    def __init__(self, config):
        self.config = config

    def _download_file_from_url(self, url, output_dir, filename=None):
        """通用文件下载函数"""
        try:
            import os
            import urllib.parse
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 如果没有提供文件名，从URL中提取
            if not filename:
                parsed_url = urllib.parse.urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename or '.' not in filename:
                    # 根据Content-Type确定扩展名
                    import requests
                    try:
                        response = requests.head(url, timeout=10)
                        content_type = response.headers.get('content-type', '').lower()
                        if 'pdf' in content_type:
                            ext = '.pdf'
                        elif 'word' in content_type or 'docx' in content_type:
                            ext = '.docx'
                        elif 'excel' in content_type or 'xlsx' in content_type:
                            ext = '.xlsx'
                        elif 'powerpoint' in content_type or 'pptx' in content_type:
                            ext = '.pptx'
                        else:
                            ext = '.bin'  # 默认扩展名

                        # 从URL路径或参数中提取可能的文件名
                        if parsed_url.path:
                            base_name = os.path.splitext(os.path.basename(parsed_url.path))[0]
                        else:
                            base_name = "downloaded_file"

                        filename = f"{base_name}{ext}"
                    except:
                        filename = "downloaded_file.bin"

            # 生成本地文件路径
            local_file_path = os.path.join(output_dir, filename)

            # 如果文件已存在，返回现有文件路径
            if os.path.exists(local_file_path):
                logger.info(f"文件已存在: {local_file_path}")
                return local_file_path

            # 下载文件
            logger.info(f"正在下载文件: {url} -> {local_file_path}")

            # 使用requests下载以支持更好的错误处理
            import requests
            response = requests.get(url, timeout=60, stream=True)
            response.raise_for_status()

            with open(local_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            logger.success(f"✅ 文件下载完成: {local_file_path}")
            return local_file_path

        except Exception as e:
            logger.error(f"文件下载失败: {url}, 错误: {e}")
            return None

    def process(self, classification, project_name, output_dir):
        """处理不同类型的内容"""
        classification_type = classification["type"]

        # 对于chat模式的概念解释，先删除已存在的工程文件夹
        if classification_type in ["concept_explain", "chat"]:
            import shutil

            if os.path.exists(output_dir):
                logger.info(f"🗑️  删除已存在的项目文件夹: {output_dir}")
                try:
                    shutil.rmtree(output_dir)
                    os.makedirs(output_dir, exist_ok=True)
                    logger.info("✅ 项目文件夹重新创建成功")
                except Exception as e:
                    logger.warning(f"⚠️  删除项目文件夹失败: {e}")

        logger.info(f"🎯 处理内容类型: {classification_type}")

        # 根据类型处理内容
        if classification_type == "github":
            return self._process_github(classification, project_name, output_dir)
        elif classification_type == "paper":
            return self._process_paper(classification, project_name, output_dir)
        elif classification_type == "webpage":
            return self._process_webpage(classification, project_name, output_dir)
        elif classification_type == "url_file":
            return self._process_url_file(classification, project_name, output_dir)
        elif classification_type == "local_file":
            return self._process_local_file(classification, project_name, output_dir)
        elif classification_type == "math_problem":
            return self._process_math_problem(classification, project_name, output_dir)
        elif classification_type == "concept_explain":
            return self._process_concept_explain(classification, project_name, output_dir)
        elif classification_type == "chat":
            return self._process_chat(classification, project_name, output_dir)
        else:
            logger.error(f"不支持的处理类型: {classification_type}")
            return {"success": False}

    def _process_github(self, classification, project_name, output_dir):
        """处理GitHub内容，包括并行录屏"""
        info = classification["extracted_info"]
        github_url = info["url"]
        # project_name 用于生成输出文件名

        analysis_file = f"{output_dir}/project_analysis.md"
        screen_record_path = f"{output_dir}/screen_record.mp4"

        # 检查是否已存在分析文件
        if os.path.exists(analysis_file):
            logger.info(f"GitHub分析文件已存在: {analysis_file}，跳过处理")
            # 检查是否有录屏文件
            media_files = []
            if os.path.exists(screen_record_path):
                media_files.append(screen_record_path)
            return {"success": True, "analysis_file": analysis_file, "media_files": media_files}

        # 并行处理GitHub分析和录屏
        logger.info(f"开始并行处理GitHub项目分析和录制视频: {github_url}")
        return self._process_github_with_recording(github_url, analysis_file, screen_record_path, output_dir)

    def _process_github_with_recording(self, github_url, analysis_file, screen_record_path, output_dir):
        """并行处理GitHub分析和录制视频"""

        def analysis_task():
            return self._process_github_only(github_url, analysis_file, output_dir)

        def recording_task():
            return self._record_github_video(github_url, screen_record_path)

        # 并行执行
        with ThreadPoolExecutor(max_workers=2) as executor:
            analysis_future = executor.submit(analysis_task)
            record_future = executor.submit(recording_task)

            analysis_result = analysis_future.result()
            record_result = record_future.result()

            media_files = []
            if record_result.get("success", False):
                media_files.append(screen_record_path)
                logger.success(f"✅ GitHub视频录制完成: {screen_record_path}")
            else:
                logger.warning(f"⚠️ GitHub视频录制失败: {record_result.get('error', '未知错误')}")

            if analysis_result["success"]:
                analysis_result["media_files"] = media_files

            return analysis_result

    def _process_github_only(self, github_url, analysis_file, output_dir):
        """只处理GitHub分析"""
        try:
            from agents.github_source_agent_refactored import GitHubAnalyzer

            agent = GitHubAnalyzer()
            result = agent.analyze_repository(github_url)

            if result:
                logger.success(f"✅ GitHub项目分析完成: {result}")
                return {"success": True, "analysis_file": result, "media_files": []}
            else:
                logger.error(f"GitHub项目分析失败: {github_url}")
                return {"success": False}
        except Exception as e:
            logger.error(f"GitHub分析过程中发生错误: {e}")
            return {"success": False}

    def _record_github_video(self, github_url, output_path):
        """录制GitHub视频"""
        try:
            if Path(output_path).is_file():
                logger.info(f"GitHub视频已存在: {output_path} 跳过录制")
                return {"success": True, "output_path": output_path}
            from tools.github_scroller_toolkit import GithubScrollerToolkit

            toolkit = GithubScrollerToolkit()
            result = toolkit.record_github_scroll_video(
                url=github_url,
                output_path=output_path,
                duration=12,
                width=1920,
                height=1080,
                fps=15,
                smooth_factor=0.2,
                title_focus=2,
                star_focus=3,
                zoom_factor=2.0,
                readme_pause=1.5,
            )

            # 转换返回格式以匹配期望的格式
            if result.get("status") == "success":
                return {"success": True, "output_path": output_path}
            else:
                return {"success": False, "error": result.get("error", "录制失败")}
        except Exception as e:
            logger.error(f"GitHub视频录制失败: {e}")
            return {"success": False, "error": str(e)}

    def _process_paper(self, classification, project_name, output_dir):
        """处理Paper内容，包括ArXiv录屏"""
        info = classification["extracted_info"]

        # 检查是本地文件还是URL
        pdf_path = info.get("file_path") or info.get("url")
        if not pdf_path:
            logger.error("Paper处理缺少文件路径或URL")
            return {"success": False}

        analysis_file = f"{output_dir}/{project_name}.md"

        # 区分本地文件和URL
        if pdf_path.startswith(("http://", "https://")):
            # 处理在线PDF文件
            screen_record_path = f"{output_dir}/screen_record.mp4"
            arxiv_url = self._convert_pdf_url_to_arxiv_abs(pdf_path)

            # 并行处理Paper和录屏
            if arxiv_url:
                logger.info(f"检测到ArXiv论文，将并行处理Paper和录制视频: {arxiv_url}")
                return self._process_pdf_with_recording(pdf_path, arxiv_url, analysis_file, screen_record_path, output_dir)
            else:
                # 只处理Paper
                return self._process_pdf_only(pdf_path, analysis_file, output_dir)
        else:
            # 处理本地Paper文件
            logger.info(f"检测到本地Paper文件，开始解析: {pdf_path}")
            return self._process_pdf_local_file(pdf_path, analysis_file, output_dir, project_name)

    def _convert_pdf_url_to_arxiv_abs(self, pdf_url: str):
        """将PDF URL转换为ArXiv论文页面URL"""
        import re

        if not pdf_url or "arxiv.org" not in pdf_url:
            return None

        # 匹配ArXiv PDF URL模式
        pdf_match = re.search(r"arxiv\.org/pdf/(\d{4}\.\d{4,5})", pdf_url)
        if pdf_match:
            paper_id = pdf_match.group(1)
            return f"https://arxiv.org/abs/{paper_id}"

        # 如果已经是abs URL，直接返回
        abs_match = re.search(r"arxiv\.org/abs/(\d{4}\.\d{4,5})", pdf_url)
        if abs_match:
            return pdf_url

        return None

    def _process_pdf_with_recording(self, pdf_url, arxiv_url, analysis_file, screen_record_path, output_dir):
        """并行处理PDF和录制ArXiv视频"""

        def pdf_task():
            return self._process_pdf_only(pdf_url, analysis_file, output_dir)

        def recording_task():
            return self._record_arxiv_video(arxiv_url, screen_record_path)

        # 并行执行
        with ThreadPoolExecutor(max_workers=2) as executor:
            pdf_future = executor.submit(pdf_task)
            record_future = executor.submit(recording_task)

            pdf_result = pdf_future.result()
            record_result = record_future.result()

            media_files = []
            if record_result.get("status", "failed") == "success":
                media_files.append(screen_record_path)
                logger.success(f"✅ ArXiv视频录制完成: {screen_record_path}")
            else:
                logger.warning(f"⚠️ ArXiv视频录制失败: {record_result.get('error', '未知错误')}")

            if pdf_result["success"]:
                pdf_result["media_files"] = media_files

            return pdf_result

    def _process_pdf_only(self, pdf_url, analysis_file, output_dir):
        """只处理PDF文件"""
        if os.path.exists(analysis_file):
            logger.info(f"PDF分析文件已存在: {analysis_file} 跳过处理")
            return {"success": True, "analysis_file": analysis_file, "media_files": []}

        try:
            from tools.pdf_toolkit import PDFToolkit

            config = self.config.get("material", {}).get("pdf", {})
            max_num_pages = config.get("max_num_pages", 30)

            pdf_toolkit = PDFToolkit()
            base_output_dir = os.path.dirname(output_dir)
            result = pdf_toolkit.extract_pdf(pdf_url, base_output_dir, max_num_pages)

            if "error" in result:
                logger.error(f"PDF处理失败: {result['error']}")
                return {"success": False}

            if not os.path.exists(analysis_file):
                logger.error(f"PDF分析文件未生成: {analysis_file}")
                return {"success": False}

            logger.success(f"✅ PDF处理完成: {analysis_file}")
            return {"success": True, "analysis_file": analysis_file, "media_files": []}

        except Exception as e:
            logger.error(f"PDF处理过程中发生错误: {e}")
            return {"success": False}

    def _record_arxiv_video(self, arxiv_url, output_path):
        """录制ArXiv视频"""
        try:
            if Path(output_path).is_file():
                logger.info(f"ArXiv视频已存在: {output_path} 跳过录制")
                return {"status": "success", "output_path": output_path}
            from tools.arxiv_recorder_toolkit import ArxivRecorderToolkit

            toolkit = ArxivRecorderToolkit()
            result = toolkit.record_arxiv_video(
                url=arxiv_url,
                output_path=output_path,
                duration=12,
                width=1920,
                height=1080,
                fps=15,
                smooth_factor=0.2,
                title_focus=4,
                zoom_factor=2.0,
                abstract_pause=1.0,
            )
            return result
        except Exception as e:
            logger.error(f"ArXiv视频录制失败: {e}")
            return {"success": False, "error": str(e)}

    def _process_pdf_local_file(self, file_path, analysis_file, output_dir, project_name):
        """处理本地PDF文件，确保使用正确的项目名称和输出路径"""
        try:
            from tools.pdf_toolkit import PDFToolkit

            pdf_toolkit = PDFToolkit()

            # 使用自定义的PDF处理逻辑，确保输出到正确的目录
            result = self._extract_pdf_with_custom_path(pdf_toolkit, file_path, output_dir, project_name)

            if "error" in result:
                logger.error(f"PDF解析失败: {result['error']}")
                return {"success": False}

            generated_markdown = result.get("markdown_file")
            if generated_markdown and os.path.exists(generated_markdown):
                # 如果生成的markdown文件不在预期位置，复制到正确位置
                if os.path.abspath(generated_markdown) != os.path.abspath(analysis_file):
                    import shutil
                    shutil.copy2(generated_markdown, analysis_file)

                logger.success(f"✅ PDF文件处理完成: {analysis_file}")
                return {"success": True, "analysis_file": analysis_file, "media_files": []}
            else:
                logger.error(f"PDF处理完成但未生成markdown文件")
                return {"success": False}

        except Exception as e:
            logger.error(f"PDF处理过程中发生错误: {e}")
            return {"success": False}

    def _extract_pdf_with_custom_path(self, pdf_toolkit, file_path, output_dir, project_name):
        """使用自定义路径提取PDF内容"""
        try:
            import os
            import json
            from pathlib import Path

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 直接调用PDF工具包的内部方法，但控制输出路径
            logger.info(f"使用项目名: {project_name}")
            logger.info(f"输出目录: {output_dir}")

            # 解析PDF内容
            config = self.config.get("material", {}).get("pdf", {})
            max_num_pages = config.get("max_num_pages", 30)

            # 调用PDF工具包的解析方法
            image_info_file, markdown_file = pdf_toolkit._parse_pdf_to_markdown(
                file_path, output_dir, project_name, max_num_pages
            )

            # 检查生成的文件
            if not os.path.exists(markdown_file):
                return {"error": f"生成的markdown文件不存在: {markdown_file}"}

            if not os.path.exists(image_info_file):
                return {"error": f"生成的图片信息文件不存在: {image_info_file}"}

            # 加载图片信息
            with open(image_info_file, 'r', encoding='utf-8') as f:
                image_info = json.load(f)

            # 返回结果
            return {
                "project_name": project_name,
                "output_dir": output_dir,
                "markdown_file": markdown_file,
                "image_info_file": image_info_file,
                "image_info": image_info
            }

        except Exception as e:
            logger.error(f"自定义PDF提取失败: {e}")
            return {"error": str(e)}

    def _process_url_file(self, classification, project_name, output_dir):
        """处理在线文件URL，先下载后使用本地文件处理逻辑"""
        info = classification["extracted_info"]
        file_url = info["url"]

        logger.info(f"🌐 处理在线文件: {file_url}")

        # 下载文件到本地
        downloaded_file = self._download_file_from_url(file_url, output_dir)
        if not downloaded_file:
            logger.error(f"文件下载失败: {file_url}")
            return {"success": False}

        logger.info(f"📁 文件已下载到: {downloaded_file}")

        # 创建模拟的本地文件分类信息
        local_classification = {
            "type": "local_file",
            "category": classification["category"],
            "subcategory": classification["subcategory"],
            "extracted_info": {
                "file_path": downloaded_file,
                "topic": info.get("topic", ""),
                "purpose": info.get("purpose", f"分析下载的{classification['category']}文件内容"),
            }
        }

        # 调用本地文件处理逻辑
        return self._process_local_file(local_classification, project_name, output_dir)

    def _process_webpage(self, classification, project_name, output_dir):
        """处理网页内容"""
        info = classification["extracted_info"]
        webpage_url = info["url"]

        analysis_file = f"{output_dir}/webpage_analysis.md"
        if os.path.exists(analysis_file):
            logger.info(f"网页分析文件已存在: {analysis_file}，跳过处理")
            return {"success": True, "analysis_file": analysis_file, "media_files": []}

        # 首先尝试使用 WebpageToolkit
        try:
            from tools.webpage_toolkit import WebpageToolkit

            toolkit = WebpageToolkit(timeout=30, use_cache=True)
            base_output_dir = os.path.dirname(output_dir)
            result = toolkit.extract_webpage(
                url=webpage_url, output_base_dir=base_output_dir, project_name=project_name
            )

            if "error" in result:
                logger.warning(f"WebpageToolkit 提取失败: {result['error']}")
                # 继续尝试备用方案
            else:
                generated_markdown = result.get("markdown_file")
                if generated_markdown and os.path.exists(generated_markdown):
                    if os.path.abspath(generated_markdown) != os.path.abspath(analysis_file):
                        import shutil
                        shutil.copy2(generated_markdown, analysis_file)

                    logger.success(f"✅ 网页内容提取成功: {analysis_file}")
                    return {"success": True, "analysis_file": analysis_file, "media_files": []}

        except Exception as e:
            logger.warning(f"WebpageToolkit 处理异常: {e}，尝试备用方案")

        # 备用方案：使用简单的 requests + BeautifulSoup 提取
        try:
            logger.info("🔄 使用备用方案提取网页内容...")
            result = self._extract_webpage_fallback(webpage_url, analysis_file)
            if result:
                logger.success(f"✅ 备用方案网页内容提取成功: {analysis_file}")
                return {"success": True, "analysis_file": analysis_file, "media_files": []}
        except Exception as e:
            logger.warning(f"备用方案也失败: {e}")

        # 最后的备用方案：创建基本的网页信息文件
        try:
            logger.info("🔄 创建基本网页信息文件...")
            self._create_basic_webpage_info(webpage_url, analysis_file, project_name)
            logger.success(f"✅ 基本网页信息文件创建成功: {analysis_file}")
            return {"success": True, "analysis_file": analysis_file, "media_files": []}
        except Exception as e:
            logger.error(f"所有网页处理方案都失败: {e}")
            return {"success": False}

    def _process_local_file(self, classification, project_name, output_dir):
        """处理本地文件内容"""
        info = classification["extracted_info"]
        file_path = info["file_path"]
        # project_name 用于生成输出文件名

        analysis_file = f"{output_dir}/local_file_analysis.md"
        if os.path.exists(analysis_file):
            logger.info(f"本地文件分析已存在: {analysis_file}，跳过处理")
            return {"success": True, "analysis_file": analysis_file, "media_files": []}

        try:
            import shutil
            from pathlib import Path

            path_obj = Path(file_path)
            ext = path_obj.suffix.lower()

            # 处理图片文件
            image_extensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".tif", ".svg", ".webp"]
            if ext in image_extensions:
                logger.info(f"检测到图片文件，开始AI分析: {file_path}")

                purpose = info.get("purpose", "分析图片内容，提取关键信息")
                analysis_result = self._analyze_image_with_ai(file_path, purpose)

                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(analysis_result)

                # 复制原始图片到输出目录
                media_dir = os.path.join(output_dir, "media")
                os.makedirs(media_dir, exist_ok=True)

                original_filename = os.path.basename(file_path)
                target_image_path = os.path.join(media_dir, original_filename)
                shutil.copy2(file_path, target_image_path)

                logger.success(f"✅ 图片文件处理完成: {analysis_file}")
                return {"success": True, "analysis_file": analysis_file, "media_files": [target_image_path]}

            # 处理PDF文件
            elif ext == ".pdf":
                logger.info(f"检测到PDF文件，开始解析: {file_path}")
                return self._process_pdf_local_file(file_path, analysis_file, output_dir, project_name)

            # 处理Office文件
            elif ext in [".docx", ".pptx", ".xlsx", ".xls", ".doc", ".ppt"]:
                logger.info(f"检测到Office文件，使用MarkItDown提取内容和图片: {file_path}")
                return self._process_office_file_hybrid(file_path, analysis_file, output_dir, project_name)

            # 其他文件类型
            else:
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(f"# 本地文件分析\n\n文件路径: {file_path}\n\n")
                    f.write(f"文件类型: {ext}\n\n")
                    f.write("注意：该文件类型需要手动处理或添加相应的处理逻辑。\n")

                logger.info(f"✅ 本地文件处理完成: {analysis_file}")
                return {"success": True, "analysis_file": analysis_file, "media_files": []}

        except Exception as e:
            logger.error(f"本地文件处理失败: {e}")
            return {"success": False}

    def _analyze_image_with_ai(self, image_path, purpose):
        """使用AI模型分析图片内容"""
        try:
            import base64

            from camel.messages import BaseMessage

            from utils.common import AgentFactory

            model = create_model()
            analyzer_agent = AgentFactory.create_analyzer_agent(
                model,
                "图片内容分析专家",
                "你是一个专业的图片内容分析专家，能够详细分析图片中的内容，包括文字、图表、图形、场景等，并生成结构化的markdown报告。",
            )

            # 读取并编码图片
            with open(image_path, "rb") as f:
                image_data = f.read()

            # 检查图片大小，如果过大则压缩
            image_size_mb = len(image_data) / (1024 * 1024)
            if image_size_mb > 10:
                logger.warning(f"图片文件较大 ({image_size_mb:.2f}MB)，尝试压缩...")
                try:
                    import io

                    from PIL import Image

                    with Image.open(image_path) as img:
                        if img.mode in ("RGBA", "LA", "P"):
                            img = img.convert("RGB")

                        max_dimension = 2048
                        if max(img.size) > max_dimension:
                            ratio = max_dimension / max(img.size)
                            new_size = (int(img.size[0] * ratio), int(img.size[1] * ratio))
                            img = img.resize(new_size, Image.Resampling.LANCZOS)

                        buffer = io.BytesIO()
                        img.save(buffer, format="JPEG", quality=85, optimize=True)
                        image_data = buffer.getvalue()

                    logger.info(f"图片已压缩至 {len(image_data) / (1024 * 1024):.2f}MB")
                except Exception as e:
                    logger.warning(f"图片压缩失败: {e}，将使用原始图片")

            # Base64编码
            image_base64 = base64.b64encode(image_data).decode("utf-8")
            filename = os.path.basename(image_path)
            file_ext = os.path.splitext(filename)[1].lower()

            # 构建分析提示
            analysis_prompt = f"""
请详细分析这张图片的内容，生成结构化的markdown报告。

**分析目标**: {purpose}

**分析要求**:
1. **整体描述**: 图片的主要内容和主题
2. **详细内容**: 提取文字、图表、流程图等信息
3. **技术信息**: 如果是技术相关图片，请提取技术要点
4. **关键见解**: 从图片中提取的重要信息和结论
5. **应用价值**: 这些内容在实际应用中的价值
6. **内容总结**: 对图片内容的总结，不超过100字

请基于图片内容生成详细的分析报告，确保信息准确完整。
"""

            # 构建包含图片的消息
            message_content = [
                {"type": "text", "text": analysis_prompt},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/{file_ext[1:] if file_ext else 'jpeg'};base64,{image_base64}",
                        "detail": "high",
                    },
                },
            ]

            # 调用模型分析
            logger.info(f"开始AI分析图片: {filename}")
            response = analyzer_agent.step(BaseMessage.make_user_message(role_name="User", content=message_content))

            analysis_result = response.msg.content
            logger.info(f"图片分析完成，生成内容长度: {len(analysis_result)} 字符")

            return analysis_result

        except Exception as e:
            logger.error(f"AI图片分析失败: {e}")
            filename = os.path.basename(image_path)
            return f"""# 图片内容分析报告

## 基本信息
- **文件名**: {filename}
- **文件路径**: {image_path}
- **状态**: AI分析失败

## 错误信息
{str(e)}

## 建议
请检查以下内容：
1. 确保图片文件有效且未损坏
2. 检查AI模型配置是否正确
3. 确认模型是否支持视觉分析功能
4. 检查网络连接是否正常
"""

    def _process_math_problem(self, classification, project_name, output_dir):
        """处理数学题目"""
        info = classification["extracted_info"]
        file_path = info["file_path"]
        # project_name 用于生成输出文件名
        topic = info.get("topic", "数学题目")
        purpose = info.get("purpose", "解析数学题目")

        analysis_file = f"{output_dir}/math_analysis.md"
        if os.path.exists(analysis_file):
            logger.info(f"数学题目分析文件已存在: {analysis_file}，跳过处理")
            return {"success": True, "analysis_file": analysis_file, "media_files": []}

        try:
            import shutil
            from pathlib import Path

            path_obj = Path(file_path)
            ext = path_obj.suffix.lower()

            # 处理图片文件（数学题目通常是图片格式）
            image_extensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".tif", ".svg", ".webp"]
            if ext in image_extensions:
                logger.info(f"检测到数学题目图片，开始AI分析: {file_path}")

                # 使用数学题目专用的purpose和prompt
                math_purpose = f"""
分析这道数学题目的内容，请详细解读题目要求、已知条件、求解目标，并提供详细的解题思路和步骤。

**分析目标**: {purpose}

**分析重点**:
1. 题目类型识别（几何、代数、概率等）
2. 已知条件提取
3. 求解目标确定
4. 解题思路分析
5. 详细解题步骤
6. 关键知识点总结
7. 类似题目的解题技巧

请生成结构化的数学题目分析报告，便于后续生成教学动画。
"""

                analysis_result = self._analyze_image_with_ai(file_path, math_purpose)

                # 保存分析结果
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(analysis_result)

                # 复制原始图片到输出目录
                media_dir = os.path.join(output_dir, "media")
                os.makedirs(media_dir, exist_ok=True)

                original_filename = os.path.basename(file_path)
                target_image_path = os.path.join(media_dir, original_filename)
                shutil.copy2(file_path, target_image_path)

                # 在markdown中添加原始图片引用
                with open(analysis_file, "a", encoding="utf-8") as f:
                    f.write("\n\n## 原始数学题目\n\n")
                    f.write(f"![数学题目]({os.path.join('media', original_filename)})\n\n")
                    f.write(f"**题目文件**: `{target_image_path}`\n")
                    f.write(f"**题目类型**: {topic}\n")

                logger.success(f"✅ 数学题目分析完成: {analysis_file}")
                return {"success": True, "analysis_file": analysis_file, "media_files": [target_image_path]}

            else:
                # 非图片类型的数学题目文件
                logger.warning(f"数学题目文件不是图片格式: {ext}")
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write("# 数学题目分析\n\n")
                    f.write(f"**文件路径**: {file_path}\n")
                    f.write(f"**文件类型**: {ext}\n")
                    f.write(f"**题目类型**: {topic}\n\n")
                    f.write("**注意**: 该文件不是图片格式，需要手动处理或转换为图片格式。\n")

                return {"success": True, "analysis_file": analysis_file, "media_files": []}

        except Exception as e:
            logger.error(f"数学题目处理失败: {e}")
            return {"success": False}

    def _process_concept_explain(self, classification, project_name, output_dir):
        """处理概念介绍（使用example_explain流程）"""
        info = classification["extracted_info"]
        topic = info.get("topic", "概念解释")
        purpose = info.get("purpose", "解释概念")
        instruction = info.get("instruction", purpose)  # noqa: F841

        logger.info(f"💡 处理概念介绍: {topic}")

        # 确保输出目录结构
        os.makedirs(output_dir, exist_ok=True)

        try:
            from agents.example_explain_agent_refactor import ExampleExplainAgent

                    # 加载原始配置
            original_config_path = "config/config.yaml"
            with open(original_config_path, encoding="utf-8") as f:
                original_config = yaml.safe_load(f)

            # 创建临时配置，保留原始配置的model部分，设置example_explain部分
            temp_config = {
                "model": original_config.get("model", {}),
                "example_explain": {"purpose": purpose, "topic": topic, "max_rounds": 1, "quality_threshold": "良好"},
            }

            # 创建临时配置文件
            temp_config_path = f"{output_dir}/temp_config.yaml"
            with open(temp_config_path, "w", encoding="utf-8") as f:
                yaml.dump(temp_config, f, indent=2, allow_unicode=True)

            logger.info(f"设置概念解释配置: topic={topic}, purpose={purpose}")
            logger.info(f"临时配置文件: {temp_config_path}")

            # 创建Agent实例，使用临时配置文件
            agent = ExampleExplainAgent(config_path=temp_config_path)
            agent.config.output_dir = output_dir

            # 运行example_explain流程
            result = agent.run()

            # 清理临时配置文件
            if os.path.exists(temp_config_path):
                os.remove(temp_config_path)


            if result.get("success", False):
                explain_file = f"{output_dir}/example_explain.md"
                if os.path.exists(explain_file):
                    logger.success(f"✅ 概念解释文件已生成: {explain_file}")

                    # 概念解释完成后，运行代码生成（与feynman_workflow_refactored.py保持一致）
                    generated_files_dir = f"{output_dir}/generated_files"
                    os.makedirs(generated_files_dir, exist_ok=True)
                    code_cmd = f"python scripts/run_claude_code_router.py '{explain_file}' '{output_dir}/code/{topic}' --video_output_path '{generated_files_dir}'"

                    # 使用改进的subprocess启动方式，实时显示日志输出并捕获输出
                    result = self._run_command_with_logging_simple(code_cmd, "概念解释代码生成")

                    if result is not None and result.returncode == 0:
                        logger.success("✅ 概念解释代码生成完成")

                        # 添加视频TTS处理流程
                        video_result = self._process_video_with_tts(explain_file, output_dir, topic)
                        if video_result:
                            logger.success("🎉 视频TTS处理完成")
                            return {"success": True, "analysis_file": explain_file, "media_files": [], "concept_explain_completed": True, "video_with_tts": video_result}
                        else:
                            logger.warning("⚠️ 视频TTS处理失败，但代码生成已完成")
                            return {"success": True, "analysis_file": explain_file, "media_files": [], "concept_explain_completed": True}
                    else:
                        logger.error("概念解释代码生成失败")
                        return {"success": False}
                else:
                    logger.warning(f"概念解释文件未找到: {explain_file}")
                    return {"success": False}
            else:
                logger.error("概念解释生成失败")
                return {"success": False}

        except Exception as e:
            logger.error(f"概念解释处理失败: {e}")
            return {"success": False}

    def _process_video_with_tts(self, explain_file: str, output_dir: str, topic: str) -> dict:
        """调用video_tts_processor处理生成的视频，添加TTS音频和字幕"""
        try:
            from pathlib import Path

            logger.info(f"🎬 开始处理视频TTS: {topic}")

            # 1. 查找生成的视频文件
            video_path = self._find_generated_video(Path(output_dir) / "generated_files")
            if not video_path:
                logger.error(f"❌ 未找到生成的视频文件在目录: {output_dir}")
                return None

            # 2. 查找或生成log文件
            log_file = Path(output_dir) / "animation_timestamp.log"
            if not log_file:
                logger.error(f"❌ 未找到或无法生成log文件")
                return None

            # 3. 准备输出路径
            output_video_path = str(Path(output_dir) / f"{topic}_with_tts.mp4")


            # 5. 构建video_tts_processor命令
            cmd_parts = [
                "python", "utils/video_tts_processor.py",
                f'"{video_path}"',
                f'"{log_file}"',
                f'"{explain_file}"',
                f'"{output_video_path}"'
            ]


            cmd = " ".join(cmd_parts)

            # 6. 执行video_tts_processor
            result = self._run_command_with_logging_simple(cmd, "视频TTS处理")

            # 7. 检查处理结果
            if result and result.returncode == 0:
                # 检查是否生成了带音频的版本
                with_audio_path = output_video_path.replace('.mp4', '_with_audio.mp4')
                if Path(with_audio_path).exists():
                    logger.success(f"✅ 带音频视频生成成功: {with_audio_path}")
                    return {
                        "original_video": video_path,
                        "processed_video": output_video_path,
                        "final_video_with_audio": with_audio_path,
                        "log_file": log_file,
                        "md_file": explain_file
                    }
                elif Path(output_video_path).exists():
                    logger.success(f"✅ 视频处理完成: {output_video_path}")
                    return {
                        "original_video": video_path,
                        "processed_video": output_video_path,
                        "log_file": log_file,
                        "md_file": explain_file
                    }
                else:
                    logger.error(f"❌ 视频处理失败，输出文件不存在")
                    return None
            else:
                logger.error("❌ video_tts_processor执行失败")
                return None

        except Exception as e:
            logger.error(f"❌ 视频TTS处理过程中发生错误: {e}")
            return None

    def _find_generated_video(self, generated_files_dir: Path) -> str:
        """查找生成的视频文件"""
        try:

            if generated_files_dir.exists():
                # 在generated_files目录中查找视频文件
                video_files = list(generated_files_dir.glob("*.mp4"))
                if video_files:
                    # 返回最新的视频文件
                    latest_video = max(video_files, key=lambda f: f.stat().st_mtime)
                    return str(latest_video)

            return None

        except Exception as e:
            logger.error(f"❌ 查找视频文件时出错: {e}")
            return None



    def _process_chat(self, classification, project_name, output_dir):
        """处理聊天内容"""
        info = classification["extracted_info"]
        purpose = info.get("purpose", info.get("instruction", ""))

        logger.info(f"💬 处理聊天内容: {purpose}")

        intro_md_path = f"{output_dir}/{project_name}_intro.md"
        if os.path.exists(intro_md_path):
            logger.info(f"聊天素材文件已存在: {intro_md_path}，跳过处理")
            return {"success": True, "analysis_file": intro_md_path, "media_files": []}

        try:
            from agents.material_agent_refactored import MaterialAgent
            from utils.common import CommonUtils

            # 创建素材生成代理
            agent = MaterialAgent()

            # 将classification数据转换为JSON字符串
            classification_json = json.dumps(classification, ensure_ascii=False)

            # 生成素材内容
            generated_content = agent.generate_material(
                material_content=classification_json,  # 提供classification数据作为材料内容
                purpose=purpose,
                output_dir=os.path.dirname(intro_md_path)
            )

            if generated_content:
                # 手动保存文件
                success = CommonUtils.save_file(generated_content, intro_md_path)
                if success:
                    logger.success(f"✅ 聊天素材生成完成: {intro_md_path}")
                    return {"success": True, "analysis_file": intro_md_path, "media_files": []}
                else:
                    logger.error(f"聊天素材文件保存失败: {intro_md_path}")
                    return {"success": False}
            else:
                logger.error(f"聊天素材生成失败: 返回内容为空")
                return {"success": False}

        except Exception as e:
            logger.error(f"聊天内容处理失败: {e}")
            return {"success": False}

    def _run_command_with_logging_simple(self, cmd, desc, verbose=True):
        """
        运行命令并实时显示日志输出（简化版本）

        Args:
            cmd: 命令字符串
            desc: 命令描述
            verbose: 是否显示详细输出

        Returns:
            CommandResult对象或None（如果执行失败）
        """
        import subprocess
        import threading

        logger.info(f"开始执行: {desc}")
        logger.info("分隔线======================================")
        if verbose:
            logger.info(f"执行命令: {cmd}")

        try:
            process = subprocess.Popen(
                cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1
            )

            def handle_output(stream, is_error=False):
                for line in stream:
                    line = line.rstrip()
                    if not line:
                        continue

                    if is_error:
                        logger.warning(line)
                    else:
                        logger.info(line)

            stdout_thread = threading.Thread(target=handle_output, args=(process.stdout, False))
            stderr_thread = threading.Thread(target=handle_output, args=(process.stderr, True))

            stdout_thread.start()
            stderr_thread.start()

            stdout_thread.join()
            stderr_thread.join()

            return_code = process.wait()

            logger.info("======================================")
            logger.info(f"命令执行完成: {desc}")

            # 创建一个包含returncode的简单对象，模拟CompletedProcess
            class CommandResult:
                def __init__(self, return_code):
                    self.returncode = return_code

            return CommandResult(return_code)
        except Exception as e:
            logger.error(f"命令执行异常: {str(e)}")
            logger.info("======================================")
            return None

    def _process_office_file_hybrid(self, file_path, analysis_file, output_dir, project_name):
        """处理Office文件，在MarkItDown基础上添加图片提取"""
        try:
            # 第一步：使用MarkItDown提取文本（保持原有逻辑）
            from markitdown import MarkItDown
            
            logger.info(f"检测到Office文件，使用MarkItDown转换: {file_path}")
            md_converter = MarkItDown()
            result = md_converter.convert(file_path)
            
            if not result or not result.text_content:
                logger.error("MarkItDown转换失败")
                return {"success": False}
            
            # 第二步：尝试提取图片（可选，失败不影响主流程）
            image_info_list = []
            try:
                image_info_list = self._extract_office_images_simple(file_path, output_dir, project_name)
                if image_info_list:
                    logger.info(f"额外提取了 {len(image_info_list)} 张图片")
            except Exception as e:
                logger.warning(f"图片提取失败（不影响主流程）: {e}")
            
            # 第三步：处理文本内容，替换无效图片引用
            final_content = result.text_content
            
            # 如果有提取的图片，替换MarkItDown中的无效图片引用
            if image_info_list:
                final_content = self._replace_image_references(final_content, image_info_list)
                
                # 在文档末尾添加提取的图片列表
                final_content += "\n\n## 📷 提取的图片\n\n"
                for i, img_info in enumerate(image_info_list):
                    caption = img_info.get("caption", f"{img_info['type']} {i+1}")
                    relative_path = img_info["relative_path"]
                    img_type = img_info["type"]
                    
                    if caption and caption.strip():
                        final_content += f"### {img_type.title()}: {caption}\n\n"
                    else:
                        final_content += f"### {img_type.title()} {i+1}\n\n"
                    
                    final_content += f"![{caption}]({relative_path})\n\n"
            
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(final_content)
            
            # 返回媒体文件路径列表（绝对路径，用于后续处理）
            media_files = [img_info["path"] for img_info in image_info_list]
            
            logger.success(f"✅ Office文件处理完成: {analysis_file}")
            return {"success": True, "analysis_file": analysis_file, "media_files": media_files}
            
        except Exception as e:
            logger.error(f"Office文件处理失败: {e}")
            return {"success": False}

    def _replace_image_references(self, content, image_info_list):
        """替换markdown中的无效图片引用为提取的图片"""
        import re
        
        # 查找所有图片引用模式：![...](...)
        image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        matches = re.finditer(image_pattern, content)
        
        # 收集所有需要替换的图片引用
        replacements = []
        img_index = 0
        
        for match in matches:
            alt_text = match.group(1)
            img_path = match.group(2)
            
            # 跳过已经是正确medias路径的引用
            if img_path.startswith('medias/'):
                continue
                
            # 如果有可用的替换图片
            if img_index < len(image_info_list):
                img_info = image_info_list[img_index]
                new_path = img_info["relative_path"]
                caption = img_info.get("caption", alt_text or f"图片 {img_index + 1}")
                
                # 记录替换信息
                replacements.append({
                    'old': match.group(0),
                    'new': f"![{caption}]({new_path})",
                    'start': match.start(),
                    'end': match.end()
                })
                
                img_index += 1
            else:
                # 如果没有更多图片可替换，移除无效引用
                replacements.append({
                    'old': match.group(0),
                    'new': f"[图片引用已失效: {img_path}]",
                    'start': match.start(),
                    'end': match.end()
                })
        
        # 从后往前替换，避免位置偏移
        for replacement in reversed(replacements):
            content = content[:replacement['start']] + replacement['new'] + content[replacement['end']:]
        
        return content

    def _extract_office_images_simple(self, file_path, output_dir, project_name):
        """简单的Office图片提取（可选功能）"""
        try:
            from pathlib import Path
            
            # 只对可能包含图片的格式进行提取
            if not Path(file_path).suffix.lower() in [".pptx", ".docx", ".xlsx"]:
                return []
            
            # 尝试使用docling提取图片
            from docling.document_converter import DocumentConverter
            from docling_core.types.doc import PictureItem, TableItem
            
            doc_converter = DocumentConverter()
            conv_res = doc_converter.convert(file_path)
            
            # 创建图片目录 - 使用 medias 保持与PDF一致
            medias_dir = os.path.join(output_dir, "medias")
            os.makedirs(medias_dir, exist_ok=True)
            
            images = []
            img_count = 0
            
            for item, level in conv_res.document.iterate_items(with_groups=False):
                if isinstance(item, (PictureItem, TableItem)):
                    try:
                        image = item.get_image(conv_res.document) if item.image is None else item.image.pil_image
                        
                        # 使用与PDF相同的命名规范
                        prefix = "image" if isinstance(item, PictureItem) else "table"
                        # 对于Office文件，没有页码概念，使用序号
                        filename = f"office_{prefix}_{img_count}.png"
                        img_path = os.path.join(medias_dir, filename)
                        
                        image.save(img_path)
                        
                        # 设置图片URI为相对路径（与PDF保持一致）
                        relative_path = f"medias/{filename}"
                        item.image.uri = relative_path
                        
                        images.append({
                            "path": img_path,  # 绝对路径，用于文件操作
                            "relative_path": relative_path,  # 相对路径，用于markdown
                            "caption": item.caption_text(conv_res.document) if hasattr(item, 'caption_text') else "",
                            "type": prefix,
                            "filename": filename
                        })
                        
                        img_count += 1
                    except:
                        continue
            
            return images
            
        except ImportError:
            # docling未安装，跳过图片提取
            return []
        except Exception:
            # 任何错误都不影响主流程
            return []


class StoryboardGenerator:
    """故事板生成模块"""

    def __init__(self, config):
        self.config = config

    def generate(self, analysis_file, purpose, media_files, output_dir, project_name):
        """生成故事板"""
        storyboard_file = f"{output_dir}/{project_name}_storyboard.json"

        if os.path.exists(storyboard_file):
            logger.info(f"✅ 故事板文件已存在，跳过生成: {storyboard_file}")
            return storyboard_file

        try:
            from agents.unified_storyboard_agent import UnifiedStoryboardAgent
            from utils.common import CommonUtils

            logger.info("--- 生成故事板 ---")

            # 读取分析内容
            analysis_content = CommonUtils.read_file(analysis_file)

            # 如果有媒体文件，添加到分析内容中，确保路径正确
            if media_files:
                analysis_content += "\n\n## 可用媒体文件\n\n"
                for media_file in media_files:
                    # 标准化媒体文件路径，确保使用正确的项目名称
                    normalized_path = self._normalize_media_path(media_file, output_dir, project_name)
                    if "screen_record" in media_file:
                        analysis_content += f"- 录屏视频: {normalized_path} (强烈推荐作为视频的开篇介绍使用)\n"
                    else:
                        analysis_content += f"- 媒体文件: {normalized_path}\n"

            # 创建故事板生成代理
            storyboard_agent = UnifiedStoryboardAgent()

            # 生成故事板
            storyboard_result = storyboard_agent.run(analysis_content, purpose, storyboard_file)

            if storyboard_result["status"] != "success":
                logger.error(f"故事板生成失败: {storyboard_result.get('error', '未知错误')}")
                return None

            logger.success(f"✅ 故事板生成成功: {storyboard_file}")
            return storyboard_file

        except Exception as e:
            logger.error(f"故事板生成过程中发生错误: {e}")
            return None

    def _normalize_media_path(self, media_file, output_dir, project_name):
        """标准化媒体文件路径，确保使用正确的项目名称"""
        import os
        from pathlib import Path

        # 如果是绝对路径，转换为相对路径
        if os.path.isabs(media_file):
            try:
                # 尝试相对于当前工作目录
                media_file = os.path.relpath(media_file)
            except ValueError:
                # 如果失败，保持原路径
                pass

        # 检查路径是否已经包含正确的项目名称
        expected_prefix = f"output/{project_name}/"
        if media_file.startswith(expected_prefix):
            return media_file

        # 检查是否是旧的硬编码路径（如 output/github_project/...）
        if media_file.startswith("output/github_project/"):
            # 替换为正确的项目名称
            relative_path = media_file[len("output/github_project/"):]
            return f"output/{project_name}/{relative_path}"

        # 检查是否是相对于output_dir的路径
        if media_file.startswith(output_dir):
            return media_file

        # 如果是相对路径但不在正确的项目目录下，添加正确的前缀
        if not media_file.startswith("output/"):
            return f"output/{project_name}/{media_file}"

        return media_file


class VideoProcessor:
    """视频处理模块"""

    def __init__(self, config, config_path: str = "config/config.yaml"):
        self.config = config
        self.config_path = config_path
        self.audio_utils = AudioUtils(config)

    def render(self, storyboard_file, output_dir, project_name):
        """渲染最终视频"""
        try:
            logger.info("--- 开始视频处理流程 ---")

            # 创建生成文件目录
            generated_files_dir = f"{output_dir}/generated_files"
            os.makedirs(generated_files_dir, exist_ok=True)

            # 读取故事板
            with open(storyboard_file, encoding="utf-8") as f:
                storyboard_data = json.load(f)

            scenes = storyboard_data.get("actions", [])
            if not scenes:
                logger.warning("故事板中没有找到场景")
                return None

            logger.info(f"找到 {len(scenes)} 个场景，开始并行处理...")

            # 并行处理所有场景
            video_files = self._process_scenes_parallel(scenes, generated_files_dir)

            if not video_files:
                logger.error("没有成功渲染的场景")
                return None

            # 拼接视频
            concatenated_video = self._concatenate_video_files(video_files, generated_files_dir, project_name)
            if not concatenated_video:
                logger.error("视频拼接失败")
                return None

            # 添加背景音乐
            final_video = self.audio_utils.add_background_music(concatenated_video, generated_files_dir, project_name)

            if final_video:
                logger.success(f"🎉 视频处理完成！最终视频: {final_video}")
                return final_video
            else:
                logger.error("视频拼接失败")
                return None

        except Exception as e:
            logger.error(f"视频处理过程中发生错误: {e}")
            return None

    def _process_scenes_parallel(self, scenes, output_dir):
        """并行处理所有场景"""
        # 获取配置
        max_workers = self.config.get("video", {}).get("max_workers", os.cpu_count())
        quality = self.config.get("video", {}).get("quality", "h")

        logger.info(f"使用 {max_workers} 个并行工作进程，质量设置: {quality}")

        # 准备任务
        tasks = [
            {"scene_num": i + 1, "scene_dsl": scene, "output_dir": Path(output_dir), "quality": quality}
            for i, scene in enumerate(scenes)
        ]

        # 并行处理
        video_files = [None] * len(scenes)
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_num = {executor.submit(self._process_single_scene, task): task["scene_num"] for task in tasks}

            for future in as_completed(future_to_num):
                scene_num = future_to_num[future]
                try:
                    result_path = future.result()
                    if result_path:
                        video_files[scene_num - 1] = result_path
                        logger.success(f"✅ 场景 {scene_num} 处理完成")
                    else:
                        logger.error(f"❌ 场景 {scene_num} 处理失败")
                except Exception as e:
                    logger.error(f"场景 {scene_num} 处理异常: {e}")

        # 过滤成功的视频文件
        successful_videos = [f for f in video_files if f]
        logger.info(f"成功处理 {len(successful_videos)}/{len(scenes)} 个场景")

        return successful_videos

    def _process_single_scene(self, task):
        """处理单个场景：生成代码、渲染视频、添加字幕"""
        import subprocess

        from dsl.v2.dsl_to_manim import generate_manim_code

        scene_num = task["scene_num"]
        scene_dsl = task["scene_dsl"]
        output_dir = task["output_dir"]
        quality = task["quality"]

        # 从output_dir推断项目名称
        project_name = Path(output_dir).parent.name

        final_clip_path = output_dir / f"scene_{scene_num}_final.mp4"

        try:
            # 预处理scene_dsl，修复硬编码路径
            processed_scene_dsl = self._fix_hardcoded_paths_in_dsl(scene_dsl, project_name)

            # 步骤1: 生成Python代码
            py_file = output_dir / f"scene_{scene_num}.py"
            wrapped_dsl = {"metadata": {"title": f"Storyboard_{scene_num}"}, "actions": [processed_scene_dsl]}
            _, scene_class_name = generate_manim_code(wrapped_dsl, str(py_file), config_path=self.config_path)

            if not scene_class_name:
                raise ValueError("generate_manim_code 未返回场景类名")

            logger.debug(f"场景 {scene_num}: 生成Python代码完成")

            # 步骤2: 渲染视频
            quality_map = {"l": "480p15", "m": "720p30", "h": "1080p60", "k": "2160p60"}
            media_dir = Path("media") / "videos" / py_file.stem / quality_map[quality]
            rendered_video = media_dir / f"{scene_class_name}.mp4"

            manim_cmd = ["manim", str(py_file), scene_class_name, f"--quality={quality}", "--progress_bar", "none"]

            # 使用改进的subprocess启动方式，实时显示日志输出
            result = self._run_command_with_logging(manim_cmd, f"场景 {scene_num} Manim渲染", is_list=True)

            if result is None or result.returncode != 0:
                logger.error(f"场景 {scene_num} Manim渲染失败")
                return None

            if not rendered_video.exists():
                logger.error(f"场景 {scene_num} 渲染输出文件不存在: {rendered_video}")
                return None

            # 由于我们使用实时日志系统，成本信息已经在日志中输出
            logger.debug(f"场景 {scene_num} 渲染完成")

            # 步骤3: 处理字幕
            narration = processed_scene_dsl.get("params", {}).get("narration")
            if not narration:
                logger.debug(f"场景 {scene_num}: 无旁白，跳过字幕处理")
                rendered_video.rename(final_clip_path)
                return str(final_clip_path)

            # 检查SRT文件
            srt_file = media_dir / f"{scene_class_name}.srt"
            if not srt_file.exists():
                logger.warning(f"场景 {scene_num}: SRT文件不存在，跳过字幕处理")
                rendered_video.rename(final_clip_path)
                return str(final_clip_path)

            # 添加字幕
            subtitle_style = "Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑,BorderStyle=3,Outline=1,Shadow=1"
            ffmpeg_cmd = [
                "ffmpeg",
                "-loglevel",
                "warning",
                "-y",
                "-i",
                str(rendered_video),
                "-vf",
                f"subtitles='{srt_file}':force_style='{subtitle_style}'",
                str(final_clip_path),
            ]

            result = self._run_command_with_logging(ffmpeg_cmd, f"场景 {scene_num} 字幕添加", is_list=True)

            if result is None or result.returncode != 0:
                logger.warning(f"场景 {scene_num} 字幕添加失败")
                rendered_video.rename(final_clip_path)
                return str(final_clip_path)

            logger.debug(f"场景 {scene_num}: 字幕添加完成")
            return str(final_clip_path)

        except Exception as e:
            logger.error(f"场景 {scene_num} 处理异常: {e}")
            return None

    def _fix_hardcoded_paths_in_dsl(self, scene_dsl, project_name):
        """修复DSL中的硬编码路径"""
        import copy
        import json

        # 深拷贝避免修改原始数据
        fixed_dsl = copy.deepcopy(scene_dsl)

        # 递归处理DSL中的所有字符串值
        def fix_paths_recursive(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if isinstance(value, str) and "output/github_project/" in value:
                        # 替换硬编码的路径
                        obj[key] = value.replace("output/github_project/", f"output/{project_name}/")
                        logger.debug(f"修复DSL路径: {value} -> {obj[key]}")
                    elif isinstance(value, (dict, list)):
                        fix_paths_recursive(value)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, str) and "output/github_project/" in item:
                        # 替换硬编码的路径
                        obj[i] = item.replace("output/github_project/", f"output/{project_name}/")
                        logger.debug(f"修复DSL路径: {item} -> {obj[i]}")
                    elif isinstance(item, (dict, list)):
                        fix_paths_recursive(item)

        fix_paths_recursive(fixed_dsl)
        return fixed_dsl

    def _concatenate_video_files(self, video_files, output_dir, project_name):
        """拼接多个视频文件为一个完整视频"""
        try:
            logger.info("🎬 开始拼接视频...")

            # 创建拼接列表文件
            concat_list_path = Path(output_dir) / "concat_list.txt"
            with open(concat_list_path, "w", encoding="utf-8") as f:
                for video_file in video_files:
                    # 使用相对路径
                    relative_path = Path(video_file).name
                    f.write(f"file '{relative_path}'\n")

            # 拼接视频（无背景音乐）
            final_video_no_audio = Path(output_dir) / f"{project_name}_final_no_bgm.mp4"
            ffmpeg_concat_cmd = [
                "ffmpeg",
                "-loglevel",
                "warning",
                "-y",
                "-f",
                "concat",
                "-safe",
                "0",
                "-i",
                str(concat_list_path),
                "-c",
                "copy",
                str(final_video_no_audio),
            ]

            result = self._run_command_with_logging(
                ffmpeg_concat_cmd, "视频拼接", is_list=True
            )

            if result is None or result.returncode != 0:
                logger.error("视频拼接失败")
                return None

            logger.success("✅ 视频拼接完成")
            return str(final_video_no_audio)

        except Exception as e:
            logger.error(f"视频拼接过程中发生错误: {e}")
            return None

    def _run_command_with_logging(self, cmd, desc, is_list=False, verbose=True):
        """
        运行命令并实时显示日志输出

        Args:
            cmd: 命令字符串或命令列表
            desc: 命令描述
            is_list: 是否为命令列表
            verbose: 是否显示详细输出

        Returns:
            CompletedProcess对象或None（如果执行失败）
        """
        import subprocess
        import threading

        logger.info(f"开始执行: {desc}")
        logger.info("分隔线======================================")
        if verbose:
            if is_list:
                logger.info(f"执行命令: {' '.join(cmd)}")
            else:
                logger.info(f"执行命令: {cmd}")

        try:
            if is_list:
                process = subprocess.Popen(
                    cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1
                )
            else:
                process = subprocess.Popen(
                    cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1
                )

            def handle_output(stream, is_error=False):
                for line in stream:
                    line = line.rstrip()
                    if not line:
                        continue

                    if is_error:
                        logger.warning(line)
                    else:
                        logger.info(line)

            stdout_thread = threading.Thread(target=handle_output, args=(process.stdout, False))
            stderr_thread = threading.Thread(target=handle_output, args=(process.stderr, True))

            stdout_thread.start()
            stderr_thread.start()

            stdout_thread.join()
            stderr_thread.join()

            return_code = process.wait()

            logger.info("======================================")
            logger.info(f"命令执行完成: {desc}")

            # 创建一个包含returncode的简单对象，模拟CompletedProcess
            class CommandResult:
                def __init__(self, return_code):
                    self.returncode = return_code

            return CommandResult(return_code)
        except Exception as e:
            logger.error(f"命令执行异常: {str(e)}")
            logger.info("======================================")
            return None


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="统一的Feynman智能视频生成工作流")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出模式")
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    workflow = UnifiedFeynmanWorkflow(args.config)
    success = workflow.run(args.verbose)

    if success:
        print("\n✨ 视频生成成功完成！")
    else:
        print("\n❌ 视频生成失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
