# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
sys.path.insert(0, root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_image_(FeynmanScene):
    config.background_color = '#000000'

    def construct(self):
        self.add_background()

        # Action 1: animate_image
        self.scene_method_name = 'animate_image'
        animate_image(
            scene=self,
            title="示例图片展示",
            image_path="assets/manim_logo.png",
            narration="让我们看看这张图片。",
            annotation="这是一张示例图片，展示了重要的内容。",
            cwd=os.path.dirname(os.path.dirname(__file__))
        )

        # Action 2: animate_image
        self.scene_method_name = 'animate_image'
        animate_image(
            scene=self,
            title="系统架构图",
            image_path="assets/manim_logo.png",
            id="architecture_diagram",
            annotation=['系统架构**关键要点**', '前端组件负责*用户交互*', '后端服务处理~~业务逻辑~~', '数据**存储**确保数据持久化$A=B+C$'],
            narration="这张架构图展示了系统的主要组件和它们之间的关系。",
            cwd=os.path.dirname(os.path.dirname(__file__))
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
