{"schema_version": "2.0-mvp", "metadata": {"title": "animate_markdown 示例", "author": "自动生成", "background_color": "BLACK"}, "actions": [{"type": "animate_markdown", "params": {"content": "# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n", "title": "这是一个Markdown示例", "narration": "这是一个Markdown示例，包含标题、文本和代码。"}}, {"type": "animate_markdown", "params": {"content": "## 数据比较📊\n\n| 产品 | 价格 | 评分 |\n| ---- | ---- | ---- |\n| A产品 | ¥199 | 4.5分 |\n| B产品 | ¥299 | 4.8分 |\n| C产品 | ¥399 | 4.9分 |\n", "title": "产品价格比较", "narration": "这个表格比较了两款产品的价格和评分。"}}]}