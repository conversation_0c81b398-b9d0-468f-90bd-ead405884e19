{"schema_version": "2.0-mvp", "metadata": {"title": "animate_image 示例", "author": "自动生成", "background_color": "BLACK"}, "actions": [{"type": "animate_image", "params": {"title": "示例图片展示", "image_path": "assets/manim_logo.png", "narration": "让我们看看这张图片。", "annotation": "这是一张示例图片，展示了重要的内容。"}}, {"type": "animate_image", "params": {"title": "系统架构图", "image_path": "assets/manim_logo.png", "id": "architecture_diagram", "annotation": ["系统架构关键要点", "前端组件负责用户交互", "后端服务处理业务逻辑", "数据存储确保数据持久化"], "narration": "这张架构图展示了系统的主要组件和它们之间的关系。"}}]}