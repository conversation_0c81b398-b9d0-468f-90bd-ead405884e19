# Feynman视频生成服务监控系统

## 概述

这是一个完整的视频生成服务监控和自动重启系统，包含以下组件：

- **监控脚本** (`monitor_video_service.sh`): 持续监控服务状态，自动重启失败的服务
- **启动脚本** (`start_video_service.sh`): 原始的服务启动脚本
- **安装脚本** (`install_service.sh`): 自动安装和配置系统服务
- **服务文件** (`feynman-video-service.service`): systemd服务配置
- **launchd配置** (`com.feynman.video.service.plist`): macOS launchd服务配置

## 功能特性

### 🔍 服务监控
- 定期检查服务健康状态 (默认30秒间隔)
- 通过HTTP健康检查端点检测服务状态
- 彩色状态输出，便于监控

### 📁 路径配置
- 服务文件使用相对路径，便于移植
- 安装时自动转换为绝对路径
- 支持不同环境的部署

### 🔄 自动重启
- 服务失败时自动重启
- 智能重启限制 (最多5次尝试)
- 重启冷却时间 (60秒)
- 防止无限重启循环

### 📝 日志记录
- 详细的监控日志记录
- 服务启动/停止日志
- 错误和警告信息记录
- 时间戳标记

### 🛡️ 系统集成
- 支持systemd (Linux)
- 支持launchd (macOS)
- 开机自启动
- 进程管理

## 快速开始

### 1. 安装服务

```bash
# 运行安装脚本
./install_service.sh
```

安装脚本会自动：
- 检查必要文件
- 配置服务参数
- 创建必要目录
- 设置执行权限
- 安装系统服务

### 2. 手动使用监控脚本

```bash
# 启动监控模式 (默认)
./monitor_video_service.sh

# 仅启动服务
./monitor_video_service.sh --start

# 仅停止服务
./monitor_video_service.sh --stop

# 重启服务
./monitor_video_service.sh --restart

# 检查服务状态
./monitor_video_service.sh --check

# 显示帮助信息
./monitor_video_service.sh --help
```

### 3. 系统服务管理

#### macOS (launchd)
```bash
# 启动服务
launchctl start com.feynman.video.service

# 停止服务
launchctl stop com.feynman.video.service

# 查看状态
launchctl list | grep feynman

# 卸载服务
launchctl unload ~/Library/LaunchAgents/com.feynman.video.service.plist
```

#### Linux (systemd)
```bash
# 启动服务
sudo systemctl start feynman-video-service

# 停止服务
sudo systemctl stop feynman-video-service

# 查看状态
sudo systemctl status feynman-video-service

# 查看日志
sudo journalctl -u feynman-video-service -f

# 重启服务
sudo systemctl restart feynman-video-service

# 启用开机自启动
sudo systemctl enable feynman-video-service
```

## 配置参数

### 监控脚本配置 (`monitor_video_service.sh`)

```bash
# 服务配置
SERVICE_NAME="Feynman视频生成服务"
SERVICE_URL="http://localhost:8000/health"
CHECK_INTERVAL=30          # 检查间隔（秒）
MAX_RESTART_ATTEMPTS=5     # 最大重启尝试次数
RESTART_COOLDOWN=60        # 重启冷却时间（秒）
LOG_FILE="video_service_monitor.log"
```

### 环境变量

```bash
# API密钥
TAVILY_API_KEY="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"

# Python路径
PYTHONPATH="/path/to/agentic-feynman"
```

## 日志文件

- **监控日志**: `video_service_monitor.log`
- **服务日志**: `video_service.log`
- **系统服务日志**: `logs/service.log`
- **错误日志**: `logs/service_error.log`

## 健康检查

服务通过以下端点进行健康检查：
- **URL**: `http://localhost:8000/health`
- **预期响应**: HTTP 200状态码

确保您的视频生成服务提供此健康检查端点。

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查配置文件 `config/config.yaml` 是否存在
   - 确认所有依赖已安装
   - 查看服务日志文件

2. **监控脚本无法连接服务**
   - 确认服务正在运行
   - 检查端口8000是否被占用
   - 验证健康检查端点是否可用

3. **权限问题**
   - 确保脚本有执行权限: `chmod +x *.sh`
   - 检查目录写入权限

4. **系统服务无法启动**
   - 检查服务配置文件路径
   - 查看系统日志: `journalctl -u feynman-video-service`
   - 确认用户权限

### 调试模式

```bash
# 启用详细日志
export DEBUG=1
./monitor_video_service.sh

# 查看实时日志
tail -f video_service_monitor.log
tail -f video_service.log
```

## 安全考虑

- 服务运行在受限环境中
- 文件系统访问限制
- 进程权限隔离
- 日志文件权限控制

## 性能优化

- 合理的检查间隔设置
- 重启限制防止资源浪费
- 日志轮转避免磁盘空间耗尽
- 进程监控和资源限制

## 扩展功能

### 自定义健康检查

修改 `check_service()` 函数以支持自定义健康检查逻辑：

```bash
check_service() {
    # 自定义健康检查逻辑
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$SERVICE_URL")
    if [ "$response" = "200" ]; then
        return 0
    else
        return 1
    fi
}
```

### 通知功能

添加邮件或Slack通知：

```bash
send_notification() {
    local message="$1"
    # 发送通知到邮件或Slack
    echo "$message" | mail -s "服务告警" <EMAIL>
}
```

### 监控面板

集成Prometheus或Grafana进行可视化监控。

## 许可证

本项目遵循MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个监控系统。
