from pathlib import Path

import yaml
from box import Box


def deep_merge(source, destination):
    """递归合并字典，source的值会覆盖destination的值"""
    for key, value in source.items():
        if isinstance(value, dict):
            node = destination.setdefault(key, {})
            deep_merge(value, node)
        else:
            destination[key] = value
    return destination


def load_active_theme(config_path: str = "config/config.yaml") -> Box:
    """加载并合并当前激活的主题配置"""
    # 1. 加载主配置文件
    with open(config_path, encoding="utf-8") as f:
        main_config = yaml.safe_load(f)

    active_theme_name = main_config.get("theme", "default")
    themes_dir = Path("themes")  # 假设主题目录固定

    # 2. 加载默认主题配置
    default_theme_path = themes_dir / "_defaults.yml"
    if not default_theme_path.exists():
        raise FileNotFoundError("'_defaults.yml' not found in themes directory!")

    with open(default_theme_path, encoding="utf-8") as f:
        theme_config = yaml.safe_load(f)

    # 3. 加载并合并激活的主题配置
    active_theme_path = themes_dir / f"{active_theme_name}.yml"
    if active_theme_path.exists():
        with open(active_theme_path, encoding="utf-8") as f:
            active_theme_data = yaml.safe_load(f)
            # 递归合并，激活的主题会覆盖默认值
            theme_config = deep_merge(active_theme_data, theme_config)
    else:
        print(f"Warning: Theme '{active_theme_name}' not found. Using defaults.")

    return Box(theme_config, default_box=True)


theme = load_active_theme()
