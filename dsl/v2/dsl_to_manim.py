# dsl/v2/dsl_to_manim.py
import argparse
import re
import shutil
import subprocess
import sys
from pathlib import Path

from loguru import logger

# 确保可以从脚本位置导入 dsl.v2 包中的模块
try:
    from dsl.v2.ast_builder import ASTBuilder
    from dsl.v2.ast_nodes import SceneNode  # 用于类型提示
    from dsl.v2.code_generator import CodeGenerator
    from dsl.v2.parser import parse_dsl_file
except ImportError:
    script_dir = Path(__file__).parent.resolve()
    project_root = script_dir.parent.parent
    sys.path.insert(0, str(project_root))
    logger.warning(f"Added {project_root} to sys.path to find modules.")
    try:
        from dsl.v2.ast_builder import ASTBuilder
        from dsl.v2.ast_nodes import SceneNode
        from dsl.v2.code_generator import CodeGenerator
        from dsl.v2.parser import parse_dsl_file
    except ImportError as e:
        logger.critical(f"无法导入 DSL 模块。请确保从项目根目录运行，或者 PYTHONPATH 正确设置。 Error: {e}")
        sys.exit(1)


def sanitize_scene_name(raw_title: str) -> str:
    """
    标准化场景名生成逻辑，与 code_generator.py 中的逻辑保持一致。
    """
    if not raw_title:
        return "GeneratedFeynmanScene"

    # Replace spaces and hyphens with underscores
    scene_name = re.sub(r"[\s\-]+", "_", raw_title)
    # Remove any characters not suitable for a Python identifier (including Chinese chars)
    scene_name = re.sub(r"[^0-9a-zA-Z_]", "", scene_name)
    # Ensure it starts with a letter or underscore
    if not scene_name or not (scene_name[0].isalpha() or scene_name[0] == "_"):
        scene_name = f"Scene_{scene_name}"
    if not scene_name.isidentifier():  # Final check
        scene_name = "GeneratedFeynmanScene"
        logger.warning(f"Original title '{raw_title}' generated an invalid class name, using fallback: {scene_name}")

    return scene_name


def generate_manim_code(
    dsl_file_path: str, output_py_file: str, scene_suffix: str = "", config_path: str = "config/config.yaml"
) -> tuple[SceneNode | None, str]:
    """Generates Manim Python code from a DSL file and returns the AST and derived scene name."""
    base_scene_name = "DefaultSceneName"
    try:
        # 1. 解析 DSL
        dsl_data = parse_dsl_file(dsl_file_path)

        # 2. 构建 AST
        builder = ASTBuilder()
        ast = builder.build(dsl_data)

        # 3. 生成代码
        generator = CodeGenerator(config_path=config_path)
        manim_code = generator.generate(ast)

        # 4. 写入文件
        output_path = Path(output_py_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)  # 确保输出目录存在
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(manim_code)
        logger.success(f"Manim 代码已写入: {output_path.resolve()}")

        # Derive base scene name from AST metadata title using the same logic as code_generator.py
        base_scene_name = sanitize_scene_name(ast.metadata.title)

        # Append suffix if provided
        final_scene_name = f"{base_scene_name}{scene_suffix}"

        return ast, final_scene_name

    except FileNotFoundError:
        logger.error(f"输入 DSL 文件未找到: {dsl_file_path}")
    except Exception as e:
        logger.exception(f"处理 DSL 文件 {dsl_file_path} 时发生错误: {e}")
    return None, ""


def run_manim_render(py_file_path: str, scene_name: str, quality: str = "l"):
    """运行 Manim 命令来渲染指定的场景。"""
    # 检查是否应该使用 uv run
    use_uv_run = shutil.which("uv") is not None  # 简单检查 uv 是否可用

    if use_uv_run:
        # 假设 manim 是 uv 管理的依赖
        command = ["uv", "run", "manim", f"-pq{quality}", py_file_path, scene_name]
        logger.info("检测到 uv，将使用 'uv run manim ...' 来渲染。")
    else:
        # 否则，假设 manim 在系统 PATH 中
        command = ["manim", f"-pq{quality}", py_file_path, scene_name]
        logger.info("未检测到 uv 或未配置，将直接调用 'manim ...'。")

    logger.info(f"准备运行 Manim 渲染命令: {' '.join(command)}")
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True, encoding="utf-8")
        logger.success("Manim 渲染成功完成。")
        logger.info("Manim 标准输出:")
        print(result.stdout)  # 打印 Manim 的输出
        if result.stderr:
            logger.warning("Manim 标准错误输出:")
            print(result.stderr)
    except FileNotFoundError:
        logger.error("无法执行渲染命令 ('uv run manim' 或 'manim')。请确保 Manim 已正确安装并配置在 PATH 中。")
    except subprocess.CalledProcessError as e:
        logger.error(f"Manim 渲染失败，返回码: {e.returncode}")
        logger.error("Manim 错误输出:")
        print(e.stderr)
    except Exception as e:
        logger.exception(f"运行 Manim 时发生未知错误: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="Manim DSL v2 Processor: Converts DSL JSON to Manim Python code and optionally renders it.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,  # 显示默认值
    )
    parser.add_argument("dsl_file", help="Path to the input DSL JSON file.")
    parser.add_argument(
        "-o",
        "--output",
        help="Path to the output Manim Python file. Defaults to 'generated/<dsl_filename_stem>.py' relative to the DSL file location.",
    )
    parser.add_argument(
        "-r", "--render", action="store_true", help="Render the generated scene using Manim after generation."
    )
    parser.add_argument("-q", "--quality", default="l", choices=["l", "m", "h", "p", "k"], help="Manim render quality.")
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable debug logging.")
    parser.add_argument("--suffix", default="", help="Suffix to append to the generated scene name.")

    args = parser.parse_args()

    dsl_file_path = Path(args.dsl_file).resolve()  # 获取绝对路径
    if not dsl_file_path.is_file():
        logger.critical(f"输入 DSL 文件不存在: {dsl_file_path}")
        sys.exit(1)

    # 确定输出文件路径
    if args.output:
        output_py_path = Path(args.output).resolve()
    else:
        # 默认输出到 DSL 文件所在目录下的 generated/ 子目录
        output_dir = dsl_file_path.parent / "generated"
        output_filename = dsl_file_path.stem + ".py"
        output_py_path = output_dir / output_filename

    logger.info(f"DSL Input: {dsl_file_path}")
    logger.info(f"Python Output: {output_py_path}")

    # Generate code, passing the suffix
    ast, scene_name = generate_manim_code(str(dsl_file_path), str(output_py_path), args.suffix)

    # If generation succeeded and rendering is requested
    if ast and args.render:
        # Scene name is now determined by generate_manim_code
        if not scene_name:
            logger.error("Scene name could not be determined from AST. Cannot render.")
            sys.exit(1)

        logger.info(f"Preparing to render scene '{scene_name}' (Quality: {args.quality}) from file: {output_py_path}")
        run_manim_render(str(output_py_path), scene_name, args.quality)
    elif args.render:
        logger.warning("代码生成失败或 AST 未返回，跳过渲染。")
    else:
        logger.info("代码生成完成，未请求渲染。")


if __name__ == "__main__":
    main()
