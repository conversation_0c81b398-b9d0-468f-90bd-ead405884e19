# dsl/v2/code_generator.py
import json
import re
import yaml
from pathlib import Path
from typing import Any

from loguru import logger

from tools.theme_manager import theme

# Simplified import: Import only the base node types needed
from .ast_nodes import (
    ActionNode,
    Node,
    SceneNode,
)


class NodeVisitor:
    """AST 节点访问者基类"""

    def visit(self, node: Node, **kwargs):
        """根据节点类型分发到具体的 visit_ 方法"""
        method_name = f"visit_{type(node).__name__.lower()}"
        visitor = getattr(self, method_name, self.generic_visit)
        logger.trace(
            f"NodeVisitor dispatch: Node={type(node).__name__}, Method={method_name}, Visitor={visitor.__name__}"
        )
        return visitor(node, **kwargs)

    def generic_visit(self, node: Node, **kwargs):
        """默认访问者，用于未实现具体 visit_ 方法的节点"""
        logger.warning(f"NodeVisitor: No specific visit method for {type(node).__name__}. Skipping.")
        return ""  # 或者 None


class CodeGenerator(NodeVisitor):
    """
    遍历 AST 并生成 Manim Python 代码。
    现在将 visit 逻辑委托给 dsl.v2.visitors 中的函数。
    """

    def __init__(self, config_path: str = "config/config.yaml"):
        self._code_lines: list[str] = []
        self._indent_level: int = 0
        self._indent: str = "    "  # 4 spaces
        self._config_path = config_path
        self._config = self._load_config()

    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            config_path = Path(self._config_path)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                logger.warning(f"配置文件 {self._config_path} 不存在，使用默认配置")
                return {"app": {"server": False}}
        except Exception as e:
            logger.warning(f"加载配置文件失败: {e}，使用默认配置")
            return {"app": {"server": False}}

    def _is_server_mode(self) -> bool:
        """检查是否启用服务器模式"""
        return self._config.get("app", {}).get("server", False)

    def _format_arg(self, value: Any) -> str:
        """Converts a Python value into its string representation for code generation."""
        if isinstance(value, str):
            return json.dumps(value, ensure_ascii=False)  # Handles quotes and special characters
        elif isinstance(value, bool):
            return "True" if value else "False"
        elif isinstance(value, (int, float)):
            return str(value)
        elif value is None:
            return "None"
        elif isinstance(value, (list, dict)):
            # For simple list/dict literals that are directly usable in Python code
            return repr(value)
        else:
            # Fallback for other types, e.g., Manim constants like UP, DOWN
            # If these are passed as strings from DSL, json.dumps handles them.
            # If they are actual Manim objects/constants in AST, repr() might be okay,
            # or a specific mapping might be needed if they should be unquoted names.
            logger.warning(
                f"CodeGenerator._format_arg: Potentially unhandled argument type {type(value)}. Using repr(). Value: {value}"
            )
            # For Manim constants like UP, RIGHT, etc., they should be passed as strings "UP", "RIGHT"
            # from the DSL or handled specifically if they are actual objects in the AST.
            # If they are intended to be bare names in the generated code (e.g., `direction=UP`),
            # this logic needs adjustment. For now, assume they are strings or simple repr-able values.
            return repr(value)

    def _add_line(self, line: str = ""):
        """添加一行代码，自动处理缩进"""
        if not line:
            self._code_lines.append("")
        else:
            self._code_lines.append(f"{self._indent * self._indent_level}{line}")

    def _indent_block(self):
        """增加缩进级别"""
        self._indent_level += 1

    def _dedent_block(self):
        """减少缩进级别，防止负数"""
        self._indent_level = max(0, self._indent_level - 1)

    def generate(self, scene_node: "SceneNode") -> str:  # Type hint for SceneNode
        """
        生成整个 Manim 场景代码。

        Args:
            scene_node: 场景的根 AST 节点。

        Returns:
            生成的 Python 代码字符串。
        """
        self._code_lines = []  # Reset for new generation
        self._indent_level = 0  # Reset indent level

        # Start traversal from the root scene node
        self.visit(scene_node)  # Calls the overridden visit method below

        generated_code = "\n".join(self._code_lines)
        # 添加文件末尾的换行符，以符合 pre-commit hook 要求
        if generated_code and not generated_code.endswith("\n"):
            generated_code += "\n"
        elif not generated_code:
            generated_code = "\n"  # 空文件也需要一个换行符

        logger.trace(f"Generated Code:\n{generated_code}")  # Use trace for long output
        return generated_code

    # --- Internal Visitor Methods for AST Nodes (New Style) ---
    def visit(self, node: Node, **kwargs):
        """Overrides NodeVisitor.visit for CodeGenerator-specific dispatch logic."""
        node_type_name = type(node).__name__

        # Prioritize internal _visit_ methods specific to CodeGenerator
        internal_method_name = f"_visit_{node_type_name}"
        internal_visitor_method = getattr(self, internal_method_name, None)

        if internal_visitor_method:
            return internal_visitor_method(node, **kwargs)
        else:
            # If no specific _visit_ method in CodeGenerator, fall back to NodeVisitor's logic
            # (which might try public visit_ methods or generic_visit)
            logger.debug(
                f"CodeGenerator: No internal method {internal_method_name} found. "
                f"Falling back to super().visit() for {node_type_name}."
            )
            return super().visit(node, **kwargs)

    def _visit_SceneNode(self, node: SceneNode, **kwargs):
        """Generates the overall structure of the Manim scene script."""

        # 1. Add header comments and essential imports
        self._add_line("# Generated by Feynman DSL v2 Code Generator")
        if node.metadata.author:
            self._add_line(f"# Author: {node.metadata.author}")
        self._add_line("# -*- coding: utf-8 -*-")  # Ensure UTF-8 for special characters in titles/narration
        self._add_line()
        self._add_line("import sys")
        self._add_line("import os")
        # Add project root to sys.path to allow finding dsl.v2 etc. when script is in output folder
        if self._is_server_mode():
            # 服务器模式：使用修复后的路径设置
            self._add_line("root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))")
            self._add_line("sys.path.insert(0, root_dir)")
            self._add_line("os.chdir(root_dir)  # 设置工作目录为项目根目录")
        else:
            # 本地模式：使用原始路径设置
            self._add_line("root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))")
            self._add_line("sys.path.insert(0, root_dir)")
        self._add_line()
        self._add_line("from manim import *")
        self._add_line("from dsl.v2.core.scene import FeynmanScene")
        self._add_line("from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered")
        # Add any other common utility imports if absolutely necessary globally
        # e.g., from utils.edgetts_service import EdgeTTSService # If FeynmanScene doesn't handle it
        self._add_line()

        # 2. Define the scene class name using standardized logic
        raw_title = node.metadata.title if node.metadata.title else "GeneratedFeynmanScene"
        scene_class_name = self._sanitize_scene_name(raw_title)

        self._add_line(f"class {scene_class_name}(FeynmanScene):")
        self._indent_block()

        # 3. Add background color config
        self._add_line(f"config.background_color = '{theme.colors.background}'")
        self._add_line()
        # 3. Optional: Add background color config (if different from FeynmanScene default)
        # if node.metadata.background_color and node.metadata.background_color.upper() != "BLACK":
        #     # Assuming background_color is a string like "BLUE" or "#RRGGBB"
        #     color_val_str = self._format_arg(node.metadata.background_color.upper())
        #     self._add_line(f"config.background_color = {color_val_str}")  # Manim usually wants string for color name
        #     self._add_line()  # Add a blank line for readability

        # 4. Define the construct method
        self._add_line("def construct(self):")
        self._indent_block()
        self._add_line("self.add_background()")
        self._add_line()

        # 5. Iterate and generate code for actions
        if node.actions:
            for i, action_node in enumerate(node.actions):
                self._add_line(f"# Action {i + 1}: {action_node.type}")
                self.visit(action_node, **kwargs)  # Dispatch to _visit_SpecificActionNode
                # Ensure a blank line is added *after* the action code, maintaining current indent
                if i < len(node.actions) - 1:  # Don't add extra blank line after the last action if final wait is next
                    self._add_line()
        else:
            self._add_line("pass  # No actions defined in the DSL scene")
            self._add_line()  # Add a blank line even if no actions

        # 6. Add final wait to hold the last frame
        self._add_line("# --- Final wait to hold the last frame ---")
        self._add_line("self.wait(1)")

        # 7. End construct and class definition
        self._dedent_block()  # Exit construct method
        self._dedent_block()  # Exit class definition

    def _visit_ActionNode(self, node: ActionNode):
        """统一处理所有动作节点的访问方法"""
        # 获取场景方法名称
        scene_method_name = node.type

        if scene_method_name == "wait":
            return

        # 特殊处理某些节点类型的方法名
        if node.type == "side_by_side_comparison":
            scene_method_name = "animate_side_by_side_comparison"

        # 收集所有参数
        args_dict = {}

        # 添加节点的所有非None属性
        for key, value in node.__dict__.items():
            # 跳过type和params字段，以及私有字段
            if key not in ["type", "params"] and not key.startswith("_") and value is not None:
                args_dict[key] = value

        # 添加params字典中的所有参数
        if hasattr(node, "params") and node.params:
            args_dict.update(node.params)

        # 构建参数字符串
        self._add_line(f"self.scene_method_name = '{scene_method_name}'")
        self._add_line(scene_method_name + "(")
        self._indent_block()
        args_str_list = ["scene=self"]
        for key, value in args_dict.items():
            args_str_list.append(f"{key}={self._format_arg(value)}")
        if node.type == "animate_image":
            args_str_list.append("cwd=os.path.dirname(os.path.dirname(__file__))")
        # 生成方法调用代码
        args_str = (",\n" + self._indent * self._indent_level).join(args_str_list)
        self._add_line(args_str)
        self._dedent_block()
        self._add_line(")")

    def _sanitize_scene_name(self, raw_title: str) -> str:
        """
        标准化场景名生成逻辑，与 dsl_to_manim.py 中的逻辑保持一致。
        """
        if not raw_title:
            return "GeneratedFeynmanScene"

        # Replace spaces and hyphens with underscores
        scene_class_name = re.sub(r"[\s\-]+", "_", raw_title)
        # Remove any characters not suitable for a Python identifier (including Chinese chars)
        scene_class_name = re.sub(r"[^0-9a-zA-Z_]", "", scene_class_name)
        # Ensure it starts with a letter or underscore
        if not scene_class_name or not (scene_class_name[0].isalpha() or scene_class_name[0] == "_"):
            scene_class_name = f"Scene_{scene_class_name}"
        if not scene_class_name.isidentifier():  # Final check
            scene_class_name = "GeneratedFeynmanScene"
            logger.warning(
                f"Original title '{raw_title}' generated an invalid class name, using fallback: {scene_class_name}"
            )

        return scene_class_name
