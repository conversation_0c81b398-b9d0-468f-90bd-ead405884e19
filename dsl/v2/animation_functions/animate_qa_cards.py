"""
effect: |
    创建完全按照现代设计风格的QA卡片展示动画

use_cases:
    - 教育培训中的问答展示
    - 知识总结和回顾
    - 产品FAQ展示
    - 学术论文要点问答
    - 面试问题练习

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  qa_data:
    type: list
    desc: QA数据列表，每个元素包含question和answer字段
    required: true
  title:
    type: str
    desc: 展示标题
    required: true
  cards_per_screen:
    type: int
    desc: 每屏显示的卡片数量（建议最多3个）
    required: false
    default: 3
  duration_per_card:
    type: float
    desc: 每张卡片的展示时长（秒）
    required: false
    default: 2.0
  narration:
    type: str
    desc: 语音旁白内容
    required: true

dsl_examples:
  - type: animate_qa_cards
    params:
      qa_data:
        - question: "什么是'第一性原理'思维？"
          answer: "第一性原理是一种解决问题的思维方式，它强调回归事物的本质和基本公理进行分析，而不是依赖类比或既有经验。"
        - question: "什么是'SMART原则'？"
          answer: "SMART原则是设定目标的经典方法论，确保目标清晰可行。它代表：具体的、可衡量的、可达成的、相关的和有时限的。"
        - question: "什么是'心流'状态？"
          answer: "心流是一种个体完全沉浸、全神贯注于某项活动时的心理状态。在这种状态下，人会感到极大的愉悦和满足。"
      title: "知识问答卡片"
      narration: "让我们通过这些精美的卡片来学习重要概念"

notes:
  - 问题要简短，有洞察力
  - 回答言简意赅
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from loguru import logger
from manim import *

from tools.theme_manager import theme
from utils.title_utils import create_title


def _get_theme_aware_colors():
    """
    根据当前主题获取适配的颜色配置 - 只使用主题中存在的颜色

    Returns:
        dict: 包含各种界面元素颜色的字典
    """ 
    return {
        "error_color": theme.colors.cyclic_colors[3],  # 使用循环颜色中的红色
        "shadow_color": theme.colors.surface_dark,  # 使用深色表面
        "question_color": theme.colors.accent,  # 使用强调色
        "answer_color": theme.colors.text,  # 使用主题文字色
        "surface_color": theme.colors.surface_light,  # 使用浅色表面
        "text_secondary_color": theme.colors.primary_light,  # 使用主色浅色
    }

def animate_qa_cards(
    scene: "FeynmanScene",
    qa_data: list[dict[str, str]],
    title: str = "Prismatic Focus Q&A Cards",
    cards_per_screen: int = 3,
    duration_per_card: float = 2.0,
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """
    创建完全按照HTML设计的现代QA卡片展示动画
    """
    unique_id = id or f"qa_cards_{abs(hash(str(qa_data))) % 10000}"

    logger.info(f"开始创建HTML风格QA卡片动画，共{len(qa_data)}个问答对")

    try:
        # 获取主题相关颜色
        colors = _get_theme_aware_colors()

        # 设置背景颜色 - 使用主题背景色
        scene.camera.background_color = theme.colors.background

        with scene.voiceover(narration) as tracker:  # noqa: F841
            # 创建标题对象
            initial_title, title_text = create_title(title, scene=scene)
            # Handle clear_current_mobject for transition support
            scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

            # 直接添加标题，动画已在 create_title 中处理
            scene.add(title_text)

            # 计算需要的屏幕数
            total_screens = (len(qa_data) + cards_per_screen - 1) // cards_per_screen

            for screen_idx in range(total_screens):
                start_idx = screen_idx * cards_per_screen
                end_idx = min(start_idx + cards_per_screen, len(qa_data))
                screen_qa_data = qa_data[start_idx:end_idx]

                # 创建当前屏幕的卡片
                cards = []
                card_group = VGroup()

                for i, qa in enumerate(screen_qa_data):
                    card = create_html_style_card(
                        qa["question"],
                        qa["answer"],
                        i,  # 卡片索引用于颜色主题
                        len(screen_qa_data),
                        colors,
                    )
                    cards.append(card)
                    card_group.add(card)

                # 横向排列卡片 - 使用组件配置中的padding
                card_spacing = theme.components.list.padding * 2
                
                if len(screen_qa_data) == 1:
                    card_group.move_to(ORIGIN + DOWN * card_spacing)
                else:
                    # 使用组件配置的间距
                    card_group.arrange(RIGHT, buff=card_spacing)
                    card_group.move_to(ORIGIN + DOWN * card_spacing)

                    # 放宽屏幕边界限制，允许更大的卡片
                    max_width = 14
                    if card_group.get_width() > max_width:
                        card_group.scale_to_fit_width(max_width)

                # 应用优雅的入场动画
                apply_html_style_animation(scene, cards)

                # 等待展示
                scene.wait(duration_per_card)

                # 如果不是最后一屏，清除当前卡片
                if screen_idx < total_screens - 1:
                    scene.play(*[FadeOut(card) for card in cards], run_time=1.0)

            # 创建显示组：initial_title用于转场，title_text用于显示，cards为实际内容
            # 转场系统会使用submobjects[0]（initial_title）作为转场对象
            final_card_group = VGroup(*cards) if cards else VGroup()
            display_group = Group(initial_title, title_text, final_card_group)

            # Update current_mobject saving - ensure title is first submobject
            scene.current_mobj = display_group
            scene.save_scene_state(content_type="qa_cards", mobject_id=unique_id)

    except Exception as e:
        logger.error(f"创建HTML风格QA卡片动画时出错: {e}", exc_info=True)
        # 创建错误提示
        error_text = Text(
            f"QA卡片创建失败\n错误: {str(e)[:50]}...", font_size=24, color=colors["error_color"], font="Arial"
        ).move_to(ORIGIN)
        scene.add(error_text)
        scene.current_mobj = error_text


def create_html_style_card(question: str, answer: str, index: int, total: int, colors: dict) -> VGroup:
    """创建主题化的QA卡片，使用当前主题配置"""

    # 使用主题的循环颜色作为卡片主题色
    theme_color = theme.colors.cyclic_colors[index % len(theme.colors.cyclic_colors)]

    # 答案图标使用次要文字颜色
    answer_icon_color = colors["text_secondary_color"]

    # 根据卡片数量调整尺寸（模拟响应式设计）
    if total == 1:
        card_width, card_height = 8, 6
    elif total == 2:
        card_width, card_height = 8, 6
    else:  # 3个
        card_width, card_height = 6.5, 6

    # 创建卡片背景 - 使用主题表面色和圆角配置
    card_bg = RoundedRectangle(
        corner_radius=theme.components.background_corner_radius,
        width=card_width,
        height=card_height,
        fill_color=colors["surface_color"],
        fill_opacity=theme.components.background_opacity,
        stroke_color=colors["text_secondary_color"],
        stroke_width=theme.components.stroke_width * 0.25,  # 较细的边框
        stroke_opacity=0.3,
    )

    # 创建顶部彩色强调条 - 使用组件配置中的圆角半径作为高度参考
    accent_height = theme.components.background_corner_radius * 0.5
    top_accent = Rectangle(
        width=card_width,
        height=accent_height,
        fill_color=theme_color,
        fill_opacity=1.0,
        stroke_width=0,
    )
    top_accent.move_to(card_bg.get_top())

    # 创建卡片阴影效果 - 使用组件配置中的参数
    shadow = card_bg.copy()
    is_dark = theme.colors.background == "#000000"
    shadow.set_fill(color=colors["shadow_color"], opacity=0.1 if not is_dark else 0.2)
    shadow.set_stroke(width=0)
    # 使用组件的圆角半径作为阴影偏移参考
    offset = theme.components.background_corner_radius * 0.3
    shadow.shift(DOWN * offset + RIGHT * offset)

    # 创建Q图标 - 使用组件配置中的节点半径
    icon_radius = theme.components.timeline.node_radius
    q_circle = Circle(
        radius=icon_radius,
        fill_color=theme_color,
        fill_opacity=1.0,
        stroke_width=0,
    )
    q_label = Text(
        "Q",
        font_size=theme.typography.sizes.body,
        color=theme.colors.background,  # 使用背景色作为对比色
        font=theme.typography.fonts.body,
        weight=BOLD,
    )
    q_icon = VGroup(q_circle, q_label)

    # 创建A图标 - 使用主题配置
    a_circle = Circle(
        radius=icon_radius,
        fill_color=answer_icon_color,
        fill_opacity=1.0,
        stroke_width=0,
    )
    a_label = Text(
        "A",
        font_size=theme.typography.sizes.body,
        color=theme.colors.background,  # 使用背景色作为对比色
        font=theme.typography.fonts.body,
        weight=BOLD,
    )
    a_icon = VGroup(a_circle, a_label)

    # 智能自动换行函数 - 定义在使用之前
    def smart_wrap_text(
        text: str, max_width: float, font_size: int, card_height: float, is_question: bool = False
    ) -> str:
        """智能自动换行文本，根据卡片宽度和高度优化显示"""

        # 根据字体大小和卡片宽度精确计算每行字符数
        # 中文字符大约是英文字符的2倍宽度
        char_width_ratio = font_size * 0.6  # 字符宽度比例
        chars_per_line = int(max_width / char_width_ratio * 2)  # 更精确的估算

        # 根据内容类型设置最大行数限制
        if is_question:
            # 问题最多显示2行，保持简洁
            max_lines = 3
        else:
            # 答案文字最多显示5行
            max_lines = 5

        # 设置合理的字符数范围
        chars_per_line = max(15, min(chars_per_line, 50))

        # 智能换行：优先在标点符号处换行
        def smart_break(text, width):
            if len(text) <= width:
                return [text]

            lines = []
            remaining = text

            while remaining and len(lines) < max_lines - 1:
                if len(remaining) <= width:
                    lines.append(remaining)
                    remaining = ""  # 清空remaining，避免重复添加
                    break

                # 寻找合适的断点（标点符号）
                break_point = width
                for i in range(width, max(width // 2, 10), -1):
                    if i < len(remaining) and remaining[i] in "，。！？；：、":
                        break_point = i + 1
                        break
                    elif i < len(remaining) and remaining[i] == " ":
                        break_point = i
                        break

                lines.append(remaining[:break_point].strip())
                remaining = remaining[break_point:].strip()

            # 如果还有剩余内容且未超过行数限制
            if remaining and len(lines) < max_lines:
                if len(remaining) <= width * 1.2:  # 允许最后一行稍长
                    lines.append(remaining)
                else:
                    lines.append(remaining[: width - 3] + "...")
            elif remaining:
                # 如果超过行数限制，截断并添加省略号
                if lines:
                    lines[-1] = lines[-1][: width - 3] + "..."

            return lines

        wrapped_lines = smart_break(text, chars_per_line)
        return "\n".join(wrapped_lines)

    # 文字大小调整 - 使用主题字体系统
    # 使用组件配置中的padding作为文字边距参考
    padding = theme.components.list.padding
    
    if total == 1:
        q_font_size = theme.typography.sizes.h3  # 大标题
        a_font_size = theme.typography.sizes.h4  # 中等标题
        text_width = card_width - (padding * 6)  # 使用配置中的padding
    elif total == 2:
        q_font_size = theme.typography.sizes.h3  # 大标题
        a_font_size = theme.typography.sizes.h4  # 中等标题
        text_width = card_width - (padding * 6)  # 使用配置中的padding
    else:
        q_font_size = theme.typography.sizes.h4  # 中等标题
        a_font_size = theme.typography.sizes.body  # 正文
        text_width = card_width - (padding * 4)  # 使用配置中的padding

    # 创建问题文本 - 使用黄色字体
    wrapped_question = smart_wrap_text(question, text_width, q_font_size, card_height, is_question=True)

    q_text = Text(
        wrapped_question,
        font_size=q_font_size,
        color=colors["question_color"],  # 问题使用强调色
        font=theme.typography.fonts.body,
        weight=BOLD,
        line_spacing=theme.typography.line_spacing.normal,  # 使用正常间距
    )
    # 添加描边效果让文字更粗 - 使用强调色
    q_text.set_stroke(color=colors["question_color"], width=1, opacity=0.8)
    q_text.set_max_width(text_width)

    # 创建答案文本 - 使用白色高亮度
    wrapped_answer = smart_wrap_text(answer, text_width, a_font_size, card_height, is_question=False)

    a_text = Text(
        wrapped_answer,
        font_size=a_font_size,
        color=colors["answer_color"],  # 回答使用主题文字色
        font=theme.typography.fonts.body,
        line_spacing=theme.typography.line_spacing.relaxed,  # 使用放松间距，让回答更易读
    )
    a_text.set_max_width(text_width)

    # 排列问答内容 - 使用组件配置中的padding
    q_group = VGroup(q_icon, q_text).arrange(RIGHT, buff=padding, aligned_edge=UP)
    a_group = VGroup(a_icon, a_text).arrange(RIGHT, buff=padding, aligned_edge=UP)

    # 问答对之间的间距 - 使用组件配置中的padding
    content_group = VGroup(q_group, a_group).arrange(DOWN, buff=padding * 2, aligned_edge=LEFT)

    # 内容居中定位
    scale_factor = min(card_width * 0.9 / content_group.width, card_height * 0.9 / content_group.height)
    content_group.scale(scale_factor).move_to(card_bg.get_center())

    # 创建悬停效果 - 使用主题配置
    hover_glow = card_bg.copy()
    hover_glow.set_fill(opacity=0)
    hover_glow.set_stroke(color=theme_color, width=theme.components.stroke_width * 0.5, opacity=0.4)

    # 组合所有元素
    card = VGroup(shadow, card_bg, top_accent, hover_glow, content_group)

    return card


def apply_html_style_animation(scene: "FeynmanScene", cards: list[VGroup]) -> None:
    """应用主题化的依次展现动画 - 每次只显示一张卡片"""
    # 使用默认的smooth动画
    rate_func = smooth
    
    # 使用组件配置中的参数作为动画时长参考
    base_duration = theme.components.stroke_width * 0.5  # 基于stroke_width计算动画时长

    if len(cards) == 1:
        # 单张卡片的简单动画
        card = cards[0]
        # 使用组件配置的padding作为移动距离
        move_distance = theme.components.list.padding * 3
        card.shift(DOWN * move_distance + LEFT * 2)
        card.set_opacity(0)

        scene.play(
            card.animate.shift(UP * move_distance + RIGHT * 2).set_opacity(1),
            run_time=base_duration,
            rate_func=rate_func,
        )

        # 轻微的悬停效果
        hover_distance = theme.components.list.padding * 0.5
        scene.play(
            card.animate.shift(UP * hover_distance),
            run_time=base_duration * 0.5,
            rate_func=there_and_back,
        )
    else:
        # 多张卡片的依次展现动画 - 每次只显示一张

        # 1. 记录所有卡片的最终正确位置（横向排列）
        final_positions = []
        for card in cards:
            final_positions.append(card.get_center())

        # 2. 初始化：所有卡片设置到正确位置但都隐藏
        for i, (card, final_pos) in enumerate(zip(cards, final_positions)):
            card.move_to(final_pos)
            card.set_opacity(0)  # 初始全部隐藏

        # 3. 依次显示每张卡片：出现 -> 放大 -> 停留 -> 缩回 -> 隐藏
        for i, (card, final_pos) in enumerate(zip(cards, final_positions)):
            # 先让卡片出现在正确位置
            scene.play(
                card.animate.set_opacity(1),
                run_time=base_duration * 0.3,
                rate_func=rate_func,
            )

            # 短暂停顿后放大到屏幕中心
            scene.wait(0.2)
            scene.play(
                card.animate.scale(1.5).move_to(ORIGIN),
                run_time=base_duration * 0.8,
                rate_func=rate_func,
            )

            # 停留展示
            scene.wait(2)

            # 缩回到原位置和大小
            scene.play(
                card.animate.scale(1 / 1.5).move_to(final_pos),
                run_time=base_duration * 0.8,
                rate_func=rate_func,
            )

            # 卡片间的停顿（但保持可见）
            if i < len(cards) - 1:
                scene.wait(0.3)

        # 4. 最后给所有可见的卡片添加轻微的悬停效果
        scene.wait(0.5)
        # 对所有卡片应用悬停效果
        hover_distance = theme.components.list.padding * 0.5
        for card in cards:
            scene.play(
                card.animate.shift(UP * hover_distance),
                run_time=base_duration * 0.5,
                rate_func=there_and_back,
            )
