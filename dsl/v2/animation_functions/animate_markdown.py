"""
effect: |
    将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。
    支持各种Markdown元素，包括标题、列表、代码块、表格等。

use_cases:
    - 展示格式化的文本内容，如教程说明、演示文稿
    - 在动画中展示结构化的信息，如列表和表格
    - 显示带有语法高亮的代码片段
    - 创建包含文本和图片的混合内容

params:
  scene:
    type: FeynmanScene
    desc: Man<PERSON>场景实例（由系统自动传入）
  content:
    type: str
    desc: Markdown格式的文本内容，尽量用格式化的元素增强语义丰富性，如标题、列表、加粗、斜体等，减少纯文本的使用。文字必须简洁精练，禁止直接展示长句形式的文本，详细信息可以通过旁白体现，屏幕上只能展示最重要的关键信息。
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  title:
    type: str
    desc: 当前内容的标题，必须言简意赅，概括内容要点。
    required: true
  narration:
    type: str
    desc: 在内容显示时播放的语音旁白文本
    required: true

dsl_examples:
  - type: animate_markdown
    params:
      content: |
        # 主标🚗题

        ⚠️ 这是一段普通文本，支持**粗体**和*斜体*。

        > 🚰 这是一个引用💦块。

        ## 子标题

        - 列表🐆 项1
        - 🐇 列表项2
        - $E = mc^2$

        $$
        a^2 = b^2 + c^2
        $$

        ```python
        def hello_world():
            print("Hello, world!")
        ```
      title: 这是一个Markdown示例
      narration: 这是一个Markdown示例，包含标题、文本和代码。
  - type: animate_markdown
    params:
      content: |
        ## 数据比较📊

        | 产品 | 价格 | 评分 |
        | ---- | ---- | ---- |
        | A产品 | ¥199 | 4.5分 |
        | B产品 | ¥299 | 4.8分 |
        | C产品 | ¥399 | 4.9分 |
      title: 产品价格比较
      narration: 这个表格比较了两款产品的价格和评分。

notes:
  - 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等
  - 根据内容会自动调整大小以适应场景
"""

import os
import re
import unicodedata
import uuid

# 移除不再需要的导入
from typing import TYPE_CHECKING, Any, Optional

import requests
from loguru import logger
from manim import *

from tools.theme_manager import theme

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

# 导入新的text_style_system
from dsl.v2.utils.text_style_system import (
    apply_style_animations_to_text,
    create_styled_text,
    find_styled_text_in_mobject,
    apply_text_animations,
)
from utils.format import wrap_text
from utils.md_to_pango import MarkdownToSimplifiedConverter
from utils.title_utils import create_title

converter = MarkdownToSimplifiedConverter()

# Emoji related constants
EMOJI_DIR = "emoji_cache"

ref_character = "中"

# ===== 简化的内联元素处理系统 =====


def parse_text_with_inline_elements(text: str) -> list[tuple[str, str]]:
    """
    简化的文本解析，处理emoji和数学公式
    返回: [(element_type, content), ...]
    element_type可以是: 'text', 'emoji', 'inlinemath'
    """

    result = []
    i = 0

    # 数学公式正则表达式
    INLINE_MATH_PATTERN = r"\$(?!\s)(?P<math_text>.+?)(?!\s)\$"

    while i < len(text):
        # 检查数学公式
        remaining_text = text[i:]
        math_match = re.match(INLINE_MATH_PATTERN, remaining_text)

        if math_match:
            # 找到数学公式
            math_content = math_match.group("math_text")
            result.append(("inlinemath", math_content))
            i += math_match.end()
            continue

        # 检查emoji
        if is_emoji(text[i]):
            # 提取emoji（处理复合emoji）
            j = i + 1
            current_emoji = text[i]

            # 尝试扩展emoji检测（复合emoji字符）
            while j < len(text) and (
                ord(text[j]) in [0xFE0F, 0xFE0E]  # 变体选择器
                or 0x1F3FB <= ord(text[j]) <= 0x1F3FF  # 肤色修饰符
            ):
                current_emoji += text[j]
                j += 1

            result.append(("emoji", current_emoji))
            i = j
            continue

        # 普通文本
        text_start = i
        while i < len(text):
            # 检查是否遇到数学公式或emoji
            if text[i] == "$" and i + 1 < len(text) and not text[i + 1].isspace():
                break
            if is_emoji(text[i]):
                break
            i += 1

        if i > text_start:
            result.append(("text", text[text_start:i]))

    return result


def create_mobject_from_elements(
    elements: list[tuple[str, str]], font_size: float, font_color: str | ManimColor
) -> Mobject:
    """从解析的元素列表创建Mobject"""
    font = DEFAULT_FONT
    ref_text = Text(
        ref_character,
        font=font,
        font_size=font_size,
    )
    base_height = ref_text.height

    group = Group()

    for element_type, content in elements:
        if element_type == "text":
            if content.strip():
                group.add(Text(content, font=font, color=font_color, font_size=font_size))
        elif element_type == "inlinemath":
            # 处理内联数学公式
            try:
                math_mob = MathTex(content, font_size=font_size * 0.8, color=font_color)
                if math_mob.height > 0:
                    math_mob.height = base_height * 0.9
                group.add(math_mob)
            except Exception as e:
                logger.error(f"创建内联数学公式失败: {e}")
                group.add(Text(f"${content}$", font=font, font_size=font_size, color=font_color))
        elif element_type == "emoji":
            # 处理emoji
            emoji_mob = create_emoji_mobject(content)
            if emoji_mob:
                emoji_mob.height = base_height * 1.0
                group.add(emoji_mob)

    if len(group) > 0:
        group.arrange(RIGHT, buff=0.05)
        return group
    else:
        return Text("", font=font, color=font_color, font_size=font_size)


# === 新增：第二级解析系统 ===


def parse_text_styles(markdown_text: str) -> tuple[str, dict]:
    """
    第二级解析：从markdown文本中提取纯文本和样式信息

    Args:
        markdown_text: 原始markdown文本，如 "A line including `inline code` and **bold text**"

    Returns:
        tuple: (clean_text, styles)
        - clean_text: 纯文本，如 "A line including inline code and bold text"
        - styles: 样式信息字典，如 {"bold": ["bold text"], "code": ["inline code"]}
    """

    clean_text = markdown_text
    styles = {"bold": [], "italic": [], "code": [], "strikethrough": [], "link": [], "math": []}

    # 处理内联数学公式 $...$，使用与mistune对齐的正则表达式
    INLINE_MATH_PATTERN = r"\$(?!\s)(?P<math_text>.+?)(?!\s)\$"
    for match in re.finditer(INLINE_MATH_PATTERN, clean_text):
        math_content = match.group("math_text")
        styles["math"].append(math_content)
    clean_text = re.sub(INLINE_MATH_PATTERN, lambda m: m.group("math_text"), clean_text)

    # 处理粗体 **text**
    bold_pattern = r"\*\*([^*]+)\*\*"
    for match in re.finditer(bold_pattern, clean_text):
        bold_content = match.group(1)
        styles["bold"].append(bold_content)
    clean_text = re.sub(bold_pattern, r"\1", clean_text)

    # 处理斜体 *text*
    italic_pattern = r"\*([^*]+)\*"
    for match in re.finditer(italic_pattern, clean_text):
        italic_content = match.group(1)
        styles["italic"].append(italic_content)
    clean_text = re.sub(italic_pattern, r"\1", clean_text)

    # 处理行内代码 `code`
    code_pattern = r"`([^`]+)`"
    for match in re.finditer(code_pattern, clean_text):
        code_content = match.group(1)
        styles["code"].append(code_content)
    clean_text = re.sub(code_pattern, r"\1", clean_text)

    # 处理删除线 ~~text~~
    strike_pattern = r"~~([^~]+)~~"
    for match in re.finditer(strike_pattern, clean_text):
        strike_content = match.group(1)
        styles["strikethrough"].append(strike_content)
    clean_text = re.sub(strike_pattern, r"\1", clean_text)

    # 处理链接 [text](url)
    link_pattern = r"\[([^\]]+)\]\([^)]+\)"
    for match in re.finditer(link_pattern, clean_text):
        link_content = match.group(1)
        styles["link"].append(link_content)
    clean_text = re.sub(link_pattern, r"\1", clean_text)

    # 过滤掉空的样式列表
    styles = {k: v for k, v in styles.items() if v}

    return clean_text, styles


# === 新增：特殊格式文本动画系统 ===


def find_text_positions(text_obj: Text, substring: str) -> list[tuple[int, int]]:
    """
    使用Text对象的_find_indexes方法查找子字符串的位置

    Args:
        text_obj: Manim Text对象
        substring: 要查找的子字符串

    Returns:
        位置列表: [(start, end), ...]
    """
    try:
        # 使用Text对象的内部方法_find_indexes，需要传入word和text两个参数
        return list(text_obj._find_indexes(substring, text_obj.text))
    except Exception as e:
        logger.error(f"查找文本位置失败: {e}")
        return []


def find_all_text_objects_in_mobject(mobject: Mobject) -> list[Text]:
    """
    递归查找Mobject中的所有Text对象

    Args:
        mobject: 要搜索的Mobject

    Returns:
        找到的所有Text对象列表
    """
    text_objects = []

    # 如果当前对象是Text对象
    if isinstance(mobject, Text):
        text_objects.append(mobject)

    # 如果当前对象有submobjects，递归搜索
    if hasattr(mobject, "submobjects") and mobject.submobjects:
        for submobject in mobject.submobjects:
            text_objects.extend(find_all_text_objects_in_mobject(submobject))

    return text_objects


def apply_bold_animation(
    scene: "FeynmanScene", text_obj: Text, target_text: str, duration: float, original_target_text: str = None
):
    """应用粗体动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 创建粗体版本的文本
                bold_text = Text(
                    original_target_text or target_text,
                    font=text_obj.font,
                    font_size=text_obj.font_size,
                    weight=BOLD,
                    color=RED,  # 粗体用红色突出显示
                ).move_to(char_group.get_center())

                scene.play(Transform(char_group, bold_text), run_time=duration)
        except Exception as e:
            logger.error(f"应用粗体动画失败: {e}")


def apply_italic_animation(scene: "FeynmanScene", text_obj: Text, target_text: str, duration: float):
    """应用斜体动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 使用Underline动画突出显示斜体
                underline = Underline(
                    char_group,
                    color=YELLOW,
                )
                scene.play(Create(underline), run_time=duration)
                return underline
        except Exception as e:
            logger.error(f"应用斜体动画失败: {e}")


def apply_code_animation(
    scene: "FeynmanScene", text_obj: Text, target_text: str, duration: float, original_target_text: str = None
):
    """应用代码样式动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 代码文本变为绿色并添加背景
                code_text = (
                    Code(
                        code_string=original_target_text or target_text,
                        add_line_numbers=False,
                    )
                    .set_width(char_group.get_width() * 0.9)
                    .move_to(char_group.get_center())
                )
                scene.play(Transform(char_group, code_text), run_time=duration)
        except Exception as e:
            logger.error(f"应用代码动画失败: {e}")


def apply_strikethrough_animation(scene: "FeynmanScene", text_obj: Text, target_text: str, duration: float):
    """应用删除线动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 删除线效果：文本变灰并添加划线
                # 添加划线
                line = Line(char_group.get_left(), char_group.get_right(), color=RED, stroke_width=4).move_to(
                    char_group.get_center()
                )
                scene.play(char_group.animate.set_color(GRAY), Create(line), run_time=duration * 0.5)
                return line
        except Exception as e:
            logger.error(f"应用删除线动画失败: {e}")


def apply_link_animation(scene: "FeynmanScene", text_obj: Text, target_text: str, duration: float):
    """应用链接动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 链接效果：变蓝色并添加下划线
                # 添加下划线
                url = (
                    Text(target_text, color=BLUE, font="Maple Mono NF CN")
                    .scale_to_fit_width(char_group.width)
                    .move_to(char_group.get_center())
                )
                underline = Underline(url, color=BLUE, stroke_width=2, buff=0.05)
                scene.play(Transform(char_group, url), Create(underline), run_time=duration * 0.5)
                return underline
        except Exception as e:
            logger.error(f"应用链接动画失败: {e}")


def apply_math_animation(
    scene: "FeynmanScene", text_obj: Text, target_text: str, duration: float, original_target_text: str = None
):
    """应用数学公式动画效果：将文本替换为MathTex"""
    from manim import MathTex, Transform

    # 使用原始文本进行查找，因为它可能包含空格
    search_text = original_target_text if original_target_text else target_text
    positions = find_text_positions(text_obj, search_text.replace(" ", "").replace("\n", ""))

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 创建MathTex对象
                math_tex = MathTex(search_text, color=theme.colors.text)  # 使用原始文本创建MathTex，防止空格被删除

                # 将MathTex移动到原文本的位置
                math_tex.move_to(char_group.get_center())

                # 调整大小以匹配原文本
                if char_group.height > 0:
                    scale_factor = char_group.height * 0.9 / math_tex.height
                    math_tex.scale(scale_factor)

                # 使用Transform动画将文本替换为数学公式
                scene.play(Transform(char_group, math_tex), run_time=duration * 0.5)

                logger.info(f"成功将文本 '{search_text}' 转换为数学公式")
                return math_tex
        except Exception as e:
            logger.error(f"应用数学公式动画失败: {e}")
            # 如果MathTex创建失败，至少改变颜色来表示这是数学公式
            try:
                char_group = text_obj.chars[char_start:char_end]
                if char_group:
                    scene.play(char_group.animate.set_color("#FFD700"), run_time=duration * 0.3)  # 金色
            except Exception as fallback_e:
                logger.error(f"数学公式fallback动画也失败: {fallback_e}")


def is_emoji(character: str) -> bool:
    """
    检查一个字符是否是emoji

    Args:
        character: 要检查的字符

    Returns:
        是否为emoji字符
    """
    if not character:
        return False

    # 检查单字符emoji
    if len(character) == 1:
        try:
            # 使用unicodedata获取字符类别
            category = unicodedata.category(character)
            # 检查是否是Emoji类别 (So-Symbol Other)
            if category == "So":
                return True

            # 检查是否在emoji Unicode范围内
            cp = ord(character)
            # 基本Emoji区域
            if 0x1F000 <= cp <= 0x1FFFF:
                return True
            # 补充符号区域中的emoji
            if 0x2600 <= cp <= 0x27BF:
                return True
        except Exception:
            return False

    # 多字符表情的检测（如带变体选择器的表情）
    if len(character) > 1:
        # 检查是否包含变体选择器 (VS15, VS16)
        for c in character:
            cp = ord(c)
            if cp in [0xFE0F, 0xFE0E]:  # 变体选择器
                return True

    return False


def download_emoji_image(emoji: str) -> Optional[str]:
    """
    下载高清emoji图片（PNG格式）

    Args:
        emoji: 要下载的emoji字符

    Returns:
        保存的文件路径，如果下载失败则返回None
    """
    if not os.path.exists(EMOJI_DIR):
        os.makedirs(EMOJI_DIR)

    # 生成emoji编码
    if len(emoji) == 0:
        return None

    emoji_code = hex(ord(emoji[0]))[2:].lower()
    emoji_name = f"emoji_{emoji_code}"
    filepath = os.path.join(EMOJI_DIR, f"{emoji_name}.svg")

    if os.path.exists(filepath):
        return filepath

    try:
        # 使用72x72高清版本（这是Twitter Twemoji的标准高质量版本）
        url = f"https://raw.githubusercontent.com/twitter/twemoji/master/assets/svg/{emoji_code}.svg"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            with open(filepath, "wb") as f:
                f.write(response.content)
            return filepath
    except Exception as e:
        logger.error(f"下载emoji失败: {e}")

    return None


def create_emoji_mobject(emoji: str) -> Optional[SVGMobject]:
    """
    创建高清emoji的Mobject（使用72x72高质量PNG）

    Args:
        emoji: 要创建的emoji字符

    Returns:
        emoji图片对象，如果创建失败则返回None
    """
    try:
        image_path = download_emoji_image(emoji)

        if image_path and os.path.exists(image_path):
            emoji_mob = SVGMobject(image_path)
            return emoji_mob
    except Exception as e:
        logger.error(f"无法加载emoji图片: {e}")

    return None


# 使用主题系统获取样式值
DEFAULT_TEXT_COLOR = theme.colors.text
BLOCKQUOTE_COLOR = GREY_C
BLOCKQUOTE_PREFIX = "> "
DEFAULT_FONT_SIZE = theme.typography.sizes.body
CODE_FONT_SIZE = theme.typography.sizes.code
LINE_SPACING = 1.2
ITEM_SPACING = 0.5
DEFAULT_FONT = theme.typography.fonts.body
DEFAULT_TEXT_WRAP_WIDTH = 60
DEFAULT_ANIMATION = "fadeIn"  # 整体动画效果
DEFAULT_ITEM_ANIMATION = "fadeIn"  # 当使用LaggedStart/Succession时的单项动画
DEFAULT_LAG_RATIO = 0.3

# === 简化的文本创建函数 ===


def create_text_with_emoji_unified(
    content: str,
    font_size: float,
    font_color: str | ManimColor = theme.colors.text,
    emoji_scale: float = 1.0,
) -> Mobject:
    """
    统一的文本和emoji创建函数

    Args:
        content: 文本内容
        font_size: 字体大小
        emoji_scale: emoji缩放比例

    Returns:
        包含emoji和文本的Mobject
    """
    # 使用简化的内联元素解析
    elements = parse_text_with_inline_elements(content)
    logger.info(f"Parsed elements: {elements}")

    # 创建Mobject
    return create_mobject_from_elements(elements, font_size, font_color)


# --- Helper Function for Mobject Creation ---
def _create_markdown_mobjects(parsed_markdown: list[dict[str, Any]], title: str = None) -> Group:
    """
    Creates all Manim mobjects from parsed markdown elements,
    arranges them, and wraps them in a background rectangle.
    """
    all_content_mobjects = Group()
    if title:
        all_content_mobjects.add(
            Text(title, font=DEFAULT_FONT, color=DEFAULT_TEXT_COLOR, font_size=theme.typography.sizes.h1)
        )

    for element in parsed_markdown:
        element_type = element.get("type")
        content = element.get("content")
        attrs = element.get("attrs", {})
        mobject = None

        if element_type == "text":
            font_size = DEFAULT_FONT_SIZE

            # 使用新的text_style_system处理文本
            # 检查是否包含markdown格式
            if any(marker in content for marker in ["**", "*", "`", "~~", "[", "$"]):
                # 包含markdown格式，使用新的样式系统
                try:
                    # 先解析样式信息
                    clean_text, styles = parse_text_styles(content)

                    # 处理文本换行
                    clean_text = "\n".join(wrap_text(clean_text, max_length=DEFAULT_TEXT_WRAP_WIDTH))

                    # 创建带样式的Text对象
                    mobject = create_styled_text(
                        markdown_text=content, font_size=font_size, font_color=DEFAULT_TEXT_COLOR
                    )

                    # 如果需要换行，更新Text对象的文本
                    if "\n" in clean_text and hasattr(mobject, "text") and mobject.text != clean_text:
                        # 重新创建带换行的文本对象
                        wrapped_content = content
                        # 简单的换行处理，保持markdown格式
                        if len(content) > DEFAULT_TEXT_WRAP_WIDTH:
                            # 这里可以添加更智能的换行逻辑
                            pass
                        mobject = create_styled_text(
                            markdown_text=wrapped_content, font_size=font_size, font_color=DEFAULT_TEXT_COLOR
                        )

                except Exception as e:
                    logger.warning(f"使用新样式系统失败，回退到原有方式: {e}")
                    # 回退到原有的处理方式
                    clean_text, styles = parse_text_styles(content)
                    clean_text = "\n".join(wrap_text(clean_text, max_length=DEFAULT_TEXT_WRAP_WIDTH))
                    mobject = create_text_with_emoji_unified(clean_text, font_size, DEFAULT_TEXT_COLOR)

                    if styles:
                        text_objects = find_all_text_objects_in_mobject(mobject)
                        for text_obj in text_objects:
                            if text_obj.text.replace("\n", "").replace(" ", "") in clean_text.replace("\n", "").replace(
                                " ", ""
                            ):
                                text_obj.text_styles = styles
                                text_obj.original_clean_text = clean_text
            else:
                # 没有markdown格式，使用原有的emoji处理
                clean_text = "\n".join(wrap_text(content, max_length=DEFAULT_TEXT_WRAP_WIDTH))
                mobject = create_text_with_emoji_unified(clean_text, font_size, DEFAULT_TEXT_COLOR)

        elif element_type == "heading":
            wrapped_content = "\n".join(wrap_text(content, max_length=DEFAULT_TEXT_WRAP_WIDTH))
            level = attrs.get("level", 1)
            # Get font size from theme and determine text_type based on level
            if level == 1:
                font_size = theme.typography.sizes.h1
                text_color = theme.colors.h1
            elif level == 2:
                font_size = theme.typography.sizes.h2
                text_color = theme.colors.h2
            elif level == 3:
                font_size = theme.typography.sizes.h3
                text_color = theme.colors.h3
            elif level == 4:
                font_size = theme.typography.sizes.h4
                text_color = DEFAULT_TEXT_COLOR
            else:
                font_size = theme.typography.sizes.h4
                text_color = DEFAULT_TEXT_COLOR

            heading_mob = create_text_with_emoji_unified(wrapped_content, font_size, text_color)
            mobject = heading_mob

        elif element_type == "horizontal_rule":
            hr_opts = {"color": GREY_C, "stroke_width": 3}
            mobject = Line(LEFT, RIGHT, **hr_opts)
            # HR needs to be scaled later based on container width

        elif element_type == "blockquote":
            formatted_content = "\n".join([BLOCKQUOTE_PREFIX + line for line in content.rstrip().split("\n")])
            wrapped_content = "\n".join(wrap_text(formatted_content, max_length=DEFAULT_TEXT_WRAP_WIDTH))
            font_size = DEFAULT_FONT_SIZE
            mobject = create_text_with_emoji_unified(wrapped_content, font_size, BLOCKQUOTE_COLOR)

        elif element_type == "list":  # Handles both ordered and unordered from parser
            font_size = DEFAULT_FONT_SIZE
            list_group = Group()

            if isinstance(content, list) and content and isinstance(content[0], dict):
                # Handle structured list data from new markdown parser
                max_item_width = 0
                for item_data in content:
                    if isinstance(item_data, dict):
                        level = item_data.get("level", 1)
                        raw_text = item_data.get("text", "")
                        marker = item_data.get("marker", "•")

                        # 使用新的text_style_system处理列表项文本
                        if any(marker in raw_text for marker in ["**", "*", "`", "~~", "[", "$"]):
                            # 包含markdown格式，使用新的样式系统
                            try:
                                # 先解析样式信息
                                clean_text, styles = parse_text_styles(raw_text)

                                # 构建完整的列表项文本（包含缩进和标记）
                                item_indent_str = "  " * (level - 1)
                                full_item_text = f"{item_indent_str}{marker} {raw_text}"  # 保持原始markdown格式

                                # Replace leading spaces with non-breaking spaces
                                leading_spaces = len(full_item_text) - len(full_item_text.lstrip(" "))
                                non_breaking_spaces = "\u00A0" * leading_spaces
                                processed_item_text = non_breaking_spaces + full_item_text.lstrip(" ")

                                # 创建带样式的列表项
                                list_item_mobject = create_styled_text(
                                    markdown_text=processed_item_text,
                                    font_size=font_size,
                                    font_color=DEFAULT_TEXT_COLOR,
                                )

                            except Exception as e:
                                logger.warning(f"列表项样式处理失败，回退到原有方式: {e}")
                                # 回退到原有的处理方式
                                clean_text, styles = parse_text_styles(raw_text)
                                item_indent_str = "  " * (level - 1)
                                full_item_text = f"{item_indent_str}{marker} {clean_text}"
                                leading_spaces = len(full_item_text) - len(full_item_text.lstrip(" "))
                                non_breaking_spaces = "\u00A0" * leading_spaces
                                processed_item_text = non_breaking_spaces + full_item_text.lstrip(" ")

                                list_item_mobject = create_text_with_emoji_unified(
                                    processed_item_text, font_size, DEFAULT_TEXT_COLOR
                                )

                                if styles:
                                    text_objects = find_all_text_objects_in_mobject(list_item_mobject)
                                    for text_obj in text_objects:
                                        if clean_text.replace("\n", "").replace(" ", "") in text_obj.text.replace(
                                            "\n", ""
                                        ).replace(" ", ""):
                                            text_obj.text_styles = styles
                                            text_obj.original_clean_text = clean_text
                        else:
                            # 没有markdown格式，使用原有的emoji处理
                            clean_text = raw_text
                            item_indent_str = "  " * (level - 1)
                            full_item_text = f"{item_indent_str}{marker} {clean_text}"
                            leading_spaces = len(full_item_text) - len(full_item_text.lstrip(" "))
                            non_breaking_spaces = "\u00A0" * leading_spaces
                            processed_item_text = non_breaking_spaces + full_item_text.lstrip(" ")

                            list_item_mobject = create_text_with_emoji_unified(
                                processed_item_text, font_size, DEFAULT_TEXT_COLOR
                            )

                        list_group.add(list_item_mobject)
                        max_item_width = max(max_item_width, list_item_mobject.width)
                    else:
                        # Fallback for unexpected data type
                        fallback_text = str(item_data)
                        list_item_mobject = create_text_with_emoji_unified(fallback_text, font_size, DEFAULT_TEXT_COLOR)
                        list_group.add(list_item_mobject)
                        max_item_width = max(max_item_width, list_item_mobject.width)
            else:
                # Fallback for old string format
                list_items_str = content if isinstance(content, list) else content.strip().split("\n")
                max_item_width = 0
                for item_text in list_items_str:
                    list_item_mobject = create_text_with_emoji_unified(item_text, font_size, DEFAULT_TEXT_COLOR)
                    list_group.add(list_item_mobject)
                    max_item_width = max(max_item_width, list_item_mobject.width)

            mobject = Group()
            if len(list_group) > 0:
                # 为列表项创建背景，使用主题系统样式
                max_item_width = max(item_mobject.width for item_mobject in list_group.submobjects)

                # 获取列表项样式
                corner_radius = 0.2
                bg_color = theme.colors.background
                bg_opacity = theme.components.list.background_opacity
                stroke_color = DEFAULT_TEXT_COLOR
                list_padding = theme.components.list.padding

                for list_item_mobject in list_group.submobjects:
                    item_group = Group()
                    item_group.add(list_item_mobject)

                    item_bg_rect = RoundedRectangle(
                        width=max_item_width + 1,
                        height=list_item_mobject.height + list_padding,
                        color=bg_color,
                        fill_opacity=bg_opacity,
                        stroke_width=2,
                        stroke_color=stroke_color,
                        corner_radius=corner_radius,
                    )
                    item_bg_rect.move_to(list_item_mobject.get_center()).align_to(list_item_mobject, LEFT)
                    list_item_mobject.shift(RIGHT * 0.2)  # 稍微缩进文本
                    item_group.add_to_back(item_bg_rect)
                    mobject.add(item_group)

                mobject.arrange(DOWN)

        elif element_type == "code_block":
            lang = attrs.get("lang", "python")
            code_content = content

            # 使用主题系统获取代码块样式
            code_font = theme.typography.fonts.code

            code_creation_opts = {
                "language": lang,
                "formatter_style": "monokai",
                "paragraph_config": {"font": code_font},
            }
            try:
                mobject = Code(code_string=code_content.strip("\n"), **code_creation_opts)
            except Exception as e:
                logger.error(f"Error creating Code mobject for lang '{lang}': {e}")
                mobject = MarkupText(f"[Code block error: {lang}]", font=DEFAULT_FONT)

        elif element_type == "image":
            image_path = attrs.get("src", content)  # src from attrs preferred
            alt_text = attrs.get("alt", "Image")
            try:
                mobject = ImageMobject(image_path)
            except Exception as e:
                logger.error(f"无法加载图片 '{image_path}': {e}")
                mobject = MarkupText(f"[图片加载失败: {alt_text or image_path}]", font=DEFAULT_FONT)

        elif element_type == "block_math":
            # 处理块级数学公式
            math_content = content
            try:
                # 使用MathTex创建块级数学公式
                math_font_size = theme.typography.sizes.h3  # 使用较大的字体
                mobject = MathTex(math_content, font_size=math_font_size, color=DEFAULT_TEXT_COLOR)
            except Exception as e:
                logger.error(f"创建块级数学公式失败: {e}")
                # 降级为普通文本显示
                mobject = MarkupText(f"$${math_content}$$", font=theme.typography.fonts.code, color=DEFAULT_TEXT_COLOR)

        elif element_type == "table":
            # Assuming content is List[List[str]] for table data
            # And attrs might contain header info: attrs.get("header", [])
            table_data = content
            if table_data and isinstance(table_data, list) and all(isinstance(row, list) for row in table_data):
                str_table_data = [[str(cell) for cell in row] for row in table_data]
                # 使用主题系统获取表格样式
                table_line_width = theme.components.table.line_width
                table_cell_padding = theme.components.table.cell_padding
                table_row_padding = theme.components.table.row_padding
                table_font = theme.typography.fonts.code
                table_color = DEFAULT_TEXT_COLOR

                table_opts = {
                    "include_outer_lines": True,
                    "line_config": {"stroke_width": table_line_width, "stroke_color": table_color},
                    "h_buff": table_cell_padding,
                    "v_buff": table_row_padding,
                    "element_to_mobject": MarkupText,
                    "element_to_mobject_config": {"font": table_font, "color": table_color},
                }

                # Check for headers from attrs
                header_row_data = attrs.get("header")
                if header_row_data and isinstance(header_row_data, list):
                    table_opts["col_labels"] = [
                        MarkupText(str(h), font=table_font, color=table_color, font_size=DEFAULT_FONT_SIZE).set_style(
                            BOLD
                        )
                        for h in header_row_data
                    ]

                mobject = Table(str_table_data, **table_opts)
            else:
                logger.error(f"表格数据格式不正确: {table_data}")
                mobject = MarkupText("[表格数据错误]")

        else:  # Fallback for unknown or unhandled types
            logger.warning(f"未知的 Markdown 元素类型: '{element_type}' 内容: '{str(content)[:50]}...'")
            mobject = MarkupText(f"[未知类型: {element_type}")

        if mobject:
            if isinstance(mobject, Line):  # Special handling for HR width
                # Will be scaled relative to container later
                pass
            logger.info(f"Markdown元素创建完成: {element_type} {mobject}")
            all_content_mobjects.add(mobject)

    if len(all_content_mobjects) == 0:
        logger.info("没有可显示的 Markdown 内容 (mobject list empty after parsing).")
        return Group()  # Return empty VGroup, placeholder handled in main function

    all_content_mobjects.arrange(DOWN, buff=ITEM_SPACING, aligned_edge=LEFT)

    return all_content_mobjects


# --- Helper Function for List Focus Animation ---
def _animate_list_with_focus_effect(
    scene: "FeynmanScene",
    list_mobj: Group,
    anim_times: dict,
    lag_ratio: float,
    rate_func,
):
    """
    为列表项实现焦点高亮效果动画

    Args:
        scene: Feynman场景对象
        list_mobj: 列表的Group对象，包含所有列表项
        anim_times: 动画时间配置字典
        lag_ratio: 延迟比例
        rate_func: 速率函数
    """
    if not list_mobj.submobjects:
        return
    scene.play(LaggedStart(FadeIn(item, shift=RIGHT) for item in list_mobj), lag_ratio=lag_ratio)

    # 获取列表容器的原始宽度和中心位置
    # original_list_center = list_mobj.get_center()
    # list_width = list_mobj.width

    # # 存储每个列表项的原始位置和大小
    # original_states = []
    # for item in list_mobj:
    #     original_states.append(
    #         {
    #             "position": item.get_center().copy(),
    #             "scale": 1.0,  # 假设初始缩放为1
    #         }
    #     )

    # item_duration = ThemeUtils.get_component_style("list", "item_duration", 1.0)
    item_anim_time = anim_times["list_item_fade"]
    # 逐个处理每个列表项
    for item in list_mobj:
        # 检查列表项是否有样式信息，如果有则应用样式动画
        new_objs = apply_text_animations(
            scene=scene,
            mobject=item,
            duration=item_anim_time * 0.3,
        )
        if new_objs:
            item.add(*new_objs)

        # scene.play(Circumscribe(item, color=RED))

        # 4. 保持焦点状态
        # scene.wait(item_duration)

        # 第二阶段：恢复效果
        # 5. 恢复项目到原始位置和大小

        # 播放恢复动画
        # scene.play(
        #     item.animate.scale(1 / scale_factor).move_to(original_state["position"]),
        #     run_time=item_anim_time * 0.5,
        #     rate_func=rate_func,
        # )

        # # 在项目之间添加短暂延迟（基于lag_ratio）
        # if i < len(list_mobj) - 1:  # 不在最后一个项目后添加延迟
        #     scene.wait(max(0.01, lag_ratio * 0.5))


# --- Helper Function for Animation ---
def _animate_markdown_mobjects(
    scene: "FeynmanScene",
    display_group: Group,  # This is the group from _create_markdown_mobjects
    parsed_markdown: list[dict[str, Any]],
):
    """
    Animates the display_group (background and its content).
    """
    if not display_group or not display_group.submobjects:
        logger.warning("动画中止：display_group 为空。")
        return

    lag_ratio = DEFAULT_LAG_RATIO

    anim_times = {
        "fade_shift": 0.5,
        "create": 0.7,
        "write": 1.0,  # Default for Write if not by letter
        "letter_base": 0.03,
        "letter_min": 0.3,
        "list_item_fade": 1.0,
    }
    rate_func_name = "smooth"
    rate_func = smooth  # 默认使用smooth
    if rate_func_name == "ease_in_out_sine":
        rate_func = there_and_back
    elif rate_func_name == "ease_out":
        rate_func = rate_functions.ease_out_sine
    elif rate_func_name == "ease_in":
        rate_func = rate_functions.ease_in_sine

    for i, (mobj, item_type) in enumerate(zip(display_group, [x["type"] for x in parsed_markdown])):
        if item_type == "text":
            # 查找带样式信息的Text对象（可能在Group中）
            styled_text_obj = find_styled_text_in_mobject(mobj)
            scene.play(FadeIn(mobj, shift=RIGHT, rate_func=rate_func))

            if styled_text_obj:
                # 找到了带样式信息的Text对象
                # 然后应用样式动画
                new_obj = apply_style_animations_to_text(
                    scene=scene,
                    text_obj=styled_text_obj,
                    styles=styled_text_obj.text_styles,
                    base_animation_duration=anim_times["fade_shift"],
                )
                if new_obj:
                    mobj.add(*new_obj)
            elif isinstance(mobj, Text):
                # 普通Text对象
                current_run_time = min(3.0, max(anim_times["letter_min"], len(mobj.text) * anim_times["letter_base"]))
                scene.play(AddTextLetterByLetter(mobj, run_time=current_run_time))
            elif isinstance(mobj, MarkupText):
                # 原有的MarkupText处理逻辑
                current_run_time = min(3.0, max(anim_times["letter_min"], len(mobj.text) * anim_times["letter_base"]))
                scene.play(AddTextLetterByLetter(mobj, run_time=current_run_time))
            # else:
            # 其他文本对象（可能是Group等）
            # scene.play(FadeIn(mobj, shift=RIGHT, rate_func=rate_func))

        elif item_type == "list" and isinstance(mobj, (Group, VGroup)) and mobj.submobjects:  # Likely a list
            # 使用主题系统设置列表项动画 - 实现焦点高亮效果
            _animate_list_with_focus_effect(scene, mobj, anim_times, lag_ratio, rate_func)
        else:  # Generic mobjects (Code, Image, Table, Line)
            scene.play(FadeIn(mobj, shift=RIGHT, rate_func=rate_func))


def set_ref_character():
    global ref_character
    candidate_text = (
        "为了避免多进程运行时发生冲突所以从这句话里随机选择一个字作为基准高度永我国中心飞马龙凤德道法天地玄黄宇宙洪"
    )
    ref_character = candidate_text[os.getpid() % len(candidate_text)]


# --- Main Function ---
def animate_markdown(scene: "FeynmanScene", content: str, title: str, narration: str, id: str = None):
    mobject_id = id or f"markdown_{str(uuid.uuid4())[:8]}"

    set_ref_character()

    parsed_markdown = converter.convert(content)
    logger.info(parsed_markdown)

    if not parsed_markdown:
        return

    display_group = _create_markdown_mobjects(parsed_markdown, None)  # 不在内容中创建标题

    if not display_group or not display_group.submobjects:  # Check if _create_markdown_mobjects returned empty
        return

    target_rect = scene.full_screen_rect
    display_group.move_to(target_rect.get_center())

    scale_factor = min(
        target_rect.width * 0.9 / display_group.width, target_rect.height * 0.9 / display_group.height, 1.0
    )
    display_group.scale(scale_factor).align_to(target_rect, ORIGIN)
    # 使用主题系统准备动画参数
    # 日志记录使用的主题

    with scene.voiceover(text=narration) as tracker:  # noqa
        # Create title first
        initial_title, title_text = create_title(title, scene=scene)
        available_height = target_rect.height - title_text.height - 0.3
        scale_factor = min(available_height * 0.9 / display_group.height, 1.0)
        display_group.scale(scale_factor).next_to(title_text, DOWN, buff=0.3)

        # Handle clear_current_mobject for transition support
        scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)
        scene.add(title_text)
        _animate_markdown_mobjects(
            scene=scene,
            display_group=display_group,
            parsed_markdown=parsed_markdown,
        )

    # 创建显示组：initial_title用于转场，title_text用于显示，display_group为实际内容
    # 转场系统会使用submobjects[0]（initial_title）作为转场对象
    final_display_group = Group(initial_title, title_text, display_group)

    scene.current_mobj = final_display_group
    setattr(scene, mobject_id, final_display_group)  # HACK

    # 保存结束状态
    scene.save_scene_state("markdown", mobject_id)

    logger.info(f"Markdown content (id: {mobject_id}) display and animation complete in region 'full_screen'.")
