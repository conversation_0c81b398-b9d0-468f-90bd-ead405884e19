"""
effect: |
    创建专业美观的封面页面，采用BPE开篇动画风格，支持疑问句标题和副标题展示。
    使用黑底白字配色，橙色装饰线，专业优雅的入场动画效果。

use_cases:
    - 学术报告封面：展示研究主题和核心观点
    - 技术分享开场：突出技术问题和解决方案
    - 在线课程封面：显示课程主题和学习收益
    - 企业培训页面：体现专业性和内容重点
    - 社交媒体内容：吸引点击和分享

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 主标题，建议疑问句形式激发好奇心
    required: true
  subtitle:
    type: str
    desc: 副标题，可包含关键数据、结论、机构等信息
    default: None
  background_style:
    type: str
    desc: 背景样式（预留参数，当前统一黑色背景）
    default: gradient
  color_scheme:
    type: str
    desc: 配色方案（预留参数，当前统一BPE配色）
    default: blue
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  narration:
    type: str
    desc: 封面展示时播放的语音旁白文本
    required: true

dsl_examples:
    - type: animate_cover
      params:
        title: AI能否完全替代程序员工作？
        subtitle: 调查显示92%认同 • 创造力仍是人类优势
        narration: 探索人工智能与人类工作的未来关系。
    - type: animate_cover
      params:
        title: 量子计算何时商用化？
        subtitle: 速度快100万倍 • 算力瓶颈即将突破 • 中科院研究
        narration: 量子计算将彻底改变计算能力的边界。
    - type: animate_cover
      params:
        title: 0基础3个月能学会编程吗？
        subtitle: 学员87%成功率 • 高效学习路径已验证 • 字节跳动
        id: python_course_cover
        narration: 掌握科学的学习方法，编程入门不再困难。
    - type: animate_cover
      params:
        title: 深度学习真的那么神奇吗？
        subtitle: 准确率提升40% • 突破传统算法极限
        narration: 让我们揭开深度学习的神秘面纱。
    - type: animate_cover
      params:
        title: 区块链技术能解决信任问题吗？
        subtitle: 去中心化革命 • MIT实验室验证
        narration: 探讨区块链技术的实际应用价值。

notes:
    - 采用BPE开篇动画风格：黑底白字，橙色装饰线，专业优雅入场
    - 主标题建议疑问句，激发观众好奇心，提高点击率
    - 副标题统一输入，可包含：具体数据(92%、100万倍) + 核心结论 + 权威机构
    - 副标题使用 • 分隔不同信息，控制在合理长度内
    - BPE色彩配置：主标题#FFFFFF，副标题#94A3B8，装饰线#F18F01
    - 动画时序：Write主标题(2s) → Create装饰线+FadeIn副标题(1.5s) → Wait展示(2s)
    - 内容保持在场景中，退出由系统统一控制
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene
    from typing import Union

from loguru import logger
from manim import *

from tools.theme_manager import theme


def _get_theme_colors(scheme: str) -> dict[str, str]:
    """获取指定配色方案的颜色配置，完全基于theme系统"""
    # 根据不同方案选择theme中的颜色组合
    if scheme == "purple":
        return {
            "primary": theme.colors.cyclic_colors[4] if len(theme.colors.cyclic_colors) > 4 else theme.colors.primary,
            "secondary": theme.colors.secondary,
            "accent": theme.colors.accent,
            "background_start": theme.colors.background,
            "background_end": theme.colors.primary_dark,
            "text_primary": theme.colors.text,
            "text_secondary": theme.colors.primary_light,
            "glow": theme.colors.cyclic_colors[4] if len(theme.colors.cyclic_colors) > 4 else theme.colors.primary_light
        }
    elif scheme == "gold":
        return {
            "primary": theme.colors.accent,
            "secondary": theme.colors.accent_light,
            "accent": theme.colors.secondary,
            "background_start": theme.colors.background,
            "background_end": theme.colors.accent_dark,
            "text_primary": theme.colors.text,
            "text_secondary": theme.colors.accent_light,
            "glow": theme.colors.accent_light
        }
    elif scheme == "red":
        return {
            "primary": theme.colors.cyclic_colors[3] if len(theme.colors.cyclic_colors) > 3 else theme.colors.primary,
            "secondary": theme.colors.accent,
            "accent": theme.colors.secondary,
            "background_start": theme.colors.background,
            "background_end": theme.colors.cyclic_colors[3] if len(theme.colors.cyclic_colors) > 3 else theme.colors.primary_dark,
            "text_primary": theme.colors.text,
            "text_secondary": theme.colors.secondary_light,
            "glow": theme.colors.cyclic_colors[3] if len(theme.colors.cyclic_colors) > 3 else theme.colors.primary_light
        }
    else:  # default: blue
        return {
            "primary": theme.colors.primary,
            "secondary": theme.colors.secondary,
            "accent": theme.colors.accent,
            "background_start": theme.colors.background,
            "background_end": theme.colors.primary_dark,
            "text_primary": theme.colors.text,
            "text_secondary": theme.colors.primary_light,
            "glow": theme.colors.primary_light
        }








def animate_cover(
    scene: "FeynmanScene",
    title: str,
    subtitle: Optional[str] = None,
    background_style: str = "gradient",
    color_scheme: str = "blue",
    id: Optional[str] = None,
    narration: Optional[str] = None,
    cwd: str = None,
) -> None:
    """
    创建专业美观的封面页面，完全按照BPE开篇风格
    """
    # 1. 初始化
    if not getattr(scene, "scene_method_name", None):
        scene.scene_method_name = "animate_cover"
    
    reference_id = id if id else f"cover_{abs(hash(title)) % 10000}"
    logger.info(f"创建封面: style='{background_style}', scheme='{color_scheme}', id='{reference_id}'")
    
    # 2. 获取配色方案
    colors = _get_theme_colors(color_scheme)
    
    # 3. 清除当前对象
    scene.clear_current_mobj()
    
    # 4. 设置黑色背景（BPE风格）
    scene.camera.background_color = BLACK
    
    # 5. 创建主标题（调整字体大小）
    main_title = Text(
        title,
        font_size=48,  # 从72减小到48
        weight=BOLD,
        color="#FFFFFF"  # 使用BPE的白色文本
    )
    
    # 6. 创建副标题（直接使用传入的副标题）
    sub_title = Text(
        subtitle,
        font_size=24,
        color="#94A3B8"  # 使用BPE的次要文本颜色
    ) if subtitle else None
    
    # 7. 创建装饰线（BPE风格）
    decorative_line = Line(
        start=LEFT * 3,
        end=RIGHT * 3,
        color="#F18F01",  # 使用BPE的橙色装饰线
        stroke_width=4
    )
    
    # 8. 布局（BPE风格）
    if sub_title:
        title_group = VGroup(main_title, sub_title, decorative_line)
        title_group.arrange(DOWN, buff=0.5)
    else:
        title_group = VGroup(main_title, decorative_line)
        title_group.arrange(DOWN, buff=0.5)
    
    # 9. 完全按照BPE的动画序列
    with scene.voiceover(text=narration or "") as tracker:
        # 第一步：主标题优雅写入（2秒）
        scene.play(
            Write(main_title, run_time=2),
            rate_func=smooth
        )
        
        # 第二步：副标题和装饰线同时出现（1.5秒）
        animations = [Create(decorative_line, run_time=1.5)]
        if sub_title:
            animations.append(FadeIn(sub_title, shift=UP))
        
        scene.play(
            *animations,
            rate_func=smooth
        )
        
        # 停留展示（2秒）
        scene.wait(2)
    
    # 10. 保存场景状态
    scene.current_mobj = title_group  # 保持内容在场景中，由系统统一控制退出
    scene.save_scene_state(content_type="cover", mobject_id=reference_id)
    logger.info(f"封面创建完成: id='{reference_id}'") 