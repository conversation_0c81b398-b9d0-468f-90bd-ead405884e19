"""
effect: |
  在Manim场景中创建并播放一个分步骤讲解的动画。
  左侧展示步骤节点，每一步包含序号和操作描述，节点动态生成并向上移动。
  右侧展示每一步的具体内容（markdown格式），支持代码、列表、文本等。
  最后所有步骤节点缩放移动到画面正中，展示整体概念。

use_cases:
  - 教学演示中的分步骤讲解，如算法步骤、操作流程等
  - 产品功能介绍，逐步展示各个功能点
  - 项目开发流程演示，突出每个阶段的重点

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  steps:
    type: array
    desc: 步骤列表，每个元素是一个dict，包括 step_number, title, content, color（可选）, narration（可选） 字段
    required: true
    items:
      type: object
      properties:
        step_number: { type: string, desc: 步骤序号 }
        title: { type: string, desc: 步骤标题 }
        content: { type: string, desc: 步骤内容（markdown） }
        color: { type: string, desc: 节点颜色（可选） }
        narration: { type: string, desc: 步骤旁白（可选） }
        required: [step_number, title, content]
        additionalProperties: false
  intro_narration:
    type: str
    desc: 开场介绍语音旁白文本
    required: true
  outro_narration:
    type: str
    desc: 结尾总结语音旁白文本
    required: true
  title:
    type: str
    desc: 整体标题
    required: true
  subtitle:
    type: str
    desc: 副标题
    default: None
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_step_by_step
    params:
      steps:
        - step_number: "1"
          title: "初始化数据"
          content: |
            ## 创建数组
            ```python
            arr = [64, 34, 25, 12, 22, 11, 90]
            ```
            - 准备待排序的数组
            - 记录数组长度
          color: "#FF6B6B"
          narration: "首先我们初始化一个待排序的数组"
        - step_number: "2"
          title: "选择最小元素"
          content: |
            ## 查找最小值
            ```python
            min_idx = 0
            for i in range(1, len(arr)):
                if arr[i] < arr[min_idx]:
                    min_idx = i
            ```
            - 遍历未排序部分
            - 找到最小元素的索引
          color: "#4ECDC4"
          narration: "接下来在未排序部分找到最小的元素"
        - step_number: "3"
          title: "交换元素"
          content: |
            ## 元素交换
            ```python
            arr[0], arr[min_idx] = arr[min_idx], arr[0]
            ```
            - 将最小元素移到已排序部分的末尾
            - 扩大已排序区域
          color: "#45B7D1"
          narration: "然后将最小元素与第一个位置交换"
      title: "选择排序算法演示"
      subtitle: "逐步理解排序过程"
      intro_narration: "今天我们来学习选择排序算法的工作原理"
      outro_narration: "通过这三个步骤，我们完成了选择排序的一轮操作"

notes:
  - 步骤按数组中的顺序呈现，每个步骤的内容支持完整的markdown语法
  - 左侧节点会动态生成并向上移动，右侧内容会淡出后显示新内容
  - 可以为每个步骤指定颜色，或使用默认颜色方案
  - 最后所有步骤节点会缩放移动到画面中央，形成整体概览
  - 与timeline的差别是，step_by_step需要讲解每个步骤中的详细内容，因此更适合例子讲解等场景，而timeline只是展示事件的整体脉络
  - title, subtitle与step title需要精简，不能字数太多，否则会导致文字重叠等问题
"""
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any, Optional

from loguru import logger
from manim import *

from dsl.v2.animation_functions.animate_markdown import _animate_markdown_mobjects, _create_markdown_mobjects
from tools.theme_manager import theme
from utils.format import wrap_text
from utils.md_to_pango import MarkdownToSimplifiedConverter
from utils.title_utils import create_title

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene


@dataclass
class StepData:
    """步骤数据类"""

    step_number: str
    title: str
    content: str
    color: str
    narration: Optional[str] = None


@dataclass
class StepElement:
    """步骤节点元素类"""

    group: Group
    node: Circle
    number: Text
    title: Text
    connector: Optional[Line]
    color: str


def create_step_element(step_data: StepData, colors: dict[str, str], node_radius: float = 0.5) -> StepElement:
    """创建单个步骤节点元素

    Args:
        step_data: 步骤数据
        colors: 颜色配置字典
        node_radius: 节点半径

    Returns:
        创建好的步骤元素对象
    """
    # 如果步骤没有指定颜色，则使用默认颜色
    cyclic_colors = colors["cyclic_colors"]
    step_color = cyclic_colors[int(step_data.step_number) % len(cyclic_colors)]

    # 创建节点
    node = Circle(radius=node_radius, color=step_color)
    node.set_fill(step_color, opacity=1)
    stroke_width = 5
    stroke_color = theme.colors.background
    node.set_stroke(stroke_color, width=stroke_width)

    # 步骤序号
    number_font_size = theme.typography.sizes.h3
    number_font = theme.typography.fonts.body
    number_text_color = theme.colors.text
    number_text = Text(
        step_data.step_number, font_size=number_font_size, weight=BOLD, color=number_text_color, font=number_font
    )
    number_text.move_to(node.get_center())

    # 步骤标题
    title_str = step_data.title or ""
    title_font_size = theme.typography.sizes.body
    title_font = theme.typography.fonts.body
    title_color = colors["text_primary"]
    wrapped_title_str = wrap_text(title_str, 20)
    title_text = Text(
        "\n".join(wrapped_title_str),
        font_size=title_font_size,
        weight=BOLD,
        color=title_color,
        font=title_font,
    )

    # 布局
    title_spacing = 0.5
    title_text.next_to(node, RIGHT, buff=title_spacing)

    # 组合所有元素
    element_group = Group(node, number_text, title_text)

    return StepElement(
        group=element_group,
        node=node,
        number=number_text,
        title=title_text,
        connector=None,  # 连接线将在动画过程中创建
        color=str(step_color),
    )


def _normalize_steps(steps: list[StepData | dict[str, Any]]) -> list[StepData]:
    """将步骤数据标准化为StepData对象

    Args:
        steps: 原始步骤数据列表

    Returns:
        标准化后的StepData对象列表
    """
    normalized_steps: list[StepData] = []
    # 使用主题定义的步骤颜色列表
    default_colors = theme.get_cyclic_colors(len(steps))

    for i, step_data in enumerate(steps):
        if isinstance(step_data, dict):
            normalized_steps.append(
                StepData(
                    step_number=step_data.get("step_number", str(i + 1)),
                    title=step_data.get("title", ""),
                    content=step_data.get("content", ""),
                    color=step_data.get("color", "") or default_colors[i % len(default_colors)],
                    narration=step_data.get("narration"),
                )
            )
        elif isinstance(step_data, StepData):
            if step_data.color is None:
                step_data.color = default_colors[i % len(default_colors)]
            normalized_steps.append(step_data)
        else:
            logger.warning(f"跳过无效步骤数据: {step_data}")

    return normalized_steps


def _create_title_group(title: Optional[str], subtitle: Optional[str], text_color: str) -> Optional[VGroup]:
    """创建标题组

    Args:
        title: 主标题文本
        subtitle: 副标题文本
        text_color: 文本主色调

    Returns:
        标题组VGroup对象
    """
    if not title:
        return None

    title_font_size = theme.typography.sizes.h1
    title_font = theme.typography.fonts.body
    title_color = theme.colors.text_primary

    main_title = Text(
        "\n".join(wrap_text(title, 40)),
        font_size=title_font_size,
        weight=BOLD,
        color=title_color,
        font=title_font,
    )

    if subtitle:
        subtitle_font_size = theme.typography.sizes.body
        subtitle_font = theme.typography.fonts.body
        subtitle_color = theme.colors.text_secondary
        subtitle_text = Text(subtitle, font_size=subtitle_font_size, color=subtitle_color, font=subtitle_font)

        title_spacing = 0.3
        title_group = Group(main_title, subtitle_text)
        title_group.arrange(DOWN, buff=title_spacing)
    else:
        title_group = VGroup(main_title)

    title_edge_buff = 0.5
    title_group.to_edge(UP, buff=title_edge_buff)
    return title_group


def _create_content_mobjects(content: str) -> tuple[Group, list[dict[str, Any]]]:
    """创建步骤内容的Mobject

    Args:
        content: markdown格式的内容

    Returns:
        内容Mobject组
    """
    converter = MarkdownToSimplifiedConverter()
    parsed_markdown = converter.convert(content)

    if not parsed_markdown:
        logger.info(f"步骤内容为空: {content}")
        return Group()

    content_group = _create_markdown_mobjects(parsed_markdown)
    return content_group, parsed_markdown


def _animate_step_by_step_elements(
    scene: "FeynmanScene",
    step_elements: list[StepElement],
    normalized_steps: list[StepData],
    title_group: Optional[VGroup],
    intro_narration: Optional[str],
    outro_narration: Optional[str],
) -> Group:
    """创建并播放分步骤动画

    Args:
        scene: 场景对象
        step_elements: 步骤元素列表
        normalized_steps: 标准化的步骤数据
        title_group: 标题组对象（用于布局计算）
        intro_narration: 开场旁白文本（现在不使用，标题已在主函数处理）
        outro_narration: 结尾旁白文本

    Returns:
        包含所有元素的组合对象
    """
    displayed_steps: list[StepElement] = []
    current_content_group: Optional[Group] = None

    vertical_center = min(0, -(title_group.height - LARGE_BUFF) / 2) if title_group else 0

    def _play_step_animation():
        nonlocal current_content_group

        # 计算左侧步骤区域和右侧内容区域
        screen_width = config.frame_width
        screen_height = config.frame_height

        # 右侧区域：占屏幕宽度的70%
        right_area_width = screen_width * 0.7
        right_area_left_x = -screen_width / 2 + screen_width * 0.3

        # 步骤节点的垂直间距
        step_spacing = 1.5

        for i, (step_element, step_data) in enumerate(zip(step_elements, normalized_steps)):
            # 1. 移动已有步骤节点向上
            if i > 0:
                move_animations = []
                for displayed_step in displayed_steps:
                    move_animations.append(displayed_step.group.animate.shift(UP * step_spacing))
                    # 如果有连接线，也要移动连接线
                    if displayed_step.connector:
                        move_animations.append(displayed_step.connector.animate.shift(UP * step_spacing))

                if move_animations:
                    move_duration = 0.5
                    scene.play(*move_animations, run_time=move_duration)

            # 2. 新步骤节点出现在左侧中间位置
            step_center_y = 0  # 屏幕中央
            step_element.group.align_to([-config.frame_width / 2, step_center_y, 0], LEFT).shift(RIGHT * SMALL_BUFF)

            # 3. 创建连接线（如果不是第一个步骤）
            connector = None
            if i > 0 and displayed_steps:
                # 获取上一个步骤的节点
                previous_step = displayed_steps[-1]

                # 计算连接线的起点和终点
                start_pos = previous_step.node.get_bottom() + DOWN * 0.1
                end_pos = step_element.node.get_top() + UP * 0.1

                # 参考timeline的连接线样式
                connector_stroke_width = 4
                connector_color = ManimColor(step_element.color)  # 使用当前步骤的颜色

                connector = Line(
                    start_pos,
                    end_pos,
                    stroke_width=connector_stroke_width,
                    color=connector_color,
                )

                # 将连接线添加到当前步骤元素
                step_element.connector = connector

            # 步骤节点入场动画（包含连接线）
            node_animation_duration = 0.8
            animations = []
            if current_content_group:
                fadeout_duration = 0.3
                animations.append(FadeOut(current_content_group, run_time=fadeout_duration))
            # 如果有连接线，添加到动画列表
            if connector:
                animations.append(Create(connector))  # 连接线先创建
            animations += [
                GrowFromCenter(step_element.node),
                FadeIn(step_element.number, scale=0.8),
                Write(step_element.title),
            ]

            scene.play(
                LaggedStart(*animations, lag_ratio=0.3),
                run_time=node_animation_duration,
            )

            displayed_steps.append(step_element)

            # 4. 创建并显示新的内容
            content_group, parsed_markdown = _create_content_mobjects(step_data.content)
            if content_group and content_group.submobjects:
                # 定位到右侧区域

                # 缩放以适应右侧区域
                max_width = right_area_width * 0.9
                max_height = screen_height * 0.8
                scale_factor = min(
                    max_width / content_group.width if content_group.width > 0 else 1,
                    max_height / content_group.height if content_group.height > 0 else 1,
                    1.0,
                )
                content_group.scale(scale_factor).next_to(
                    title_group, DOWN, buff=MED_LARGE_BUFF, coor_mask=[0, 1, 0]
                ) if title_group else content_group.move_to([right_area_left_x, vertical_center, 0], aligned_edge=LEFT)
                content_group.scale(scale_factor).next_to(
                    title_group, DOWN, buff=MED_LARGE_BUFF, coor_mask=[0, 1, 0]
                ) if title_group else content_group.move_to([right_area_left_x, vertical_center, 0], aligned_edge=LEFT)

                # 对齐左边界
                content_group.move_to([right_area_left_x, vertical_center, 0], aligned_edge=LEFT)

                # 内容入场动画
                if step_data.narration:
                    with scene.voiceover(step_data.narration) as tracker:  # noqa
                        _animate_markdown_mobjects(scene, content_group, parsed_markdown)
                else:
                    _animate_markdown_mobjects(scene, content_group, parsed_markdown)

                current_content_group = content_group

            # 5. 等待一段时间
            step_wait_time = 0.5
            scene.wait(step_wait_time)

    _play_step_animation()

    def _play_final_animation():
        # 右侧内容淡出
        if current_content_group:
            fadeout_duration = 0.5
            scene.play(FadeOut(current_content_group), run_time=fadeout_duration)

        # 所有步骤节点移动到画面中央并缩放
        if displayed_steps:
            # 创建步骤节点组，包含连接线
            all_steps_group = Group()

            for step in displayed_steps:
                all_steps_group.add(step.group)
                if step.connector:
                    all_steps_group.add(step.connector)

            all_steps_final_dest = all_steps_group.copy()

            # 缩放和移动到中央
            final_animation_duration = 1.0
            final_scale = min(
                (title_group.get_bottom()[1] + config.frame_height / 2) * 0.8 / all_steps_final_dest.height
                if title_group
                else 1.0,
                1.0,
                (title_group.get_bottom()[1] + config.frame_height / 2) * 0.8 / all_steps_final_dest.height
                if title_group
                else 1.0,
                1.0,
            )
            # 缩放并水平居中
            all_steps_final_dest.scale(final_scale).set_x(0)

            # 垂直放置在标题下方，确保不重叠
            target_y = (
                title_group.get_bottom()[1] - MED_LARGE_BUFF - all_steps_final_dest.height / 2
                if title_group
                else vertical_center
            )
            target_y = (
                title_group.get_bottom()[1] - MED_LARGE_BUFF - all_steps_final_dest.height / 2
                if title_group
                else vertical_center
            )
            all_steps_final_dest.set_y(min(target_y, vertical_center))

            scene.play(Transform(all_steps_group, all_steps_final_dest), run_time=final_animation_duration)

            final_wait_time = 1.0
            scene.wait(final_wait_time)

        # 组合所有元素
        all_elements_group = Group()
        if displayed_steps:
            for step in displayed_steps:
                all_elements_group.add(step.group)
                if step.connector:
                    all_elements_group.add(step.connector)
        return all_elements_group

    try:
        if outro_narration:
            with scene.voiceover(outro_narration) as tracker:  # noqa
                return _play_final_animation()
        else:
            return _play_final_animation()
    except Exception as e:
        logger.warning(f"语音功能不可用，跳过outro_narration: {e}")
        return _play_final_animation()


def animate_step_by_step(
    scene: "FeynmanScene",
    title: str,
    steps: list[StepData | dict[str, Any]],
    intro_narration: Optional[str] = None,
    outro_narration: Optional[str] = None,
    subtitle: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """分步骤讲解动画函数

    Args:
        scene: Manim场景实例
        steps: 步骤列表
        intro_narration: 开场介绍语音旁白文本
        outro_narration: 结尾总结语音旁白文本
        title: 整体标题
        subtitle: 副标题
        id: 创建的Manim Mobject的唯一标识符
    """
    logger.info(f"Animating step-by-step with {len(steps)} steps in region 'full_screen'...")
    unique_id = id or f"step_by_step_{abs(hash(str(steps))) % 10000}"

    # 检查步骤是否为空
    if not steps:
        logger.warning("Steps list is empty. Nothing to show.")
        return None

    # 使用主题化的颜色配置
    colors = {
        "bg": theme.colors.step_background,
        "text_primary": theme.colors.text_primary,
        "text_secondary": theme.colors.text_secondary,
        "step_default": theme.colors.step_default_color,
        "cyclic_colors": theme.get_cyclic_colors(len(steps)),
    }

    # 设置参数 - 使用主题化的尺寸
    node_radius = 0.5

    # 标准化步骤数据为StepData对象
    normalized_steps = _normalize_steps(steps)
    if not normalized_steps:
        logger.warning("没有步骤数据可以动画展示.")
        return

    target_rect = scene.full_screen_rect
    if not target_rect:
        logger.error("目标区域 'full_screen' 未找到. 无法放置步骤动画.")
        return

    # 创建标题对象
    initial_title, title_text = create_title(title, scene=scene)

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

    # 创建标题组（用于内部布局计算）
    title_group = _create_title_group(title, subtitle, colors["text_primary"])

    # 在开始时显示标题动画
    if intro_narration:
        with scene.voiceover(intro_narration) as tracker:  # noqa
            # 直接添加标题，动画已在 create_title 中处理
            scene.add(title_text)
    else:
        # 直接添加标题，动画已在 create_title 中处理
        scene.add(title_text)

    # 创建所有步骤元素
    step_elements = []
    for step_data in normalized_steps:
        step_elements.append(create_step_element(step_data, colors, node_radius))

    # 缩放步骤元素，不超过 config.frame_width * 0.28
    max_width = config.frame_width * 0.28
    step_element_scale_factor = 1.0
    for step_element in step_elements:
        step_element_scale_factor = min(max_width / step_element.group.width, step_element_scale_factor)

    for step_element in step_elements:
        step_element.group.scale(step_element_scale_factor)

    # 动画展示步骤（不再处理标题）
    result_group = _animate_step_by_step_elements(
        scene,
        step_elements,
        normalized_steps,
        title_group,  # 传入title_group用于布局计算
        None,  # 不传入intro_narration，因为标题已经显示
        outro_narration,
    )

    # 创建显示组：initial_title用于转场，title_text用于显示，result_group为实际内容
    # 转场系统会使用submobjects[0]（initial_title）作为转场对象
    display_group = Group(initial_title, title_text, result_group)

    # 保存结果并更新当前场景中的对象
    scene.current_mobj = display_group
    scene.save_scene_state(content_type="step_by_step", mobject_id=unique_id)
    logger.info(f"Step-by-step '{unique_id}' animation complete.")

    return None
