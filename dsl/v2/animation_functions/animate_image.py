"""
effect: |
    在Manim场景中显示图像，支持动态入场动画和叠加注释文本。图片会从右下角斜着入场，然后顺正并进行左右移动放大。

use_cases:
    - 展示需要详细解释的图片或截图
    - 展示图表、图解或可视化内容
    - 显示产品或界面截图并添加叠加注释说明
    - 突出显示图片中的关键要点

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  image_path:
    type: str
    desc: 要显示的图片的本地文件路径
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  narration:
    type: str
    desc: 在图片显示时播放的语音旁白文本
    required: true
  annotation:
    type: str | list[str]
    desc: 作为注释叠加显示在图片上的文本
    default: None

dsl_examples:
    - type: animate_image
      params:
        title: 示例图片展示
        image_path: assets/manim_logo.png
        narration: 让我们看看这张图片。
        annotation: 这是一张示例图片，展示了重要的内容。
    - type: animate_image
      params:
        title: 系统架构图
        image_path: assets/manim_logo.png
        id: architecture_diagram
        annotation: [ "系统架构关键要点",
            "前端组件负责用户交互",
            "后端服务处理业务逻辑",
            "数据存储确保数据持久化"
        ]
        narration: 这张架构图展示了系统的主要组件和它们之间的关系。

notes:
    - 图片文件必须存在且路径正确，否则会抛出FileNotFoundError
    - 图片动画分为四个步骤：1）从右下角斜着入场 2）移动到中心并放大 3）旋转顺正 4）左右移动并进一步放大
    - 如果提供annotation，它会以发光文字形式叠加显示在图片上，有半透明黑色背景
    - annotation支持字符串或列表格式，列表中每个元素会作为一行显示
    - 注释文字使用sequential动画逐行显示，提供更好的视觉效果
"""

import os
import random
from typing import TYPE_CHECKING, Any, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

import numpy as np
from loguru import logger
from manim import *

from dsl.v2.animation_functions.animate_markdown import create_text_with_emoji_unified
from dsl.v2.utils.text_style_system import apply_text_animations, create_styled_text, has_styled_text_content
from tools.theme_manager import theme
from utils.title_utils import create_title

# ---- 辅助函数 ----


def _create_enhanced_text(
    text_content: str, font_size: int = 48, font_color: ManimColor = YELLOW_B, enable_markdown: bool = True
) -> Text:
    """
    创建增强的文本对象，支持markdown样式和emoji

    Args:
        text_content: 文本内容，可能包含markdown格式
        font_size: 字体大小
        font_color: 字体颜色
        enable_markdown: 是否启用markdown样式解析

    Returns:
        Text对象，如果启用markdown则包含样式信息
    """
    if enable_markdown and any(marker in text_content for marker in ["**", "*", "`", "~~", "[", "$"]):
        # 包含markdown格式标记，使用新的样式系统
        try:
            return create_styled_text(markdown_text=text_content, font_size=font_size, font_color=font_color)
        except Exception as e:
            logger.warning(f"创建样式文本失败，回退到普通文本: {e}")
            # 回退到原有的emoji处理
            return create_text_with_emoji_unified(text_content, font_size=font_size, font_color=font_color)
    else:
        # 没有markdown格式，使用原有的emoji处理
        result = create_text_with_emoji_unified(text_content, font_size=font_size, font_color=font_color)
        # 如果返回的是Group对象（包含emoji），需要特殊处理
        # 为了保持接口一致性，我们返回原始结果
        return result


def _apply_text_style_animations(
    scene: "FeynmanScene", text_group: Mobject, duration: float = 2
) -> Optional[list[Mobject]]:
    """
    对文本组应用样式动画

    Args:
        scene: Manim场景对象
        text_group: 包含文本的Group对象
        duration: 动画持续时间

    Returns:
        创建的新对象列表（如果有）
    """
    if not has_styled_text_content(text_group):
        return None

    try:
        return apply_text_animations(scene, text_group, duration=duration)
    except Exception as e:
        logger.error(f"应用文本样式动画失败: {e}")
        return None


def _determine_layout_type(obj: Mobject, target_rect: Rectangle) -> str:
    """
    根据图片的宽高比确定布局类型。

    Args:
        obj: 图片对象
        target_rect: 目标区域

    Returns:
        str: 布局类型 ('side_by_side', 'top_bottom', 'overlay')
    """
    # 获取图片的宽高比
    image_aspect_ratio = obj.width / obj.height if obj.height > 0 else 1.0

    # 判断图片相对于屏幕的占比
    width_ratio = obj.width / target_rect.width if target_rect.width > 0 else 1.0
    height_ratio = obj.height / target_rect.height if target_rect.height > 0 else 1.0

    # 正常宽度（占屏幕30%-70%）且高度较大：左右布局
    if 0.3 <= width_ratio <= 0.7 and height_ratio > 0.7:
        return "side_by_side"

    # 横向很长（宽高比>1.8）且竖向占比较小：上下布局
    elif image_aspect_ratio > 1.8 and height_ratio <= 0.65:
        return "top_bottom"

    # 其他情况：叠加布局
    else:
        return "overlay"


def _create_image_mobject(image_path: str, **kwargs) -> ImageMobject | SVGMobject:
    """
    创建图像 Manim 对象。

    Args:
        image_path: 图像文件路径
        **kwargs: 其他 ImageMobject 参数

    Returns:
        ImageMobject | SVGMobject: Manim ImageMobject 对象或 SVGMobject 对象
    """
    if not os.path.exists(image_path):
        logger.error(f"图像文件未找到: {image_path}")
        raise FileNotFoundError(f"图像文件未找到: {image_path}")

    return SVGMobject(image_path, **kwargs) if image_path.endswith(".svg") else ImageMobject(image_path, **kwargs)


def _calculate_pan_animation_params(obj: Mobject, target_rect: Rectangle) -> dict[str, Any]:
    """
    计算图像平移动画的参数。

    Args:
        obj: 要平移的对象
        target_rect: 目标区域的矩形

    Returns:
        Dict: 包含平移动画参数的字典
    """
    params = {}

    # 获取原始尺寸
    orig_width = obj.width
    orig_height = obj.height

    # 获取目标区域尺寸
    target_width = target_rect.width
    target_height = target_rect.height

    # 目标区域中心
    target_center = target_rect.get_center()

    # 检查宽高比
    image_aspect_ratio = orig_width / orig_height
    if image_aspect_ratio > 1.5:  # 宽图像 -> 向左平移
        target_height_scaled = target_height * 0.95  # 从0.9提高到0.95
        scale_factor = target_height_scaled / orig_height if orig_height > 0 else 1.0
        obj.scale(scale_factor)
        scaled_width = obj.width

        # Initial position: Center of image is at target_right_edge + scaled_width / 2
        # (So, left edge of image is at target_right_edge)
        initial_pos_x = target_center[0] + (scaled_width / 4)
        initial_pos_y = target_center[1]
        initial_pos = np.array([initial_pos_x, initial_pos_y, target_center[2]])

        shift_dist = scaled_width / 2  # Pan half width
        pan_direction = LEFT
    elif image_aspect_ratio < 0.6:  # 长图像 -> 向上平移
        target_width_scaled = target_width * 0.95  # 从0.9提高到0.95
        scale_factor = target_width_scaled / orig_width if orig_width > 0 else 1.0
        obj.scale(scale_factor)
        scaled_height = obj.height

        # Initial position: Center of image is at target_bottom_edge - scaled_height / 2
        # (So, top edge of image is at target_bottom_edge)
        initial_pos_x = target_center[0]
        initial_pos_y = target_center[1] - (scaled_height / 4)
        initial_pos = np.array([initial_pos_x, initial_pos_y, target_center[2]])

        shift_dist = scaled_height / 2  # Pan half height
        pan_direction = UP
    else:
        # 不需要平移
        initial_pos = target_center
        shift_dist = 0
        pan_direction = None

    params["initial_pos"] = initial_pos
    params["shift_distance"] = shift_dist
    params["pan_direction"] = pan_direction
    params["target_rect"] = target_rect

    return params


def _animate_zoom_and_pan(
    scene: "FeynmanScene",
    obj: Mobject,
    narration: Optional[str],
    pan_params: dict[str, Any],
    annotation: Optional[str | list[str]] = None,
) -> Optional[Mobject]:
    """
    缩放和平移动画策略。

    Args:
        scene: Manim 场景
        obj: 要动画的对象
        narration: 旁白文本
        pan_params: 平移动画参数
        annotation: 图片注释文本（可选，支持字符串或列表）

    Returns:
        Optional[Mobject]: 注释文本对象
    """
    text_obj = None

    # 处理可能的None值
    if narration is None:
        narration = ""

    with scene.voiceover(text=narration) as tracker:
        # 获取目标区域和设置初始参数
        target_rect = pan_params["target_rect"]
        target_center = target_rect.get_center()

        # 计算初始缩放比例 - 提高缩放系数
        width_scale_factor = 0.9
        height_scale_factor = 0.9
        initial_scale_factor = min(
            float(target_rect.width) * width_scale_factor / float(obj.width),
            float(target_rect.height) * height_scale_factor / float(obj.height),
        )

        # 第一步：设置图片初始状态 - 随机轨迹斜着进入
        # 随机选择进入方向
        entry_directions = [
            np.array([float(target_rect.width) * 1.2, -float(target_rect.height) * 1.2, 0]),  # 右下角
            np.array([float(target_rect.width) * 1.2, float(target_rect.height) * 1.2, 0]),  # 右上角
            np.array([-float(target_rect.width) * 1.2, -float(target_rect.height) * 1.2, 0]),  # 左下角
            np.array([-float(target_rect.width) * 1.2, float(target_rect.height) * 1.2, 0]),  # 左上角
            np.array([float(target_rect.width) * 1.5, 0, 0]),  # 右侧
            np.array([-float(target_rect.width) * 1.5, 0, 0]),  # 左侧
            np.array([0, float(target_rect.height) * 1.5, 0]),  # 上方
            np.array([0, -float(target_rect.height) * 1.5, 0]),  # 下方
        ]

        # 随机选择进入位置
        entry_offset = random.choice(entry_directions)
        initial_pos = target_center + entry_offset

        # 随机生成旋转角度，增加丰富性
        rotation_angle = random.uniform(-40, 40) * DEGREES  # 简化为Z轴旋转：-40到40度

        # 设置初始状态：小尺寸，随机角度，在屏幕外
        obj.scale(initial_scale_factor).move_to(initial_pos).rotate(rotation_angle)

        # 第二步：随机轨迹斜着入场 - 移动到中心
        # 随机选择轨迹类型：直线、弧形或S型
        trajectory_type = random.choice(["direct", "arc", "s_curve"])

        if trajectory_type == "direct":
            # 直线轨迹：直接移动到中心
            scene.play(
                obj.animate.move_to(target_center),
                FadeIn(obj),
                run_time=0.5,
            )
        elif trajectory_type == "arc":
            # 弧形轨迹：通过中间点移动
            intermediate_pos = target_center + np.array(
                [
                    random.uniform(-float(target_rect.width) * 0.4, float(target_rect.width) * 0.4),  # 中间点随机偏移
                    random.uniform(-float(target_rect.height) * 0.3, float(target_rect.height) * 0.3),
                    0,
                ]
            )

            # 分两段移动：先到中间点，再到中心，创造弧形轨迹
            scene.play(
                obj.animate.move_to(intermediate_pos),  # 移除随机缩放
                FadeIn(obj),
                run_time=0.3,
            )

            scene.play(
                obj.animate.move_to(target_center),  # 移除调整缩放
                run_time=0.3,
            )
        else:  # s_curve
            # S型轨迹：通过两个中间点移动
            mid1_offset = np.array(
                [
                    random.uniform(-float(target_rect.width) * 0.5, float(target_rect.width) * 0.5),
                    random.uniform(-float(target_rect.height) * 0.4, float(target_rect.height) * 0.4),
                    0,
                ]
            )
            mid2_offset = np.array(
                [
                    random.uniform(-float(target_rect.width) * 0.3, float(target_rect.width) * 0.3),
                    random.uniform(-float(target_rect.height) * 0.2, float(target_rect.height) * 0.2),
                    0,
                ]
            )

            intermediate_pos1 = target_center + mid1_offset
            intermediate_pos2 = target_center + mid2_offset

            # 三段移动：创造S形轨迹
            scene.play(
                obj.animate.move_to(intermediate_pos1).rotate(random.uniform(-15, 15) * DEGREES),  # 添加轻微旋转
                FadeIn(obj),
                run_time=0.3,
            )

            scene.play(
                obj.animate.move_to(intermediate_pos2),  # 移除随机缩放
                run_time=0.3,
            )

            scene.play(
                obj.animate.move_to(target_center),  # 移除调整缩放
                run_time=0.3,
            )

        # 第三步：顺正 - 将旋转恢复到0度，消除倾斜变为正面
        scene.play(
            obj.animate.rotate(-rotation_angle),  # 逆向旋转恢复正面
            run_time=0.5,
        )

        # 第四步：智能定位和平移 - 进一步增大图片尺寸
        enlarge_factor = (
            max(target_rect.width / obj.width, target_rect.height / obj.height) * 0.98
        )  # 增加最终放大系数到0.98
        if pan_params["shift_distance"] > 0.01:
            # 先移动到初始位置并放大
            scene.play(
                obj.animate.move_to(pan_params["initial_pos"]).scale(enlarge_factor),
                run_time=1,
            )
            # 然后进行平移
            scene.play(
                obj.animate.shift(pan_params["pan_direction"] * pan_params["shift_distance"]),
                run_time=min(4.0, tracker.duration * 2 / 3),
                rate_func=linear,
            )
        else:
            # 如果不需要平移，直接放大
            scene.play(obj.animate.scale(enlarge_factor), run_time=1)

        # 第五步：根据布局类型决定最终位置和annotation显示方式
        if annotation:
            # 先将图片缩放到合适大小，然后判断布局类型
            temp_scale_factor = min(
                float(target_rect.width) * 0.9 / float(obj.width),
                float(target_rect.height) * 0.9 / float(obj.height),
            )
            # 创建临时对象来判断缩放后的尺寸
            temp_obj = obj.copy()
            temp_obj.scale(temp_scale_factor)

            # 判断布局类型
            layout_type = _determine_layout_type(temp_obj, target_rect)
            logger.info(
                f"布局检测: {temp_obj.width:.1f}x{temp_obj.height:.1f}, 屏幕{target_rect.width:.1f}x{target_rect.height:.1f}, 类型={layout_type}"
            )

            if layout_type == "side_by_side":
                # 左右布局：明确划分左右区域，避免重叠
                left_width = target_rect.width * 0.55  # 左侧图片区域55%
                right_width = target_rect.width * 0.45  # 右侧文字区域45%

                # 左侧图片区域中心位置
                left_center = target_rect.get_left() + np.array([left_width / 2, -target_rect.height * 0.05, 0])

                scene.play(obj.animate.move_to(left_center).scale(0.9 / enlarge_factor), run_time=0.8)

                # 创建右侧annotation，传入右侧区域宽度
                text_obj = _create_side_by_side_annotation(annotation, target_rect, right_area_width=right_width)
                text_obj.set_z_index(1)

                # 右侧区域中心位置
                right_center = target_rect.get_left() + np.array(
                    [left_width + right_width / 2, -target_rect.height * 0.05, 0]
                )
                text_obj.move_to(right_center)

                # 动画显示文字
                scene.play(
                    LaggedStart(
                        *[FadeIn(line, shift=LEFT * 0.3) for line in text_obj],
                        lag_ratio=0.3,
                    ),
                    run_time=1,
                )

                # 应用文本样式动画
                style_objects = _apply_text_style_animations(scene, text_obj, duration=2)
                if style_objects:
                    text_obj.add(*style_objects)

            elif layout_type == "top_bottom":
                # 上下布局：图片移到上方，annotation放下方，尽量填满上方区域
                # 图片往下移动一些，避免挡住标题
                final_image_pos = target_rect.get_center() + np.array([0, target_rect.height * 0.05, 0])

                scene.play(obj.animate.move_to(final_image_pos).scale(0.9 / enlarge_factor), run_time=0.8)

                # 创建下方annotation
                text_obj = _create_top_bottom_annotation(annotation, target_rect)
                text_obj.set_z_index(1)

                # 定位到下方区域（调整下方区域位置，相对图片新位置进行调整）
                bottom_area_center = target_rect.get_center() + np.array([0, -target_rect.height * 0.35, 0])
                text_obj.move_to(bottom_area_center)

                # 动画显示文字
                scene.play(
                    FadeIn(text_obj, shift=UP * 0.3),
                    run_time=0.5,
                )

                # 应用文本样式动画
                style_objects = _apply_text_style_animations(scene, text_obj, duration=2)
                if style_objects:
                    text_obj.add(*style_objects)

            else:
                # 叠加布局：保持原有逻辑
                scene.play(obj.animate.move_to(target_rect.get_center()).scale(1 / enlarge_factor), run_time=0.5)

                text_obj = _create_sequential_text_lines_group(annotation)
                text_obj.set_z_index(1)
                text_obj.move_to(obj.get_center())

                # 根据图片大小缩放文字
                scale_factor_text = (
                    min(float(obj.height) / float(text_obj.height), float(obj.width) / float(text_obj.width), 1.0) * 0.8
                )
                text_obj.scale(scale_factor_text)

                # 使用sequential动画方式显示文字
                scene.play(
                    LaggedStart(
                        *[FadeIn(line, shift=RIGHT * 0.2) for line in text_obj],
                        lag_ratio=0.3,
                    ),
                    run_time=0.5,
                )

                # 应用文本样式动画
                style_objects = _apply_text_style_animations(scene, text_obj, duration=2)
                if style_objects:
                    text_obj.add(*style_objects)

            scene.wait()
        else:
            # 没有annotation的情况，回到中心
            scene.play(obj.animate.move_to(target_rect.get_center()).scale(1 / enlarge_factor), run_time=0.5)
            scene.wait()

    return text_obj


def _animate_annotation_only(
    scene: "FeynmanScene", annotation: str | list[str], target_rect: Rectangle, narration: Optional[str] = None
) -> Optional[Mobject]:
    """
    仅注释显示动画策略。

    Args:
        scene: Manim 场景
        annotation: 注释文本，支持字符串或列表格式
        target_rect: 目标区域的矩形
        narration: 旁白文本（可选）

    Returns:
        创建的注释文本对象
    """
    # 使用sequential text lines group创建文本
    text_obj = _create_sequential_text_lines_group(annotation)
    text_obj.move_to(target_rect.get_center())

    # 缩放以适应目标区域
    width_factor = 0.8
    height_factor = 0.8
    if text_obj.width > float(target_rect.width) * width_factor:
        text_obj.scale_to_fit_width(float(target_rect.width) * width_factor)
    if text_obj.height > float(target_rect.height) * height_factor:
        text_obj.scale_to_fit_height(float(target_rect.height) * height_factor)

    with scene.voiceover(text=narration) as tracker:  # noqa: F841
        # 使用与animate_video类似的sequential动画方式
        scene.play(
            LaggedStart(
                *[FadeIn(line, shift=RIGHT * 0.2) for line in text_obj],
                lag_ratio=0.3,
            )
        )

        # 应用文本样式动画
        style_objects = _apply_text_style_animations(scene, text_obj, duration=2)
        if style_objects:
            text_obj.add(*style_objects)

    return text_obj


def _create_side_by_side_annotation(
    annotation: str | list[str],
    target_rect: Rectangle,
    text_font_size: int = None,
    text_color: ManimColor = YELLOW_B,
    right_area_width: float = None,
) -> Group:
    """
    创建左右布局的annotation，用于放在图片右侧。
    """
    # 使用theme定义的正文字体大小
    if text_font_size is None:
        text_font_size = theme.typography.sizes.body

    # 设置右侧区域宽度，如果没有传入则使用默认值
    if right_area_width is None:
        right_area_width = target_rect.width * 0.42

    # 计算文字的最大允许宽度（留出一些边距）
    max_allowed_text_width = right_area_width * 0.9

    # 处理不同格式的输入
    if isinstance(annotation, list):
        lines = [str(item).strip() for item in annotation if item and str(item).strip()]
    else:
        lines = str(annotation).strip().split("\n")

    line_mobjects_with_background = []
    max_text_width = 0

    created_text_rect_pairs: list[tuple[Mobject, SurroundingRectangle]] = []

    for i, line_content in enumerate(lines):
        # 先创建文字对象，检查是否需要换行
        temp_text = _create_enhanced_text(
            line_content,
            font_size=text_font_size,
            font_color=text_color,
        )

        # 如果文字宽度超过允许宽度，进行智能换行
        if temp_text.width > max_allowed_text_width:
            # 智能分割：优先在标点符号处分割
            words = []
            current_word = ""
            for char in line_content:
                current_word += char
                if char in ["，", "。", "！", "？", "；", "：", " ", "、"]:
                    words.append(current_word)
                    current_word = ""
            if current_word:
                words.append(current_word)

            # 重新组合成适合宽度的行
            current_line = ""
            for word in words:
                test_line = current_line + word
                test_text = _create_enhanced_text(
                    test_line,
                    font_size=text_font_size,
                    font_color=text_color,
                )

                if test_text.width <= max_allowed_text_width:
                    current_line = test_line
                else:
                    # 保存当前行
                    if current_line:
                        text_line = _create_enhanced_text(
                            current_line.strip(),
                            font_size=text_font_size,
                            font_color=text_color,
                        )
                        rect = SurroundingRectangle(
                            text_line,
                            buff=theme.components.list.padding,
                            fill_opacity=0.8,
                            fill_color=BLACK,
                            stroke_width=0,
                            corner_radius=(text_line.height + theme.components.list.padding) * 0.3,
                        )
                        max_text_width = max(max_text_width, float(text_line.width))
                        created_text_rect_pairs.append((text_line, rect))

                    # 开始新行
                    current_line = word

            # 处理最后一行
            if current_line:
                text_line = _create_enhanced_text(
                    current_line.strip(),
                    font_size=text_font_size,
                    font_color=text_color,
                )
                rect = SurroundingRectangle(
                    text_line,
                    buff=theme.components.list.padding,
                    fill_opacity=0.8,
                    fill_color=BLACK,
                    stroke_width=0,
                    corner_radius=(text_line.height + theme.components.list.padding) * 0.3,
                )
                max_text_width = max(max_text_width, float(text_line.width))
                created_text_rect_pairs.append((text_line, rect))
        else:
            # 文字宽度在允许范围内，直接使用
            text_line = temp_text
            rect = SurroundingRectangle(
                text_line,
                buff=theme.components.list.padding,
                fill_opacity=0.8,
                fill_color=BLACK,
                stroke_width=0,
                corner_radius=(text_line.height + theme.components.list.padding) * 0.3,
            )
            max_text_width = max(max_text_width, float(text_line.width))
            created_text_rect_pairs.append((text_line, rect))

    # 调整矩形宽度并组合，确保不超过右侧区域宽度
    final_rect_width = min(max_text_width + 2 * theme.components.list.padding, max_allowed_text_width)
    for i, (text_line, rect) in enumerate(created_text_rect_pairs):
        rect.stretch_to_fit_width(final_rect_width)
        line_group = Group(rect, text_line)
        line_mobjects_with_background.append(line_group)

    text_lines_group = Group(*line_mobjects_with_background)
    text_lines_group.arrange(DOWN, buff=MED_LARGE_BUFF, aligned_edge=LEFT)
    return text_lines_group


def _create_top_bottom_annotation(
    annotation: str | list[str],
    target_rect: Rectangle,
    text_font_size: int = None,
    text_color: ManimColor = YELLOW_B,
) -> Group:
    """
    创建上下布局的annotation，合并成一长行放在图片下方。
    """
    # 使用theme定义的正文字体大小
    if text_font_size is None:
        text_font_size = theme.typography.sizes.body

    # 处理不同格式的输入并合并成一行
    if isinstance(annotation, list):
        combined_text = " • ".join([str(item).strip() for item in annotation if item and str(item).strip()])
    else:
        combined_text = str(annotation).strip().replace("\n", " • ")

    # 智能换行：如果文字太长，进行换行处理
    max_char_per_line = 60  # 每行最大字符数，增加到60个字符
    if len(combined_text) > max_char_per_line:
        # 按分隔符进行换行
        parts = combined_text.split(" • ")
        if len(parts) > 2:  # 如果有多个部分，分成两行
            mid_point = len(parts) // 2
            line1 = " • ".join(parts[:mid_point])
            line2 = " • ".join(parts[mid_point:])
            combined_text = line1 + "\n" + line2

    text_line = _create_enhanced_text(
        combined_text,
        font_size=text_font_size,
        font_color=text_color,
    )

    # 如果文字太宽，缩放以适应屏幕宽度
    if text_line.width > target_rect.width * 0.9:
        text_line.scale_to_fit_width(target_rect.width * 0.9)

    rect = SurroundingRectangle(
        text_line,
        buff=theme.components.list.padding,
        fill_opacity=0.8,
        fill_color=BLACK,
        stroke_width=0,
        corner_radius=(text_line.height + theme.components.list.padding) * 0.3,
    )

    line_group = Group(rect, text_line)
    return Group(line_group)


def _create_sequential_text_lines_group(
    annotation: str | list[str],
    text_font_size: int = 48,
    text_color: ManimColor = YELLOW_B,
) -> Group:
    """
    创建文本行组，每行都有背景矩形，用于在图片上顺序显示注释。

    Args:
        annotation: 注释文本，可以是字符串或字符串列表
        text_font_size: 文字大小
        text_color: 文字颜色

    Returns:
        Group: 包含所有文本行的群组
    """
    # 处理不同格式的输入
    if isinstance(annotation, list):
        lines = [str(item).strip() for item in annotation if item and str(item).strip()]
    else:
        lines = str(annotation).strip().split("\n")
    line_mobjects_with_background = []
    max_text_width = 0

    # First pass: create text and rects, find max_width
    created_text_rect_pairs: list[tuple[Mobject, SurroundingRectangle]] = []  # 修复类型注解

    for i, line_content in enumerate(lines):
        text_line = _create_enhanced_text(
            line_content,
            font_size=text_font_size,
            font_color=text_color,
        )
        # Transparent background for text, actual background is the rectangle
        rect = SurroundingRectangle(
            text_line,
            buff=theme.components.list.padding,
            fill_opacity=0.75,
            fill_color=BLACK,
            stroke_width=0,
            corner_radius=(text_line.height + theme.components.list.padding) * 0.3,
        )
        max_text_width = max(max_text_width, float(text_line.width))
        created_text_rect_pairs.append((text_line, rect))

    # Second pass: adjust rect widths and group
    for i, (text_line, rect) in enumerate(created_text_rect_pairs):
        # Stretch rectangle to max_text_width (plus buff)
        rect.stretch_to_fit_width(max_text_width + 2 * theme.components.list.padding)
        line_group = Group(rect, text_line)  # Rectangle behind text
        line_mobjects_with_background.append(line_group)

    text_lines_group = Group(*line_mobjects_with_background)
    text_lines_group.arrange(DOWN, buff=MED_LARGE_BUFF, aligned_edge=LEFT)  # Increased buff
    return text_lines_group


# ---- 主函数 ----
def animate_image(
    scene: "FeynmanScene",
    title: str,
    image_path: str,
    id: Optional[str] = None,
    narration: Optional[str] = None,
    annotation: Optional[str | list[str]] = None,
    cwd: str = None,
) -> None:
    # 1. 初始化和基本设置
    if not getattr(scene, "scene_method_name", None):
        scene.scene_method_name = "animate_image"
    # 创建唯一的引用 ID
    reference_id = id if id else f"image_{abs(hash(image_path)) % 10000}"

    logger.info(f"显示图片内容: region='full_screen', id='{reference_id}'")

    # Create title object
    initial_title, title_text = create_title(title, scene=scene)

    # 2. 获取目标区域并清除
    target_rect = scene.full_screen_rect
    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

    # annotation 参数现在由 _create_sequential_text_lines_group 函数直接处理

    # 3. 检查图像是否存在
    image_exists = os.path.exists(image_path)
    if not image_exists and cwd is not None:
        # 增加 image_path 的父文件夹前缀
        image_path = os.path.join(cwd, image_path)
        logger.info(f"updated image_path: {image_path}")
    image_exists = os.path.exists(image_path)

    if not image_exists:
        logger.warning(f"图像文件未找到: {image_path}")
        if not annotation:
            logger.warning("没有提供注释文本，跳过显示。")
            return

        # 处理仅有注释的情况
        logger.info("图像不存在但有注释文本，将全屏显示注释。")
        try:
            # 直接添加标题，动画已在 create_title 中处理
            scene.add(title_text)

            # Always animate the annotation content with original effects
            text_obj = _animate_annotation_only(scene, annotation, target_rect, narration)

            # Create display group with title and annotation
            display_group = Group(initial_title, title_text, text_obj)

            scene.current_mobj = display_group
        except Exception as e:
            logger.error(f"处理注释时出错: {e}")
        return

    # 4. 创建图像对象
    try:
        obj = _create_image_mobject(image_path)
    except Exception as e:
        logger.error(f"创建图像对象时出错: {e}")
        if not annotation:
            return

        # 如果图像创建失败但有注释，回退到仅显示注释
        logger.info("图像创建失败但有注释文本，将全屏显示注释。")
        try:
            # 直接添加标题，动画已在 create_title 中处理
            scene.add(title_text)

            # Always animate the annotation content with original effects
            text_obj = _animate_annotation_only(scene, annotation, target_rect, narration)

            # Create display group with title and annotation
            display_group = Group(initial_title, title_text, text_obj)

            scene.current_mobj = display_group
        except Exception as e2:
            logger.error(f"处理注释时出错: {e2}")
        return

    # 5. 计算动画参数
    pan_params = _calculate_pan_animation_params(obj, target_rect)

    # 6. 播放动画
    # 直接添加标题，动画已在 create_title 中处理
    scene.add(title_text)

    # Always animate the image content with original effects
    text_obj = _animate_zoom_and_pan(scene, obj, narration, pan_params, annotation)

    # Create display group with title and content
    # 构建内容群组，包含图片和注释
    content_elements: list[Mobject] = [obj]  # 修复类型注解
    if text_obj:
        content_elements.append(text_obj)

    content_group = Group(*content_elements) if len(content_elements) > 1 else content_elements[0]

    # 创建显示组：initial_title用于转场，title_text用于显示，content为实际内容
    # 转场系统会使用submobjects[0]（initial_title）作为转场对象
    display_group = Group(initial_title, title_text, content_group)

    # 7. Update current_mobject saving - ensure title is first submobject
    scene.current_mobj = display_group
    scene.save_scene_state(content_type="image", mobject_id=reference_id)
    logger.info(f"显示图片完成: image_path={image_path}, id='{reference_id}'")
