"""
Text Style Animator - 样式动画模块

负责对Text对象应用各种动态样式效果。
支持的动画类型：
- 粗体: 文本变红色并加粗
- 斜体: 添加下划线突出显示
- 代码: 转换为Code对象
- 删除线: 添加划线并变灰
- 链接: 变蓝色并添加下划线
- 数学公式: 转换为MathTex对象
"""

from __future__ import annotations

from typing import TYPE_CHECKING

from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from .finder import find_text_positions

try:
    from tools.theme_manager import theme
except ImportError:
    # 测试环境的fallback
    class MockTheme:
        colors = type("Colors", (), {"text": "#FFFFFF"})()

    theme = MockTheme()


def apply_style_animations_to_text(
    scene: FeynmanScene, text_obj: Text, styles: dict[str, list[str]], base_animation_duration: float = 1.0
) -> list[Mobject] | None:
    """
    对Text对象应用所有样式动画

    Args:
        scene: Manim场景对象
        text_obj: Text对象
        styles: 样式信息字典，如 {"bold": ["加粗文本"], "italic": ["斜体文本"]}
        base_animation_duration: 基础动画持续时间

    Returns:
        创建的新Mobject对象列表，如果没有创建新对象则返回None
    """
    if not styles or not text_obj:
        return None

    new_objs = []

    try:
        # 按样式类型分组处理
        for style_type, style_list in styles.items():
            for target_text in style_list:
                original_target_text = target_text
                target_text = target_text.replace(" ", "").replace("\n", "")

                obj = None
                if style_type == "bold":
                    # 粗体效果：使用Transform变粗
                    obj = apply_bold_animation(
                        scene, text_obj, target_text, base_animation_duration, original_target_text
                    )
                elif style_type == "italic":
                    # 斜体效果：使用Indicate
                    obj = apply_italic_animation(scene, text_obj, target_text, base_animation_duration)
                elif style_type == "code":
                    # 代码效果：使用颜色变换
                    obj = apply_code_animation(
                        scene, text_obj, target_text, base_animation_duration, original_target_text
                    )
                elif style_type == "strikethrough":
                    # 删除线效果：使用划线动画
                    obj = apply_strikethrough_animation(scene, text_obj, target_text, base_animation_duration)
                elif style_type == "link":
                    # 链接效果：使用颜色和下划线
                    obj = apply_link_animation(scene, text_obj, target_text, base_animation_duration)
                elif style_type == "math":
                    # 数学公式效果：将文本替换为MathTex
                    obj = apply_math_animation(
                        scene, text_obj, target_text, base_animation_duration, original_target_text
                    )

                if obj:
                    new_objs.append(obj)

    except Exception as e:
        logger.error(f"应用样式动画时出错: {e}")

    return new_objs if new_objs else None


def apply_bold_animation(
    scene: FeynmanScene, text_obj: Text, target_text: str, duration: float, original_target_text: str | None = None
) -> Mobject | None:
    """应用粗体动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 创建粗体版本的文本
                bold_text = Text(
                    original_target_text or target_text,
                    font=text_obj.font,
                    font_size=text_obj.font_size,
                    weight=BOLD,
                    color=RED,  # 粗体用红色突出显示
                ).move_to(char_group.get_center())

                scene.play(Transform(char_group, bold_text), run_time=duration)
                return bold_text
        except Exception as e:
            logger.error(f"应用粗体动画失败: {e}")

    return None


def apply_italic_animation(scene: FeynmanScene, text_obj: Text, target_text: str, duration: float) -> Mobject | None:
    """应用斜体动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 使用Underline动画突出显示斜体
                print(f"add underline for {target_text}")
                underline = Underline(
                    char_group,
                    color=RED,
                    buff=0.05,
                ).set_z_index(2)
                # 确保下划线不透明
                underline.set_stroke(opacity=1.0)
                scene.play(Create(underline), run_time=duration)
                return underline
        except Exception as e:
            logger.error(f"应用斜体动画失败: {e}")

    return None


def apply_code_animation(
    scene: FeynmanScene, text_obj: Text, target_text: str, duration: float, original_target_text: str | None = None
) -> Mobject | None:
    """应用代码样式动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 代码文本变为绿色并添加背景
                code_text = (
                    Code(
                        code_string=original_target_text or target_text,
                        add_line_numbers=False,
                    )
                    .set_width(char_group.get_width() * 0.9)
                    .move_to(char_group.get_center())
                )
                scene.play(Transform(char_group, code_text), run_time=duration)
                return code_text
        except Exception as e:
            logger.error(f"应用代码动画失败: {e}")

    return None


def apply_strikethrough_animation(
    scene: FeynmanScene, text_obj: Text, target_text: str, duration: float
) -> Mobject | None:
    """应用删除线动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 删除线效果：文本变灰并添加划线
                line = (
                    Line(
                        char_group.get_left(),
                        char_group.get_right(),
                        color=RED,  # 使用红色删除线，更明显
                    )
                    .move_to(char_group.get_center())
                    .set_z_index(2)
                )

                scene.play(
                    LaggedStart(
                        char_group.animate.set_color(GRAY),
                        Create(line),
                        lag_ratio=0.5,
                    ),
                    run_time=duration * 0.5,
                )
                return line
        except Exception as e:
            logger.error(f"应用删除线动画失败: {e}")

    return None


def apply_link_animation(scene: FeynmanScene, text_obj: Text, target_text: str, duration: float) -> Mobject | None:
    """应用链接动画效果"""
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                print(f"add link for {target_text}")
                # 链接效果：变蓝色并添加下划线
                url = (
                    Text(target_text, color=BLUE, font="Maple Mono NF CN")
                    .scale_to_fit_width(char_group.width)
                    .move_to(char_group.get_center())
                )
                # 下划线颜色与文本颜色保持一致
                underline = Underline(url, color=BLUE, buff=0.05).set_z_index(2)
                scene.play(
                    LaggedStart(
                        Transform(char_group, url),
                        Create(underline),
                        lag_ratio=0.5,
                    ),
                    run_time=duration,
                )
                return underline
        except Exception as e:
            logger.error(f"应用链接动画失败: {e}")

    return None


def apply_math_animation(
    scene: FeynmanScene, text_obj: Text, target_text: str, duration: float, original_target_text: str | None = None
) -> Mobject | None:
    """应用数学公式动画效果：将文本替换为MathTex"""
    # 使用原始文本进行查找，因为它可能包含空格
    search_text = original_target_text if original_target_text else target_text
    positions = find_text_positions(text_obj, search_text.replace(" ", "").replace("\n", ""))

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                # 创建MathTex对象
                math_tex = MathTex(search_text, color=theme.colors.text)

                # 将MathTex移动到原文本的位置
                math_tex.move_to(char_group.get_center())

                # 调整大小以匹配原文本
                if char_group.height > 0:
                    scale_factor = char_group.height * 0.9 / math_tex.height
                    math_tex.scale(scale_factor)

                # 使用Transform动画将文本替换为数学公式
                scene.play(Transform(char_group, math_tex), run_time=duration * 0.5)

                logger.info(f"成功将文本 '{search_text}' 转换为数学公式")
                return math_tex
        except Exception as e:
            logger.error(f"应用数学公式动画失败: {e}")
            # 如果MathTex创建失败，至少改变颜色来表示这是数学公式
            try:
                char_group = text_obj.chars[char_start:char_end]
                if char_group:
                    scene.play(char_group.animate.set_color("#FFD700"), run_time=duration * 0.3)  # 金色
            except Exception as fallback_e:
                logger.error(f"数学公式fallback动画也失败: {fallback_e}")

    return None


def apply_simple_highlight_animation(
    scene: FeynmanScene, text_obj: Text, target_text: str, duration: float = 0.5, animation_type: str = "indicate"
) -> Mobject | None:
    """
    应用简单的高亮动画效果（不改变文本内容）

    Args:
        scene: Manim场景对象
        text_obj: Text对象
        target_text: 要高亮的文本
        duration: 动画持续时间
        animation_type: 动画类型 ("indicate", "circumscribe")

    Returns:
        创建的动画对象（如果有）
    """
    positions = find_text_positions(text_obj, target_text)

    for char_start, char_end in positions:
        try:
            char_group = text_obj.chars[char_start:char_end]
            if char_group:
                if animation_type == "indicate":
                    scene.play(Indicate(char_group), run_time=duration)
                elif animation_type == "circumscribe":
                    scene.play(Circumscribe(char_group), run_time=duration)
                return char_group
        except Exception as e:
            logger.error(f"应用简单高亮动画失败: {e}")

    return None
