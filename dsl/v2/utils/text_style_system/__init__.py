"""
Text Style System - 通用文本样式动画系统

这个模块提供了一套通用的文本样式处理和动画系统，可以被各种动效函数复用。

主要功能：
1. 解析markdown格式文本，提取样式信息
2. 将样式信息附加到Text对象
3. 查找带有样式信息的Text对象
4. 对Text对象应用动态样式动画
5. 提供统一的高级API接口

使用示例：
    from dsl.v2.utils.text_style_system import create_styled_text, apply_text_animations

    # 创建带样式的文本对象
    text_obj = create_styled_text(
        markdown_text="这是**加粗**和*斜体*的文本",
        font_size=48,
        font_color=WHITE
    )

    # 应用动画效果
    apply_text_animations(scene, text_obj, duration=1.0)
"""

from .animator import apply_style_animations_to_text
from .finder import find_all_styled_text_objects, find_all_text_objects_in_mobject, find_styled_text_in_mobject
from .interface import (
    TextStyleProcessor,
    apply_text_animations,
    create_styled_text,
    extract_text_styles,
    get_clean_text,
    has_styled_text_content,
)
from .parser import extract_style_patterns, parse_text_styles, remove_markdown_formatting, validate_markdown_syntax
from .styler import attach_styles_to_text, create_text_with_styles, get_styles, has_styles

__all__ = [
    # 高级接口
    "create_styled_text",
    "apply_text_animations",
    "TextStyleProcessor",
    "extract_text_styles",
    "get_clean_text",
    "has_styled_text_content",
    # 底层模块
    "parse_text_styles",
    "extract_style_patterns",
    "remove_markdown_formatting",
    "validate_markdown_syntax",
    "attach_styles_to_text",
    "create_text_with_styles",
    "has_styles",
    "get_styles",
    "find_styled_text_in_mobject",
    "find_all_styled_text_objects",
    "find_all_text_objects_in_mobject",
    "apply_style_animations_to_text",
]

__version__ = "1.0.0"
