"""
Text Style System Interface - 统一接口模块

提供简单易用的高级API，封装完整的文本样式处理流程。
这是其他动效函数使用text_style_system的主要入口。
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any

from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from .animator import apply_style_animations_to_text
from .finder import find_all_styled_text_objects, find_styled_text_in_mobject
from .parser import parse_text_styles
from .styler import create_text_with_styles, get_styles, has_styles


def create_styled_text(
    markdown_text: str, font_size: int = 48, font_color: str | ManimColor = WHITE, **text_kwargs
) -> Text:
    """
    从markdown文本创建带样式信息的Text对象

    这是创建带样式文本的主要入口函数。

    Args:
        markdown_text: 包含markdown格式的文本，如 "这是**加粗**和*斜体*的文本"
        font_size: 字体大小
        font_color: 字体颜色
        **text_kwargs: 传递给Text构造函数的其他参数

    Returns:
        带有样式信息的Text对象

    Examples:
        >>> text_obj = create_styled_text("这是**加粗**和`代码`的文本")
        >>> print(has_styled_text_content(text_obj))  # True
    """
    return create_text_with_styles(
        markdown_text=markdown_text, font_size=font_size, font_color=font_color, **text_kwargs
    )


def apply_text_animations(
    scene: FeynmanScene, mobject: Mobject, duration: float = 1.0, auto_find: bool = True
) -> list[Mobject] | None:
    """
    对Mobject中的带样式文本应用动画效果

    这是应用文本动画的主要入口函数。

    Args:
        scene: Manim场景对象
        mobject: 包含Text对象的Mobject（可能是Group、VGroup等）
        duration: 动画持续时间
        auto_find: 是否自动查找带样式的Text对象

    Returns:
        创建的新Mobject对象列表，如果没有创建新对象则返回None

    Examples:
        >>> text_obj = create_styled_text("这是**加粗**文本")
        >>> new_objects = apply_text_animations(scene, text_obj, duration=1.5)
        >>> if new_objects:
        ...     mobject.add(*new_objects)
    """
    if not mobject:
        return None

    new_objects = []

    try:
        if auto_find:
            # 自动查找所有带样式的Text对象
            styled_text_objects = find_all_styled_text_objects(mobject)
        else:
            # 假设传入的就是Text对象
            if isinstance(mobject, Text) and has_styles(mobject):
                styled_text_objects = [mobject]
            else:
                styled_text_objects = []

        # 对每个带样式的Text对象应用动画
        for text_obj in styled_text_objects:
            styles = get_styles(text_obj)
            if styles:
                result = apply_style_animations_to_text(scene, text_obj, styles, duration)
                if result:
                    new_objects.extend(result)

        logger.debug(f"成功应用文本动画，创建了{len(new_objects)}个新对象")

    except Exception as e:
        logger.error(f"应用文本动画时出错: {e}")

    return new_objects if new_objects else None


def has_styled_text_content(mobject: Mobject) -> bool:
    """
    检查Mobject是否包含带样式信息的文本内容

    Args:
        mobject: 要检查的Mobject

    Returns:
        如果包含带样式信息的文本返回True，否则返回False
    """
    return find_styled_text_in_mobject(mobject) is not None


def extract_text_styles(markdown_text: str) -> dict[str, list[str]]:
    """
    从markdown文本中提取样式信息（不创建Text对象）

    Args:
        markdown_text: 包含markdown格式的文本

    Returns:
        样式信息字典

    Examples:
        >>> styles = extract_text_styles("这是**加粗**和*斜体*的文本")
        >>> print(styles)  # {"bold": ["加粗"], "italic": ["斜体"]}
    """
    _, styles = parse_text_styles(markdown_text)
    return styles


def get_clean_text(markdown_text: str) -> str:
    """
    从markdown文本中提取纯文本（移除格式标记）

    Args:
        markdown_text: 包含markdown格式的文本

    Returns:
        移除格式后的纯文本

    Examples:
        >>> clean_text = get_clean_text("这是**加粗**和*斜体*的文本")
        >>> print(clean_text)  # "这是加粗和斜体的文本"
    """
    clean_text, _ = parse_text_styles(markdown_text)
    return clean_text


class TextStyleProcessor:
    """
    文本样式处理器类

    提供面向对象的API来处理文本样式，适合需要批量处理或复杂配置的场景。
    """

    def __init__(
        self,
        default_font_size: int = 48,
        default_font_color: str | ManimColor = WHITE,
        default_animation_duration: float = 1.0,
    ):
        """
        初始化文本样式处理器

        Args:
            default_font_size: 默认字体大小
            default_font_color: 默认字体颜色
            default_animation_duration: 默认动画持续时间
        """
        self.default_font_size = default_font_size
        self.default_font_color = default_font_color
        self.default_animation_duration = default_animation_duration

        logger.debug(f"初始化TextStyleProcessor: font_size={default_font_size}, duration={default_animation_duration}")

    def create_text(
        self,
        markdown_text: str,
        font_size: int | None = None,
        font_color: str | ManimColor | None = None,
        **text_kwargs,
    ) -> Text:
        """
        创建带样式的Text对象

        Args:
            markdown_text: 包含markdown格式的文本
            font_size: 字体大小（如果不提供则使用默认值）
            font_color: 字体颜色（如果不提供则使用默认值）
            **text_kwargs: 传递给Text构造函数的其他参数

        Returns:
            带有样式信息的Text对象
        """
        return create_styled_text(
            markdown_text=markdown_text,
            font_size=font_size or self.default_font_size,
            font_color=font_color or self.default_font_color,
            **text_kwargs,
        )

    def apply_animations(
        self, scene: FeynmanScene, mobject: Mobject, duration: float | None = None, auto_find: bool = True
    ) -> list[Mobject] | None:
        """
        应用文本动画

        Args:
            scene: Manim场景对象
            mobject: 包含Text对象的Mobject
            duration: 动画持续时间（如果不提供则使用默认值）
            auto_find: 是否自动查找带样式的Text对象

        Returns:
            创建的新Mobject对象列表
        """
        return apply_text_animations(
            scene=scene, mobject=mobject, duration=duration or self.default_animation_duration, auto_find=auto_find
        )

    def process_text(
        self,
        markdown_text: str,
        mobject_type: str = "text",
        animation_config: dict[str, Any] | None = None,
        **text_kwargs,
    ) -> dict[str, Any]:
        """
        完整处理文本：解析、创建、配置动画

        Args:
            markdown_text: 包含markdown格式的文本
            mobject_type: Mobject类型标识（用于日志和调试）
            animation_config: 动画配置字典
            **text_kwargs: 传递给Text构造函数的其他参数

        Returns:
            包含处理结果的字典：
            {
                "text_obj": Text对象,
                "clean_text": 纯文本,
                "styles": 样式信息,
                "has_styles": 是否有样式,
                "animation_config": 动画配置
            }
        """
        # 解析文本
        clean_text, styles = parse_text_styles(markdown_text)

        # 创建Text对象
        text_obj = self.create_text(markdown_text, **text_kwargs)

        # 准备动画配置
        final_animation_config = {"duration": self.default_animation_duration, "auto_find": True}
        if animation_config:
            final_animation_config.update(animation_config)

        result = {
            "text_obj": text_obj,
            "clean_text": clean_text,
            "styles": styles,
            "has_styles": bool(styles),
            "animation_config": final_animation_config,
            "mobject_type": mobject_type,
        }

        logger.debug(f"处理{mobject_type}文本完成: '{clean_text[:30]}...', 样式类型: {list(styles.keys())}")

        return result

    def batch_process(self, markdown_texts: list[str], **common_kwargs) -> list[dict[str, Any]]:
        """
        批量处理多个文本

        Args:
            markdown_texts: markdown文本列表
            **common_kwargs: 传递给process_text的公共参数

        Returns:
            处理结果列表
        """
        results = []

        for i, text in enumerate(markdown_texts):
            try:
                result = self.process_text(markdown_text=text, mobject_type=f"batch_text_{i}", **common_kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"批量处理第{i}个文本时出错: {e}")
                # 添加错误结果
                results.append(
                    {"text_obj": None, "clean_text": text, "styles": {}, "has_styles": False, "error": str(e)}
                )

        logger.debug(f"批量处理完成，共处理{len(markdown_texts)}个文本")

        return results
