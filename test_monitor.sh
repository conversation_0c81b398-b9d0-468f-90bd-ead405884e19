#!/bin/bash

# 监控功能测试脚本

echo "🧪 开始测试Feynman视频生成服务监控功能"
echo ""

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试1: 检查服务状态
echo -e "${BLUE}📋 测试1: 检查服务状态${NC}"
if ./monitor_video_service.sh --check; then
    echo -e "${GREEN}✅ 服务状态检查通过${NC}"
else
    echo -e "${RED}❌ 服务状态检查失败${NC}"
fi
echo ""

# 测试2: 检查进程是否存在
echo -e "${BLUE}📋 测试2: 检查进程状态${NC}"
pids=$(pgrep -f "video_generation_service.py")
if [ -n "$pids" ]; then
    echo -e "${GREEN}✅ 找到服务进程: $pids${NC}"
else
    echo -e "${RED}❌ 未找到服务进程${NC}"
fi
echo ""

# 测试3: 检查健康检查端点
echo -e "${BLUE}📋 测试3: 检查健康检查端点${NC}"
response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:8000/health" 2>/dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ 健康检查端点正常 (HTTP $response)${NC}"
else
    echo -e "${RED}❌ 健康检查端点异常 (HTTP $response)${NC}"
fi
echo ""

# 测试4: 检查日志文件
echo -e "${BLUE}📋 测试4: 检查日志文件${NC}"
if [ -f "video_service_monitor.log" ]; then
    echo -e "${GREEN}✅ 监控日志文件存在${NC}"
    echo "最近5行日志:"
    tail -5 video_service_monitor.log
else
    echo -e "${YELLOW}⚠️  监控日志文件不存在 (首次运行时会创建)${NC}"
fi
echo ""

# 测试5: 检查脚本权限
echo -e "${BLUE}📋 测试5: 检查脚本权限${NC}"
if [ -x "monitor_video_service.sh" ]; then
    echo -e "${GREEN}✅ 监控脚本有执行权限${NC}"
else
    echo -e "${RED}❌ 监控脚本缺少执行权限${NC}"
fi

if [ -x "start_video_service.sh" ]; then
    echo -e "${GREEN}✅ 启动脚本有执行权限${NC}"
else
    echo -e "${RED}❌ 启动脚本缺少执行权限${NC}"
fi
echo ""

# 测试6: 检查配置文件
echo -e "${BLUE}📋 测试6: 检查配置文件${NC}"
if [ -f "config/config.yaml" ]; then
    echo -e "${GREEN}✅ 配置文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  配置文件不存在${NC}"
fi
echo ""

# 测试7: 检查必要目录
echo -e "${BLUE}📋 测试7: 检查必要目录${NC}"
directories=("output" "temp_uploads" "logs" "config")
for dir in "${directories[@]}"; do
    if [ -d "$dir" ]; then
        echo -e "${GREEN}✅ 目录 $dir 存在${NC}"
    else
        echo -e "${YELLOW}⚠️  目录 $dir 不存在${NC}"
    fi
done
echo ""

echo -e "${BLUE}🎯 测试完成！${NC}"
echo ""
echo -e "${YELLOW}💡 使用建议:${NC}"
echo "1. 运行 './monitor_video_service.sh' 启动监控模式"
echo "2. 运行 './install_service.sh' 安装系统服务"
echo "3. 查看 'VIDEO_SERVICE_MONITOR_README.md' 了解详细使用方法"
